import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Avatar,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  LinearProgress,
  Divider,
  Badge
} from '@mui/material';
import {
  Add,
  Edit,
  Delete,
  Visibility,
  School,
  Email,
  Phone,
  CalendarToday,
  TrendingUp,
  Assignment,
  CheckCircle,
  AccessTime,
  Person,
  Search,
  FilterList
} from '@mui/icons-material';
import axios from 'axios';
import toast from 'react-hot-toast';

const StudentManagementAdvanced = () => {
  const [students, setStudents] = useState([]);
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState('add');
  const [tabValue, setTabValue] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  const [studentForm, setStudentForm] = useState({
    name: '',
    email: '',
    phone: '',
    studentCode: '',
    isActive: true,
    notes: ''
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const [studentsResponse, coursesResponse] = await Promise.all([
        axios.get('/admin/students'),
        axios.get('/admin/courses')
      ]);
      setStudents(studentsResponse.data);
      setCourses(coursesResponse.data);
    } catch (error) {
      console.error('خطأ في جلب البيانات:', error);
      toast.error('فشل في جلب البيانات');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (type, student = null) => {
    setDialogType(type);
    setSelectedStudent(student);
    if (student && type === 'edit') {
      setStudentForm({
        name: student.name,
        email: student.email || '',
        phone: student.phone || '',
        studentCode: student.studentCode,
        isActive: student.isActive,
        notes: student.notes || ''
      });
    } else {
      setStudentForm({
        name: '',
        email: '',
        phone: '',
        studentCode: '',
        isActive: true,
        notes: ''
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedStudent(null);
    setTabValue(0);
  };

  const handleSaveStudent = async () => {
    try {
      if (dialogType === 'add') {
        const response = await axios.post('/admin/students', studentForm);
        setStudents([...students, response.data]);
        toast.success('تم إضافة الطالب بنجاح');
      } else if (dialogType === 'edit') {
        const response = await axios.put(`/admin/students/${selectedStudent._id}`, studentForm);
        setStudents(students.map(s => s._id === selectedStudent._id ? response.data : s));
        toast.success('تم تحديث الطالب بنجاح');
      }
      handleCloseDialog();
    } catch (error) {
      console.error('خطأ في حفظ الطالب:', error);
      toast.error('فشل في حفظ الطالب');
    }
  };

  const handleDeleteStudent = async (studentId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الطالب؟')) {
      try {
        await axios.delete(`/admin/students/${studentId}`);
        setStudents(students.filter(s => s._id !== studentId));
        toast.success('تم حذف الطالب بنجاح');
      } catch (error) {
        console.error('خطأ في حذف الطالب:', error);
        toast.error('فشل في حذف الطالب');
      }
    }
  };

  const filteredStudents = students.filter(student => {
    const matchesSearch = student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.studentCode.includes(searchTerm);
    const matchesFilter = filterStatus === 'all' || 
                         (filterStatus === 'active' && student.isActive) ||
                         (filterStatus === 'inactive' && !student.isActive);
    return matchesSearch && matchesFilter;
  });

  const StudentCard = ({ student }) => (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ flexGrow: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar sx={{ width: 56, height: 56, mr: 2, bgcolor: 'primary.main' }}>
            {student.name.charAt(0)}
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h6" noWrap>
              {student.name}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              كود الطالب: {student.studentCode}
            </Typography>
            <Chip
              label={student.isActive ? 'نشط' : 'غير نشط'}
              color={student.isActive ? 'success' : 'default'}
              size="small"
              sx={{ mt: 0.5 }}
            />
          </Box>
        </Box>

        <Divider sx={{ my: 2 }} />

        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Email fontSize="small" color="action" sx={{ mr: 1 }} />
              <Typography variant="body2" noWrap>
                {student.email || 'غير محدد'}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Phone fontSize="small" color="action" sx={{ mr: 1 }} />
              <Typography variant="body2">
                {student.phone || 'غير محدد'}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <CalendarToday fontSize="small" color="action" sx={{ mr: 1 }} />
              <Typography variant="body2">
                انضم: {new Date(student.joinDate).toLocaleDateString('ar-SA')}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <School fontSize="small" color="action" sx={{ mr: 1 }} />
              <Typography variant="body2">
                الكورسات: {student.enrolledCourses?.length || 0}
              </Typography>
            </Box>
          </Grid>
        </Grid>

        {student.notes && (
          <Box sx={{ mt: 2 }}>
            <Typography variant="caption" color="text.secondary">
              ملاحظات: {student.notes}
            </Typography>
          </Box>
        )}
      </CardContent>

      <Box sx={{ p: 2, pt: 0 }}>
        <Grid container spacing={1}>
          <Grid item xs={4}>
            <Button
              fullWidth
              size="small"
              startIcon={<Visibility />}
              onClick={() => handleOpenDialog('view', student)}
            >
              عرض
            </Button>
          </Grid>
          <Grid item xs={4}>
            <Button
              fullWidth
              size="small"
              startIcon={<Edit />}
              onClick={() => handleOpenDialog('edit', student)}
            >
              تعديل
            </Button>
          </Grid>
          <Grid item xs={4}>
            <Button
              fullWidth
              size="small"
              color="error"
              startIcon={<Delete />}
              onClick={() => handleDeleteStudent(student._id)}
            >
              حذف
            </Button>
          </Grid>
        </Grid>
      </Box>
    </Card>
  );

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <LinearProgress />
        <Typography sx={{ mt: 2 }}>جاري تحميل الطلاب...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">إدارة الطلاب المتقدمة</Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => handleOpenDialog('add')}
        >
          إضافة طالب جديد
        </Button>
      </Box>

      {/* شريط البحث والفلترة */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="البحث بالاسم أو كود الطالب..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: <Search color="action" sx={{ mr: 1 }} />
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>الحالة</InputLabel>
                <Select
                  value={filterStatus}
                  label="الحالة"
                  onChange={(e) => setFilterStatus(e.target.value)}
                >
                  <MenuItem value="all">جميع الطلاب</MenuItem>
                  <MenuItem value="active">نشط</MenuItem>
                  <MenuItem value="inactive">غير نشط</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <Typography variant="body2" color="text.secondary">
                إجمالي الطلاب: {filteredStudents.length}
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* عرض الطلاب */}
      <Grid container spacing={3}>
        {filteredStudents.map((student) => (
          <Grid item xs={12} sm={6} md={4} lg={3} key={student._id}>
            <StudentCard student={student} />
          </Grid>
        ))}
      </Grid>

      {filteredStudents.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="text.secondary">
            لا توجد نتائج
          </Typography>
          <Typography variant="body2" color="text.secondary">
            جرب تغيير معايير البحث أو الفلترة
          </Typography>
        </Box>
      )}

      {/* Dialog for Add/Edit/View Student */}
      <Dialog 
        open={openDialog} 
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {dialogType === 'add' && 'إضافة طالب جديد'}
          {dialogType === 'edit' && 'تعديل الطالب'}
          {dialogType === 'view' && 'تفاصيل الطالب'}
        </DialogTitle>
        
        <DialogContent>
          {/* محتوى النافذة سيتم إضافته لاحقاً */}
          <Typography>محتوى النافذة سيتم إضافته...</Typography>
        </DialogContent>

        <DialogActions>
          <Button onClick={handleCloseDialog}>إلغاء</Button>
          {dialogType !== 'view' && (
            <Button variant="contained" onClick={handleSaveStudent}>
              {dialogType === 'add' ? 'إضافة' : 'حفظ'}
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StudentManagementAdvanced;
