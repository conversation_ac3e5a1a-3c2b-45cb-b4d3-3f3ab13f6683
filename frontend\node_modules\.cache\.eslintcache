[{"C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\AdminDashboard.js": "3", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\contexts\\AuthContext.js": "4", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\StudentDashboard.js": "5", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\Login.js": "6", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\LoadingSpinner.js": "7", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\CoursePlayer.js": "8", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\DashboardOverview.js": "9", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\StudentsManagement.js": "10", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CategoriesManagement.js": "11", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CertificatesManagement.js": "12", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CoursesManagement.js": "13", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\StudentManagement.js": "14", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CourseManagement.js": "15", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\CourseViewer.js": "16", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\ContactAdmin.js": "17", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CertificateManagement.js": "18", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\VideoManagement.js": "19", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\DashboardAdvanced.js": "20", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CourseManagementAdvanced.js": "21"}, {"size": 233, "mtime": 1751936645905, "results": "22", "hashOfConfig": "23"}, {"size": 4981, "mtime": 1751971810812, "results": "24", "hashOfConfig": "23"}, {"size": 10325, "mtime": 1751972503032, "results": "25", "hashOfConfig": "23"}, {"size": 3749, "mtime": 1751936115808, "results": "26", "hashOfConfig": "23"}, {"size": 16665, "mtime": 1751940953156, "results": "27", "hashOfConfig": "23"}, {"size": 9947, "mtime": 1751971797065, "results": "28", "hashOfConfig": "23"}, {"size": 729, "mtime": 1751936128460, "results": "29", "hashOfConfig": "23"}, {"size": 439, "mtime": 1751936452877, "results": "30", "hashOfConfig": "23"}, {"size": 11530, "mtime": 1751971533357, "results": "31", "hashOfConfig": "23"}, {"size": 15293, "mtime": 1751936691832, "results": "32", "hashOfConfig": "23"}, {"size": 0, "mtime": 1751936741843, "results": "33", "hashOfConfig": "23"}, {"size": 457, "mtime": 1751936425851, "results": "34", "hashOfConfig": "23"}, {"size": 449, "mtime": 1751936412497, "results": "35", "hashOfConfig": "23"}, {"size": 14166, "mtime": 1751971570307, "results": "36", "hashOfConfig": "23"}, {"size": 21013, "mtime": 1751943357364, "results": "37", "hashOfConfig": "23"}, {"size": 8779, "mtime": 1751971634964, "results": "38", "hashOfConfig": "23"}, {"size": 7599, "mtime": 1751941008690, "results": "39", "hashOfConfig": "23"}, {"size": 18601, "mtime": 1751944608945, "results": "40", "hashOfConfig": "23"}, {"size": 12434, "mtime": 1751943839818, "results": "41", "hashOfConfig": "23"}, {"size": 10758, "mtime": 1751972432866, "results": "42", "hashOfConfig": "23"}, {"size": 10486, "mtime": 1751972387169, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "tsclx4", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\AdminDashboard.js", ["107", "108", "109"], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\StudentDashboard.js", ["110", "111"], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\Login.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\CoursePlayer.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\DashboardOverview.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\StudentsManagement.js", ["112", "113", "114", "115", "116"], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CategoriesManagement.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CertificatesManagement.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CoursesManagement.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\StudentManagement.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CourseManagement.js", ["117", "118", "119", "120"], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\CourseViewer.js", [], ["121"], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\ContactAdmin.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CertificateManagement.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\VideoManagement.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\DashboardAdvanced.js", ["122", "123"], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CourseManagementAdvanced.js", ["124", "125", "126", "127", "128", "129", "130", "131", "132", "133", "134", "135", "136", "137", "138", "139", "140", "141", "142", "143", "144", "145", "146", "147"], [], {"ruleId": "148", "severity": 1, "message": "149", "line": 18, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 18, "endColumn": 8}, {"ruleId": "148", "severity": 1, "message": "152", "line": 19, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 19, "endColumn": 7}, {"ruleId": "148", "severity": 1, "message": "153", "line": 20, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 20, "endColumn": 14}, {"ruleId": "148", "severity": 1, "message": "154", "line": 15, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 15, "endColumn": 7}, {"ruleId": "148", "severity": 1, "message": "155", "line": 32, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 32, "endColumn": 13}, {"ruleId": "148", "severity": 1, "message": "156", "line": 35, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 35, "endColumn": 13}, {"ruleId": "148", "severity": 1, "message": "157", "line": 36, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 36, "endColumn": 9}, {"ruleId": "148", "severity": 1, "message": "158", "line": 49, "column": 10, "nodeType": "150", "messageId": "151", "endLine": 49, "endColumn": 17}, {"ruleId": "159", "severity": 1, "message": "160", "line": 67, "column": 6, "nodeType": "161", "endLine": 67, "endColumn": 56, "suggestions": "162"}, {"ruleId": "148", "severity": 1, "message": "163", "line": 180, "column": 9, "nodeType": "150", "messageId": "151", "endLine": 180, "endColumn": 16}, {"ruleId": "148", "severity": 1, "message": "164", "line": 33, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 33, "endColumn": 11}, {"ruleId": "148", "severity": 1, "message": "156", "line": 41, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 41, "endColumn": 13}, {"ruleId": "148", "severity": 1, "message": "165", "line": 42, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 42, "endColumn": 16}, {"ruleId": "148", "severity": 1, "message": "166", "line": 207, "column": 13, "nodeType": "150", "messageId": "151", "endLine": 207, "endColumn": 21}, {"ruleId": "159", "severity": 1, "message": "167", "line": 40, "column": 6, "nodeType": "161", "endLine": 40, "endColumn": 14, "suggestions": "168", "suppressions": "169"}, {"ruleId": "148", "severity": 1, "message": "170", "line": 15, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 15, "endColumn": 8}, {"ruleId": "148", "severity": 1, "message": "171", "line": 23, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 23, "endColumn": 9}, {"ruleId": "148", "severity": 1, "message": "172", "line": 10, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 10, "endColumn": 9}, {"ruleId": "148", "severity": 1, "message": "173", "line": 11, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 11, "endColumn": 13}, {"ruleId": "148", "severity": 1, "message": "174", "line": 16, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 16, "endColumn": 12}, {"ruleId": "148", "severity": 1, "message": "175", "line": 17, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 17, "endColumn": 14}, {"ruleId": "148", "severity": 1, "message": "176", "line": 18, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 18, "endColumn": 13}, {"ruleId": "148", "severity": 1, "message": "177", "line": 19, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 19, "endColumn": 9}, {"ruleId": "148", "severity": 1, "message": "178", "line": 20, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 20, "endColumn": 11}, {"ruleId": "148", "severity": 1, "message": "179", "line": 21, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 21, "endColumn": 7}, {"ruleId": "148", "severity": 1, "message": "180", "line": 22, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 22, "endColumn": 6}, {"ruleId": "148", "severity": 1, "message": "181", "line": 23, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 23, "endColumn": 7}, {"ruleId": "148", "severity": 1, "message": "182", "line": 24, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 24, "endColumn": 11}, {"ruleId": "148", "severity": 1, "message": "183", "line": 25, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 25, "endColumn": 15}, {"ruleId": "148", "severity": 1, "message": "184", "line": 26, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 26, "endColumn": 26}, {"ruleId": "148", "severity": 1, "message": "185", "line": 27, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 27, "endColumn": 12}, {"ruleId": "148", "severity": 1, "message": "186", "line": 28, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 28, "endColumn": 19}, {"ruleId": "148", "severity": 1, "message": "187", "line": 29, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 29, "endColumn": 19}, {"ruleId": "148", "severity": 1, "message": "188", "line": 32, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 32, "endColumn": 10}, {"ruleId": "148", "severity": 1, "message": "189", "line": 39, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 39, "endColumn": 12}, {"ruleId": "148", "severity": 1, "message": "157", "line": 40, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 40, "endColumn": 9}, {"ruleId": "148", "severity": 1, "message": "190", "line": 42, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 42, "endColumn": 7}, {"ruleId": "148", "severity": 1, "message": "191", "line": 43, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 43, "endColumn": 13}, {"ruleId": "148", "severity": 1, "message": "192", "line": 46, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 46, "endColumn": 11}, {"ruleId": "148", "severity": 1, "message": "193", "line": 48, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 48, "endColumn": 14}, {"ruleId": "148", "severity": 1, "message": "194", "line": 59, "column": 10, "nodeType": "150", "messageId": "151", "endLine": 59, "endColumn": 18}, "no-unused-vars", "'Badge' is defined but never used.", "Identifier", "unusedVar", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'Chip' is defined but never used.", "'AccessTime' is defined but never used.", "'Visibility' is defined but never used.", "'School' is defined but never used.", "'loading' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchStudents'. Either include it or remove the dependency array.", "ArrayExpression", ["195"], "'columns' is assigned a value but never used.", "'Checkbox' is defined but never used.", "'VisibilityOff' is defined but never used.", "'response' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCourseProgress'. Either include it or remove the dependency array.", ["196"], ["197"], "'Paper' is defined but never used.", "'Button' is defined but never used.", "'Avatar' is defined but never used.", "'IconButton' is defined but never used.", "'TextField' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "'Tabs' is defined but never used.", "'Tab' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", "'ListItemSecondaryAction' is defined but never used.", "'Accordion' is defined but never used.", "'AccordionSummary' is defined but never used.", "'AccordionDetails' is defined but never used.", "'Divider' is defined but never used.", "'PlayArrow' is defined but never used.", "'Star' is defined but never used.", "'ExpandMore' is defined but never used.", "'Language' is defined but never used.", "'AttachMoney' is defined but never used.", "'tabValue' is assigned a value but never used.", {"desc": "198", "fix": "199"}, {"desc": "200", "fix": "201"}, {"kind": "202", "justification": "203"}, "Update the dependencies array to be: [fetchStudents, pagination.page, pagination.pageSize, searchTerm]", {"range": "204", "text": "205"}, "Update the dependencies array to be: [course, fetchCourseProgress]", {"range": "206", "text": "207"}, "directive", "", [1278, 1328], "[fetchStudents, pagination.page, pagination.pageSize, searchTerm]", [831, 839], "[course, fetchCourseProgress]"]