[{"C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\AdminDashboard.js": "3", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\contexts\\AuthContext.js": "4", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\StudentDashboard.js": "5", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\Login.js": "6", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\LoadingSpinner.js": "7", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\CoursePlayer.js": "8", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\DashboardOverview.js": "9", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\StudentsManagement.js": "10", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CategoriesManagement.js": "11", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CertificatesManagement.js": "12", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CoursesManagement.js": "13", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\StudentManagement.js": "14", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CourseManagement.js": "15", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\CourseViewer.js": "16", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\ContactAdmin.js": "17", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CertificateManagement.js": "18", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\VideoManagement.js": "19"}, {"size": 233, "mtime": 1751936645905, "results": "20", "hashOfConfig": "21"}, {"size": 4981, "mtime": 1751971810812, "results": "22", "hashOfConfig": "21"}, {"size": 9515, "mtime": 1751943409132, "results": "23", "hashOfConfig": "21"}, {"size": 3749, "mtime": 1751936115808, "results": "24", "hashOfConfig": "21"}, {"size": 16665, "mtime": 1751940953156, "results": "25", "hashOfConfig": "21"}, {"size": 9947, "mtime": 1751971797065, "results": "26", "hashOfConfig": "21"}, {"size": 729, "mtime": 1751936128460, "results": "27", "hashOfConfig": "21"}, {"size": 439, "mtime": 1751936452877, "results": "28", "hashOfConfig": "21"}, {"size": 11530, "mtime": 1751971533357, "results": "29", "hashOfConfig": "21"}, {"size": 15293, "mtime": 1751936691832, "results": "30", "hashOfConfig": "21"}, {"size": 0, "mtime": 1751936741843, "results": "31", "hashOfConfig": "21"}, {"size": 457, "mtime": 1751936425851, "results": "32", "hashOfConfig": "21"}, {"size": 449, "mtime": 1751936412497, "results": "33", "hashOfConfig": "21"}, {"size": 14166, "mtime": 1751971570307, "results": "34", "hashOfConfig": "21"}, {"size": 21013, "mtime": 1751943357364, "results": "35", "hashOfConfig": "21"}, {"size": 8779, "mtime": 1751971634964, "results": "36", "hashOfConfig": "21"}, {"size": 7599, "mtime": 1751941008690, "results": "37", "hashOfConfig": "21"}, {"size": 18601, "mtime": 1751944608945, "results": "38", "hashOfConfig": "21"}, {"size": 12434, "mtime": 1751943839818, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "tsclx4", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\AdminDashboard.js", ["97", "98", "99", "100", "101", "102"], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\StudentDashboard.js", ["103", "104"], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\Login.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\CoursePlayer.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\DashboardOverview.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\StudentsManagement.js", ["105", "106", "107", "108", "109"], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CategoriesManagement.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CertificatesManagement.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CoursesManagement.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\StudentManagement.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CourseManagement.js", ["110", "111", "112", "113"], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\CourseViewer.js", [], ["114"], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\ContactAdmin.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\CertificateManagement.js", [], [], "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\src\\components\\admin\\VideoManagement.js", [], [], {"ruleId": "115", "severity": 1, "message": "116", "line": 18, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 18, "endColumn": 8}, {"ruleId": "115", "severity": 1, "message": "119", "line": 19, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 19, "endColumn": 7}, {"ruleId": "115", "severity": 1, "message": "120", "line": 20, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 20, "endColumn": 14}, {"ruleId": "115", "severity": 1, "message": "121", "line": 27, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 27, "endColumn": 9}, {"ruleId": "115", "severity": 1, "message": "122", "line": 35, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 35, "endColumn": 13}, {"ruleId": "115", "severity": 1, "message": "123", "line": 36, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 36, "endColumn": 13}, {"ruleId": "115", "severity": 1, "message": "124", "line": 15, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 15, "endColumn": 7}, {"ruleId": "115", "severity": 1, "message": "125", "line": 32, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 32, "endColumn": 13}, {"ruleId": "115", "severity": 1, "message": "126", "line": 35, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 35, "endColumn": 13}, {"ruleId": "115", "severity": 1, "message": "121", "line": 36, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 36, "endColumn": 9}, {"ruleId": "115", "severity": 1, "message": "127", "line": 49, "column": 10, "nodeType": "117", "messageId": "118", "endLine": 49, "endColumn": 17}, {"ruleId": "128", "severity": 1, "message": "129", "line": 67, "column": 6, "nodeType": "130", "endLine": 67, "endColumn": 56, "suggestions": "131"}, {"ruleId": "115", "severity": 1, "message": "132", "line": 180, "column": 9, "nodeType": "117", "messageId": "118", "endLine": 180, "endColumn": 16}, {"ruleId": "115", "severity": 1, "message": "133", "line": 33, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 33, "endColumn": 11}, {"ruleId": "115", "severity": 1, "message": "126", "line": 41, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 41, "endColumn": 13}, {"ruleId": "115", "severity": 1, "message": "134", "line": 42, "column": 3, "nodeType": "117", "messageId": "118", "endLine": 42, "endColumn": 16}, {"ruleId": "115", "severity": 1, "message": "135", "line": 207, "column": 13, "nodeType": "117", "messageId": "118", "endLine": 207, "endColumn": 21}, {"ruleId": "128", "severity": 1, "message": "136", "line": 40, "column": 6, "nodeType": "130", "endLine": 40, "endColumn": 14, "suggestions": "137", "suppressions": "138"}, "no-unused-vars", "'Badge' is defined but never used.", "Identifier", "unusedVar", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'School' is defined but never used.", "'TrendingUp' is defined but never used.", "'Assignment' is defined but never used.", "'Chip' is defined but never used.", "'AccessTime' is defined but never used.", "'Visibility' is defined but never used.", "'loading' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchStudents'. Either include it or remove the dependency array.", "ArrayExpression", ["139"], "'columns' is assigned a value but never used.", "'Checkbox' is defined but never used.", "'VisibilityOff' is defined but never used.", "'response' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchCourseProgress'. Either include it or remove the dependency array.", ["140"], ["141"], {"desc": "142", "fix": "143"}, {"desc": "144", "fix": "145"}, {"kind": "146", "justification": "147"}, "Update the dependencies array to be: [fetchStudents, pagination.page, pagination.pageSize, searchTerm]", {"range": "148", "text": "149"}, "Update the dependencies array to be: [course, fetchCourseProgress]", {"range": "150", "text": "151"}, "directive", "", [1278, 1328], "[fetchStudents, pagination.page, pagination.pageSize, searchTerm]", [831, 839], "[course, fetchCourseProgress]"]