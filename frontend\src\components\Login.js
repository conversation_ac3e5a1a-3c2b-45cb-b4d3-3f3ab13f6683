import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Tabs,
  Tab,
  Container,
  InputAdornment,
  IconButton,
  Fade,
  Slide,
  CircularProgress
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  AdminPanelSettings,
  School,
  Login as LoginIcon
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';

const Login = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  
  // Admin login form
  const [adminForm, setAdminForm] = useState({
    email: '',
    password: ''
  });
  
  // Student login form
  const [studentForm, setStudentForm] = useState({
    code: ''
  });

  const { loginAdmin, loginStudent } = useAuth();

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleAdminSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    await loginAdmin(adminForm.email, adminForm.password);
    
    setLoading(false);
  };

  const handleStudentSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    await loginStudent(studentForm.code);
    
    setLoading(false);
  };

  const handleAdminChange = (field) => (e) => {
    setAdminForm(prev => ({
      ...prev,
      [field]: e.target.value
    }));
  };

  const handleStudentChange = (e) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 6);
    setStudentForm({ code: value });
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 2
      }}
    >
      <Container maxWidth="sm">
        <Fade in timeout={1000}>
          <Card
            sx={{
              borderRadius: 4,
              boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
              overflow: 'hidden'
            }}
          >
            {/* Header */}
            <Box
              sx={{
                background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                color: 'white',
                textAlign: 'center',
                py: 4,
                px: 3
              }}
            >
              <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
                علاء عبد الحميد
              </Typography>
              <Typography variant="h6" sx={{ opacity: 0.9 }}>
                منصة كورسات التسويق
              </Typography>
            </Box>

            <CardContent sx={{ p: 0 }}>
              {/* Tabs */}
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                variant="fullWidth"
                sx={{
                  borderBottom: 1,
                  borderColor: 'divider',
                  '& .MuiTab-root': {
                    py: 2,
                    fontSize: '1rem',
                    fontWeight: 500
                  }
                }}
              >
                <Tab
                  icon={<AdminPanelSettings />}
                  label="المدير"
                  iconPosition="start"
                />
                <Tab
                  icon={<School />}
                  label="الطالب"
                  iconPosition="start"
                />
              </Tabs>

              <Box sx={{ p: 4 }}>
                {/* Admin Login */}
                {tabValue === 0 && (
                  <Slide direction="right" in={tabValue === 0} timeout={300}>
                    <Box component="form" onSubmit={handleAdminSubmit}>
                      <Typography variant="h6" sx={{ mb: 3, textAlign: 'center' }}>
                        تسجيل دخول المدير
                      </Typography>
                      
                      <TextField
                        fullWidth
                        label="البريد الإلكتروني"
                        type="email"
                        value={adminForm.email}
                        onChange={handleAdminChange('email')}
                        required
                        sx={{ mb: 3 }}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <AdminPanelSettings color="primary" />
                            </InputAdornment>
                          )
                        }}
                      />
                      
                      <TextField
                        fullWidth
                        label="كلمة المرور"
                        type={showPassword ? 'text' : 'password'}
                        value={adminForm.password}
                        onChange={handleAdminChange('password')}
                        required
                        sx={{ mb: 4 }}
                        InputProps={{
                          endAdornment: (
                            <InputAdornment position="end">
                              <IconButton
                                onClick={() => setShowPassword(!showPassword)}
                                edge="end"
                              >
                                {showPassword ? <VisibilityOff /> : <Visibility />}
                              </IconButton>
                            </InputAdornment>
                          )
                        }}
                      />
                      
                      <Button
                        type="submit"
                        fullWidth
                        variant="contained"
                        size="large"
                        disabled={loading}
                        startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <LoginIcon />}
                        sx={{
                          py: 1.5,
                          fontSize: '1.1rem',
                          fontWeight: 600,
                          background: 'linear-gradient(45deg, #2196F3 30%, #21CBF3 90%)',
                          '&:hover': {
                            background: 'linear-gradient(45deg, #1976D2 30%, #0288D1 90%)',
                          }
                        }}
                      >
                        {loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
                      </Button>
                    </Box>
                  </Slide>
                )}

                {/* Student Login */}
                {tabValue === 1 && (
                  <Slide direction="left" in={tabValue === 1} timeout={300}>
                    <Box component="form" onSubmit={handleStudentSubmit}>
                      <Typography variant="h6" sx={{ mb: 3, textAlign: 'center' }}>
                        تسجيل دخول الطالب
                      </Typography>
                      
                      <Typography 
                        variant="body2" 
                        sx={{ 
                          mb: 3, 
                          textAlign: 'center',
                          color: 'text.secondary'
                        }}
                      >
                        أدخل الكود المكون من 6 أرقام الذي حصلت عليه من المدير
                      </Typography>
                      
                      <TextField
                        fullWidth
                        label="كود الطالب (6 أرقام)"
                        value={studentForm.code}
                        onChange={handleStudentChange}
                        required
                        inputProps={{
                          maxLength: 6,
                          style: { 
                            textAlign: 'center', 
                            fontSize: '1.5rem',
                            letterSpacing: '0.5rem'
                          }
                        }}
                        sx={{ 
                          mb: 4,
                          '& .MuiOutlinedInput-root': {
                            '& fieldset': {
                              borderWidth: 2
                            }
                          }
                        }}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <School color="primary" />
                            </InputAdornment>
                          )
                        }}
                      />
                      
                      <Button
                        type="submit"
                        fullWidth
                        variant="contained"
                        size="large"
                        disabled={loading || studentForm.code.length !== 6}
                        startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <LoginIcon />}
                        sx={{
                          py: 1.5,
                          fontSize: '1.1rem',
                          fontWeight: 600,
                          background: 'linear-gradient(45deg, #4CAF50 30%, #8BC34A 90%)',
                          '&:hover': {
                            background: 'linear-gradient(45deg, #388E3C 30%, #689F38 90%)',
                          }
                        }}
                      >
                        {loading ? 'جاري تسجيل الدخول...' : 'دخول'}
                      </Button>
                    </Box>
                  </Slide>
                )}
              </Box>
            </CardContent>
          </Card>
        </Fade>
      </Container>
    </Box>
  );
};

export default Login;
