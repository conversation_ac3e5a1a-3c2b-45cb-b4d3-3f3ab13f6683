{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0643\\u0648\\u0633\\u0627\\u062A\\\\frontend\\\\src\\\\components\\\\CourseViewer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Card, CardContent, Button, List, ListItem, ListItemIcon, ListItemText, LinearProgress, Chip, IconButton, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';\nimport { PlayArrow, CheckCircle, ArrowBack, Star, AccessTime, VideoLibrary } from '@mui/icons-material';\n// import { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CourseViewer = ({\n  course,\n  onBack\n}) => {\n  _s();\n  var _course$videos2;\n  // const { user } = useAuth(); // سيتم استخدامه لاحقاً\n  const [selectedVideo, setSelectedVideo] = useState(null);\n  const [openVideoDialog, setOpenVideoDialog] = useState(false);\n  const [courseProgress, setCourseProgress] = useState(null);\n  useEffect(() => {\n    if (course) {\n      fetchCourseProgress();\n    }\n  }, [course]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const fetchCourseProgress = async () => {\n    try {\n      var _course$videos;\n      // في التطبيق الحقيقي، ستجلب التقدم من الخادم\n      setCourseProgress({\n        completedVideos: course.completedVideos || 0,\n        totalVideos: course.totalVideos || ((_course$videos = course.videos) === null || _course$videos === void 0 ? void 0 : _course$videos.length) || 0,\n        progress: course.progress || 0\n      });\n    } catch (error) {\n      console.error('خطأ في جلب تقدم الكورس:', error);\n    }\n  };\n  const handleVideoClick = video => {\n    setSelectedVideo(video);\n    setOpenVideoDialog(true);\n  };\n  const markVideoAsCompleted = async videoId => {\n    try {\n      // في التطبيق الحقيقي، ستحدث الخادم\n      console.log('تم إكمال الفيديو:', videoId);\n\n      // تحديث محلي\n      const updatedProgress = {\n        ...courseProgress,\n        completedVideos: courseProgress.completedVideos + 1\n      };\n      updatedProgress.progress = Math.round(updatedProgress.completedVideos / updatedProgress.totalVideos * 100);\n      setCourseProgress(updatedProgress);\n      setOpenVideoDialog(false);\n    } catch (error) {\n      console.error('خطأ في تحديث التقدم:', error);\n    }\n  };\n  if (!course) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 3,\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        children: \"\\u0644\\u0645 \\u064A\\u062A\\u0645 \\u0627\\u0644\\u0639\\u062B\\u0648\\u0631 \\u0639\\u0644\\u0649 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      p: 3\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(IconButton, {\n        onClick: onBack,\n        sx: {\n          mr: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(ArrowBack, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 'bold'\n        },\n        children: course.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'flex-start',\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              flex: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body1\",\n              sx: {\n                mb: 2\n              },\n              children: course.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                gap: 2,\n                mb: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Chip, {\n                icon: /*#__PURE__*/_jsxDEV(Star, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 25\n                }, this),\n                label: `${course.rating || 4.5} ⭐`,\n                size: \"small\",\n                color: \"warning\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                icon: /*#__PURE__*/_jsxDEV(AccessTime, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 25\n                }, this),\n                label: course.duration || '8 ساعات',\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                icon: /*#__PURE__*/_jsxDEV(VideoLibrary, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 121,\n                  columnNumber: 25\n                }, this),\n                label: `${course.totalVideos || ((_course$videos2 = course.videos) === null || _course$videos2 === void 0 ? void 0 : _course$videos2.length) || 0} فيديو`,\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 120,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: course.level || 'مبتدئ',\n                size: \"small\",\n                color: course.level === 'متقدم' ? 'error' : course.level === 'متوسط' ? 'warning' : 'success'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle2\",\n              color: \"textSecondary\",\n              children: [\"\\u0627\\u0644\\u0645\\u062F\\u0631\\u0628: \", course.instructor || 'علاء عبد الحميد']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), courseProgress && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              justifyContent: 'space-between',\n              mb: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [\"\\u0627\\u0644\\u062A\\u0642\\u062F\\u0645: \", courseProgress.completedVideos, \"/\", courseProgress.totalVideos]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: [courseProgress.progress, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n            variant: \"determinate\",\n            value: courseProgress.progress,\n            sx: {\n              height: 8,\n              borderRadius: 4\n            },\n            color: courseProgress.progress === 100 ? 'success' : 'primary'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 2\n          },\n          children: \"\\u0645\\u062D\\u062A\\u0648\\u0649 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          children: course.videos && course.videos.length > 0 ? course.videos.map((video, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n            sx: {\n              border: '1px solid #e0e0e0',\n              borderRadius: 2,\n              mb: 1,\n              '&:hover': {\n                bgcolor: '#f5f5f5'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: video.isCompleted ? /*#__PURE__*/_jsxDEV(CheckCircle, {\n                sx: {\n                  color: '#4caf50'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(PlayArrow, {\n                sx: {\n                  color: '#2196f3'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"subtitle1\",\n                  children: video.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: video.duration\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 23\n              }, this),\n              secondary: `الدرس ${index + 1}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: video.isCompleted ? \"outlined\" : \"contained\",\n              size: \"small\",\n              startIcon: video.isCompleted ? /*#__PURE__*/_jsxDEV(CheckCircle, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 52\n              }, this) : /*#__PURE__*/_jsxDEV(PlayArrow, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 70\n              }, this),\n              onClick: () => handleVideoClick(video),\n              sx: {\n                ml: 2\n              },\n              children: video.isCompleted ? 'مكتمل' : 'مشاهدة'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 19\n            }, this)]\n          }, video.id || index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 17\n          }, this)) : /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            sx: {\n              textAlign: 'center',\n              py: 4\n            },\n            children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A \\u0645\\u062A\\u0627\\u062D\\u0629 \\u062D\\u0627\\u0644\\u064A\\u0627\\u064B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openVideoDialog,\n      onClose: () => setOpenVideoDialog(false),\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: selectedVideo === null || selectedVideo === void 0 ? void 0 : selectedVideo.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            height: 300,\n            bgcolor: '#000',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            borderRadius: 2,\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              color: 'white'\n            },\n            children: \"\\uD83C\\uDFA5 \\u0645\\u0634\\u063A\\u0644 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body1\",\n          children: [\"\\u0645\\u062F\\u0629 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648: \", selectedVideo === null || selectedVideo === void 0 ? void 0 : selectedVideo.duration]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"textSecondary\",\n          sx: {\n            mt: 1\n          },\n          children: \"\\u0641\\u064A \\u0627\\u0644\\u062A\\u0637\\u0628\\u064A\\u0642 \\u0627\\u0644\\u062D\\u0642\\u064A\\u0642\\u064A\\u060C \\u0633\\u064A\\u062A\\u0645 \\u0639\\u0631\\u0636 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648 \\u0647\\u0646\\u0627\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setOpenVideoDialog(false),\n          children: \"\\u0625\\u063A\\u0644\\u0627\\u0642\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this), selectedVideo && !selectedVideo.isCompleted && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          onClick: () => markVideoAsCompleted(selectedVideo.id),\n          children: \"\\u062A\\u0645 \\u0627\\u0644\\u0627\\u0646\\u062A\\u0647\\u0627\\u0621\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 5\n  }, this);\n};\n_s(CourseViewer, \"ApA4SmCtb+3OolePC0I4B+2sSBQ=\");\n_c = CourseViewer;\nexport default CourseViewer;\nvar _c;\n$RefreshReg$(_c, \"CourseViewer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "List", "ListItem", "ListItemIcon", "ListItemText", "LinearProgress", "Chip", "IconButton", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "PlayArrow", "CheckCircle", "ArrowBack", "Star", "AccessTime", "VideoLibrary", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>iewer", "course", "onBack", "_s", "_course$videos2", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedVideo", "openVideoDialog", "setOpenVideoDialog", "courseProgress", "setCourseProgress", "fetchCourseProgress", "_course$videos", "completedVideos", "totalVideos", "videos", "length", "progress", "error", "console", "handleVideoClick", "video", "markVideoAsCompleted", "videoId", "log", "updatedProgress", "Math", "round", "sx", "p", "textAlign", "children", "variant", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "alignItems", "mb", "onClick", "mr", "fontWeight", "title", "justifyContent", "flex", "description", "gap", "icon", "label", "rating", "size", "color", "duration", "level", "instructor", "mt", "value", "height", "borderRadius", "map", "index", "border", "bgcolor", "isCompleted", "primary", "secondary", "startIcon", "ml", "id", "py", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/كوسات/frontend/src/components/CourseViewer.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  Button,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  LinearProgress,\n  Chip,\n  IconButton,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions\n} from '@mui/material';\nimport {\n  PlayArrow,\n  CheckCircle,\n  ArrowBack,\n  Star,\n  AccessTime,\n  VideoLibrary\n} from '@mui/icons-material';\n// import { useAuth } from '../contexts/AuthContext';\n\nconst CourseViewer = ({ course, onBack }) => {\n  // const { user } = useAuth(); // سيتم استخدامه لاحقاً\n  const [selectedVideo, setSelectedVideo] = useState(null);\n  const [openVideoDialog, setOpenVideoDialog] = useState(false);\n  const [courseProgress, setCourseProgress] = useState(null);\n\n  useEffect(() => {\n    if (course) {\n      fetchCourseProgress();\n    }\n  }, [course]); // eslint-disable-line react-hooks/exhaustive-deps\n\n  const fetchCourseProgress = async () => {\n    try {\n      // في التطبيق الحقيقي، ستجلب التقدم من الخادم\n      setCourseProgress({\n        completedVideos: course.completedVideos || 0,\n        totalVideos: course.totalVideos || course.videos?.length || 0,\n        progress: course.progress || 0\n      });\n    } catch (error) {\n      console.error('خطأ في جلب تقدم الكورس:', error);\n    }\n  };\n\n  const handleVideoClick = (video) => {\n    setSelectedVideo(video);\n    setOpenVideoDialog(true);\n  };\n\n  const markVideoAsCompleted = async (videoId) => {\n    try {\n      // في التطبيق الحقيقي، ستحدث الخادم\n      console.log('تم إكمال الفيديو:', videoId);\n      \n      // تحديث محلي\n      const updatedProgress = {\n        ...courseProgress,\n        completedVideos: courseProgress.completedVideos + 1\n      };\n      updatedProgress.progress = Math.round((updatedProgress.completedVideos / updatedProgress.totalVideos) * 100);\n      setCourseProgress(updatedProgress);\n      \n      setOpenVideoDialog(false);\n    } catch (error) {\n      console.error('خطأ في تحديث التقدم:', error);\n    }\n  };\n\n  if (!course) {\n    return (\n      <Box sx={{ p: 3, textAlign: 'center' }}>\n        <Typography variant=\"h6\">لم يتم العثور على الكورس</Typography>\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{ p: 3 }}>\n      {/* Header */}\n      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n        <IconButton onClick={onBack} sx={{ mr: 2 }}>\n          <ArrowBack />\n        </IconButton>\n        <Typography variant=\"h4\" sx={{ fontWeight: 'bold' }}>\n          {course.title}\n        </Typography>\n      </Box>\n\n      {/* Course Info */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>\n            <Box sx={{ flex: 1 }}>\n              <Typography variant=\"body1\" sx={{ mb: 2 }}>\n                {course.description}\n              </Typography>\n              \n              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>\n                <Chip \n                  icon={<Star />} \n                  label={`${course.rating || 4.5} ⭐`} \n                  size=\"small\" \n                  color=\"warning\"\n                />\n                <Chip \n                  icon={<AccessTime />} \n                  label={course.duration || '8 ساعات'} \n                  size=\"small\" \n                />\n                <Chip \n                  icon={<VideoLibrary />} \n                  label={`${course.totalVideos || course.videos?.length || 0} فيديو`} \n                  size=\"small\" \n                />\n                <Chip \n                  label={course.level || 'مبتدئ'} \n                  size=\"small\"\n                  color={course.level === 'متقدم' ? 'error' : course.level === 'متوسط' ? 'warning' : 'success'}\n                />\n              </Box>\n\n              <Typography variant=\"subtitle2\" color=\"textSecondary\">\n                المدرب: {course.instructor || 'علاء عبد الحميد'}\n              </Typography>\n            </Box>\n          </Box>\n\n          {/* Progress */}\n          {courseProgress && (\n            <Box sx={{ mt: 3 }}>\n              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n                <Typography variant=\"body2\">\n                  التقدم: {courseProgress.completedVideos}/{courseProgress.totalVideos}\n                </Typography>\n                <Typography variant=\"body2\">\n                  {courseProgress.progress}%\n                </Typography>\n              </Box>\n              <LinearProgress\n                variant=\"determinate\"\n                value={courseProgress.progress}\n                sx={{ height: 8, borderRadius: 4 }}\n                color={courseProgress.progress === 100 ? 'success' : 'primary'}\n              />\n            </Box>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Videos List */}\n      <Card>\n        <CardContent>\n          <Typography variant=\"h6\" sx={{ mb: 2 }}>\n            محتوى الكورس\n          </Typography>\n          \n          <List>\n            {course.videos && course.videos.length > 0 ? (\n              course.videos.map((video, index) => (\n                <ListItem \n                  key={video.id || index}\n                  sx={{ \n                    border: '1px solid #e0e0e0', \n                    borderRadius: 2, \n                    mb: 1,\n                    '&:hover': { bgcolor: '#f5f5f5' }\n                  }}\n                >\n                  <ListItemIcon>\n                    {video.isCompleted ? (\n                      <CheckCircle sx={{ color: '#4caf50' }} />\n                    ) : (\n                      <PlayArrow sx={{ color: '#2196f3' }} />\n                    )}\n                  </ListItemIcon>\n                  \n                  <ListItemText\n                    primary={\n                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                        <Typography variant=\"subtitle1\">\n                          {video.title}\n                        </Typography>\n                        <Typography variant=\"body2\" color=\"textSecondary\">\n                          {video.duration}\n                        </Typography>\n                      </Box>\n                    }\n                    secondary={`الدرس ${index + 1}`}\n                  />\n                  \n                  <Button\n                    variant={video.isCompleted ? \"outlined\" : \"contained\"}\n                    size=\"small\"\n                    startIcon={video.isCompleted ? <CheckCircle /> : <PlayArrow />}\n                    onClick={() => handleVideoClick(video)}\n                    sx={{ ml: 2 }}\n                  >\n                    {video.isCompleted ? 'مكتمل' : 'مشاهدة'}\n                  </Button>\n                </ListItem>\n              ))\n            ) : (\n              <Typography variant=\"body2\" color=\"textSecondary\" sx={{ textAlign: 'center', py: 4 }}>\n                لا توجد فيديوهات متاحة حالياً\n              </Typography>\n            )}\n          </List>\n        </CardContent>\n      </Card>\n\n      {/* Video Dialog */}\n      <Dialog \n        open={openVideoDialog} \n        onClose={() => setOpenVideoDialog(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          {selectedVideo?.title}\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ \n            height: 300, \n            bgcolor: '#000', \n            display: 'flex', \n            alignItems: 'center', \n            justifyContent: 'center',\n            borderRadius: 2,\n            mb: 2\n          }}>\n            <Typography variant=\"h6\" sx={{ color: 'white' }}>\n              🎥 مشغل الفيديو\n            </Typography>\n          </Box>\n          \n          <Typography variant=\"body1\">\n            مدة الفيديو: {selectedVideo?.duration}\n          </Typography>\n          \n          <Typography variant=\"body2\" color=\"textSecondary\" sx={{ mt: 1 }}>\n            في التطبيق الحقيقي، سيتم عرض الفيديو هنا\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setOpenVideoDialog(false)}>\n            إغلاق\n          </Button>\n          {selectedVideo && !selectedVideo.isCompleted && (\n            <Button \n              variant=\"contained\" \n              onClick={() => markVideoAsCompleted(selectedVideo.id)}\n            >\n              تم الانتهاء\n            </Button>\n          )}\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default CourseViewer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,QACR,eAAe;AACtB,SACEC,SAAS,EACTC,WAAW,EACXC,SAAS,EACTC,IAAI,EACJC,UAAU,EACVC,YAAY,QACP,qBAAqB;AAC5B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,YAAY,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,eAAA;EAC3C;EACA,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACiC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACmC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAE1DC,SAAS,CAAC,MAAM;IACd,IAAI0B,MAAM,EAAE;MACVU,mBAAmB,CAAC,CAAC;IACvB;EACF,CAAC,EAAE,CAACV,MAAM,CAAC,CAAC,CAAC,CAAC;;EAEd,MAAMU,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MAAA,IAAAC,cAAA;MACF;MACAF,iBAAiB,CAAC;QAChBG,eAAe,EAAEZ,MAAM,CAACY,eAAe,IAAI,CAAC;QAC5CC,WAAW,EAAEb,MAAM,CAACa,WAAW,MAAAF,cAAA,GAAIX,MAAM,CAACc,MAAM,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,MAAM,KAAI,CAAC;QAC7DC,QAAQ,EAAEhB,MAAM,CAACgB,QAAQ,IAAI;MAC/B,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IACjD;EACF,CAAC;EAED,MAAME,gBAAgB,GAAIC,KAAK,IAAK;IAClCf,gBAAgB,CAACe,KAAK,CAAC;IACvBb,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMc,oBAAoB,GAAG,MAAOC,OAAO,IAAK;IAC9C,IAAI;MACF;MACAJ,OAAO,CAACK,GAAG,CAAC,mBAAmB,EAAED,OAAO,CAAC;;MAEzC;MACA,MAAME,eAAe,GAAG;QACtB,GAAGhB,cAAc;QACjBI,eAAe,EAAEJ,cAAc,CAACI,eAAe,GAAG;MACpD,CAAC;MACDY,eAAe,CAACR,QAAQ,GAAGS,IAAI,CAACC,KAAK,CAAEF,eAAe,CAACZ,eAAe,GAAGY,eAAe,CAACX,WAAW,GAAI,GAAG,CAAC;MAC5GJ,iBAAiB,CAACe,eAAe,CAAC;MAElCjB,kBAAkB,CAAC,KAAK,CAAC;IAC3B,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC;EAED,IAAI,CAACjB,MAAM,EAAE;IACX,oBACEF,OAAA,CAACvB,GAAG;MAACoD,EAAE,EAAE;QAAEC,CAAC,EAAE,CAAC;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,eACrChC,OAAA,CAACtB,UAAU;QAACuD,OAAO,EAAC,IAAI;QAAAD,QAAA,EAAC;MAAwB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC;EAEV;EAEA,oBACErC,OAAA,CAACvB,GAAG;IAACoD,EAAE,EAAE;MAAEC,CAAC,EAAE;IAAE,CAAE;IAAAE,QAAA,gBAEhBhC,OAAA,CAACvB,GAAG;MAACoD,EAAE,EAAE;QAAES,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,gBACxDhC,OAAA,CAACZ,UAAU;QAACqD,OAAO,EAAEtC,MAAO;QAAC0B,EAAE,EAAE;UAAEa,EAAE,EAAE;QAAE,CAAE;QAAAV,QAAA,eACzChC,OAAA,CAACL,SAAS;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACbrC,OAAA,CAACtB,UAAU;QAACuD,OAAO,EAAC,IAAI;QAACJ,EAAE,EAAE;UAAEc,UAAU,EAAE;QAAO,CAAE;QAAAX,QAAA,EACjD9B,MAAM,CAAC0C;MAAK;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGNrC,OAAA,CAACrB,IAAI;MAACkD,EAAE,EAAE;QAAEW,EAAE,EAAE;MAAE,CAAE;MAAAR,QAAA,eAClBhC,OAAA,CAACpB,WAAW;QAAAoD,QAAA,gBACVhC,OAAA,CAACvB,GAAG;UAACoD,EAAE,EAAE;YAAES,OAAO,EAAE,MAAM;YAAEO,cAAc,EAAE,eAAe;YAAEN,UAAU,EAAE,YAAY;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,eAC7FhC,OAAA,CAACvB,GAAG;YAACoD,EAAE,EAAE;cAAEiB,IAAI,EAAE;YAAE,CAAE;YAAAd,QAAA,gBACnBhC,OAAA,CAACtB,UAAU;cAACuD,OAAO,EAAC,OAAO;cAACJ,EAAE,EAAE;gBAAEW,EAAE,EAAE;cAAE,CAAE;cAAAR,QAAA,EACvC9B,MAAM,CAAC6C;YAAW;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEbrC,OAAA,CAACvB,GAAG;cAACoD,EAAE,EAAE;gBAAES,OAAO,EAAE,MAAM;gBAAEU,GAAG,EAAE,CAAC;gBAAER,EAAE,EAAE;cAAE,CAAE;cAAAR,QAAA,gBAC1ChC,OAAA,CAACb,IAAI;gBACH8D,IAAI,eAAEjD,OAAA,CAACJ,IAAI;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACfa,KAAK,EAAE,GAAGhD,MAAM,CAACiD,MAAM,IAAI,GAAG,IAAK;gBACnCC,IAAI,EAAC,OAAO;gBACZC,KAAK,EAAC;cAAS;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,eACFrC,OAAA,CAACb,IAAI;gBACH8D,IAAI,eAAEjD,OAAA,CAACH,UAAU;kBAAAqC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACrBa,KAAK,EAAEhD,MAAM,CAACoD,QAAQ,IAAI,SAAU;gBACpCF,IAAI,EAAC;cAAO;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACFrC,OAAA,CAACb,IAAI;gBACH8D,IAAI,eAAEjD,OAAA,CAACF,YAAY;kBAAAoC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvBa,KAAK,EAAE,GAAGhD,MAAM,CAACa,WAAW,MAAAV,eAAA,GAAIH,MAAM,CAACc,MAAM,cAAAX,eAAA,uBAAbA,eAAA,CAAeY,MAAM,KAAI,CAAC,QAAS;gBACnEmC,IAAI,EAAC;cAAO;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb,CAAC,eACFrC,OAAA,CAACb,IAAI;gBACH+D,KAAK,EAAEhD,MAAM,CAACqD,KAAK,IAAI,OAAQ;gBAC/BH,IAAI,EAAC,OAAO;gBACZC,KAAK,EAAEnD,MAAM,CAACqD,KAAK,KAAK,OAAO,GAAG,OAAO,GAAGrD,MAAM,CAACqD,KAAK,KAAK,OAAO,GAAG,SAAS,GAAG;cAAU;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENrC,OAAA,CAACtB,UAAU;cAACuD,OAAO,EAAC,WAAW;cAACoB,KAAK,EAAC,eAAe;cAAArB,QAAA,GAAC,wCAC5C,EAAC9B,MAAM,CAACsD,UAAU,IAAI,iBAAiB;YAAA;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGL3B,cAAc,iBACbV,OAAA,CAACvB,GAAG;UAACoD,EAAE,EAAE;YAAE4B,EAAE,EAAE;UAAE,CAAE;UAAAzB,QAAA,gBACjBhC,OAAA,CAACvB,GAAG;YAACoD,EAAE,EAAE;cAAES,OAAO,EAAE,MAAM;cAAEO,cAAc,EAAE,eAAe;cAAEL,EAAE,EAAE;YAAE,CAAE;YAAAR,QAAA,gBACnEhC,OAAA,CAACtB,UAAU;cAACuD,OAAO,EAAC,OAAO;cAAAD,QAAA,GAAC,wCAClB,EAACtB,cAAc,CAACI,eAAe,EAAC,GAAC,EAACJ,cAAc,CAACK,WAAW;YAAA;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACbrC,OAAA,CAACtB,UAAU;cAACuD,OAAO,EAAC,OAAO;cAAAD,QAAA,GACxBtB,cAAc,CAACQ,QAAQ,EAAC,GAC3B;YAAA;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNrC,OAAA,CAACd,cAAc;YACb+C,OAAO,EAAC,aAAa;YACrByB,KAAK,EAAEhD,cAAc,CAACQ,QAAS;YAC/BW,EAAE,EAAE;cAAE8B,MAAM,EAAE,CAAC;cAAEC,YAAY,EAAE;YAAE,CAAE;YACnCP,KAAK,EAAE3C,cAAc,CAACQ,QAAQ,KAAK,GAAG,GAAG,SAAS,GAAG;UAAU;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPrC,OAAA,CAACrB,IAAI;MAAAqD,QAAA,eACHhC,OAAA,CAACpB,WAAW;QAAAoD,QAAA,gBACVhC,OAAA,CAACtB,UAAU;UAACuD,OAAO,EAAC,IAAI;UAACJ,EAAE,EAAE;YAAEW,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,EAAC;QAExC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbrC,OAAA,CAAClB,IAAI;UAAAkD,QAAA,EACF9B,MAAM,CAACc,MAAM,IAAId,MAAM,CAACc,MAAM,CAACC,MAAM,GAAG,CAAC,GACxCf,MAAM,CAACc,MAAM,CAAC6C,GAAG,CAAC,CAACvC,KAAK,EAAEwC,KAAK,kBAC7B9D,OAAA,CAACjB,QAAQ;YAEP8C,EAAE,EAAE;cACFkC,MAAM,EAAE,mBAAmB;cAC3BH,YAAY,EAAE,CAAC;cACfpB,EAAE,EAAE,CAAC;cACL,SAAS,EAAE;gBAAEwB,OAAO,EAAE;cAAU;YAClC,CAAE;YAAAhC,QAAA,gBAEFhC,OAAA,CAAChB,YAAY;cAAAgD,QAAA,EACVV,KAAK,CAAC2C,WAAW,gBAChBjE,OAAA,CAACN,WAAW;gBAACmC,EAAE,EAAE;kBAAEwB,KAAK,EAAE;gBAAU;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEzCrC,OAAA,CAACP,SAAS;gBAACoC,EAAE,EAAE;kBAAEwB,KAAK,EAAE;gBAAU;cAAE;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YACvC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACW,CAAC,eAEfrC,OAAA,CAACf,YAAY;cACXiF,OAAO,eACLlE,OAAA,CAACvB,GAAG;gBAACoD,EAAE,EAAE;kBAAES,OAAO,EAAE,MAAM;kBAAEO,cAAc,EAAE,eAAe;kBAAEN,UAAU,EAAE;gBAAS,CAAE;gBAAAP,QAAA,gBAClFhC,OAAA,CAACtB,UAAU;kBAACuD,OAAO,EAAC,WAAW;kBAAAD,QAAA,EAC5BV,KAAK,CAACsB;gBAAK;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACbrC,OAAA,CAACtB,UAAU;kBAACuD,OAAO,EAAC,OAAO;kBAACoB,KAAK,EAAC,eAAe;kBAAArB,QAAA,EAC9CV,KAAK,CAACgC;gBAAQ;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CACN;cACD8B,SAAS,EAAE,SAASL,KAAK,GAAG,CAAC;YAAG;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eAEFrC,OAAA,CAACnB,MAAM;cACLoD,OAAO,EAAEX,KAAK,CAAC2C,WAAW,GAAG,UAAU,GAAG,WAAY;cACtDb,IAAI,EAAC,OAAO;cACZgB,SAAS,EAAE9C,KAAK,CAAC2C,WAAW,gBAAGjE,OAAA,CAACN,WAAW;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAAGrC,OAAA,CAACP,SAAS;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC/DI,OAAO,EAAEA,CAAA,KAAMpB,gBAAgB,CAACC,KAAK,CAAE;cACvCO,EAAE,EAAE;gBAAEwC,EAAE,EAAE;cAAE,CAAE;cAAArC,QAAA,EAEbV,KAAK,CAAC2C,WAAW,GAAG,OAAO,GAAG;YAAQ;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA,GAtCJf,KAAK,CAACgD,EAAE,IAAIR,KAAK;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAuCd,CACX,CAAC,gBAEFrC,OAAA,CAACtB,UAAU;YAACuD,OAAO,EAAC,OAAO;YAACoB,KAAK,EAAC,eAAe;YAACxB,EAAE,EAAE;cAAEE,SAAS,EAAE,QAAQ;cAAEwC,EAAE,EAAE;YAAE,CAAE;YAAAvC,QAAA,EAAC;UAEtF;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QACb;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPrC,OAAA,CAACX,MAAM;MACLmF,IAAI,EAAEhE,eAAgB;MACtBiE,OAAO,EAAEA,CAAA,KAAMhE,kBAAkB,CAAC,KAAK,CAAE;MACzCiE,QAAQ,EAAC,IAAI;MACbC,SAAS;MAAA3C,QAAA,gBAEThC,OAAA,CAACV,WAAW;QAAA0C,QAAA,EACT1B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEsC;MAAK;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACdrC,OAAA,CAACT,aAAa;QAAAyC,QAAA,gBACZhC,OAAA,CAACvB,GAAG;UAACoD,EAAE,EAAE;YACP8B,MAAM,EAAE,GAAG;YACXK,OAAO,EAAE,MAAM;YACf1B,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBM,cAAc,EAAE,QAAQ;YACxBe,YAAY,EAAE,CAAC;YACfpB,EAAE,EAAE;UACN,CAAE;UAAAR,QAAA,eACAhC,OAAA,CAACtB,UAAU;YAACuD,OAAO,EAAC,IAAI;YAACJ,EAAE,EAAE;cAAEwB,KAAK,EAAE;YAAQ,CAAE;YAAArB,QAAA,EAAC;UAEjD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAENrC,OAAA,CAACtB,UAAU;UAACuD,OAAO,EAAC,OAAO;UAAAD,QAAA,GAAC,iEACb,EAAC1B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEgD,QAAQ;QAAA;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eAEbrC,OAAA,CAACtB,UAAU;UAACuD,OAAO,EAAC,OAAO;UAACoB,KAAK,EAAC,eAAe;UAACxB,EAAE,EAAE;YAAE4B,EAAE,EAAE;UAAE,CAAE;UAAAzB,QAAA,EAAC;QAEjE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAChBrC,OAAA,CAACR,aAAa;QAAAwC,QAAA,gBACZhC,OAAA,CAACnB,MAAM;UAAC4D,OAAO,EAAEA,CAAA,KAAMhC,kBAAkB,CAAC,KAAK,CAAE;UAAAuB,QAAA,EAAC;QAElD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACR/B,aAAa,IAAI,CAACA,aAAa,CAAC2D,WAAW,iBAC1CjE,OAAA,CAACnB,MAAM;UACLoD,OAAO,EAAC,WAAW;UACnBQ,OAAO,EAAEA,CAAA,KAAMlB,oBAAoB,CAACjB,aAAa,CAACgE,EAAE,CAAE;UAAAtC,QAAA,EACvD;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACjC,EAAA,CAhPIH,YAAY;AAAA2E,EAAA,GAAZ3E,YAAY;AAkPlB,eAAeA,YAAY;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}