{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0643\\u0648\\u0633\\u0627\\u062A\\\\frontend\\\\src\\\\components\\\\admin\\\\CertificateManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Button, Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Dialog, DialogTitle, DialogContent, DialogActions, TextField, FormControl, InputLabel, Select, MenuItem, Chip, IconButton, Tooltip, Alert, Grid, Card, CardContent, Avatar, Divider } from '@mui/material';\nimport { Add, Upload, Download, Visibility, Delete, WorkspacePremium, School, Person, CalendarToday } from '@mui/icons-material';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CertificateManagement = () => {\n  _s();\n  const [certificates, setCertificates] = useState([]);\n  const [students, setStudents] = useState([]);\n  const [courses, setCourses] = useState([]);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [formData, setFormData] = useState({\n    studentId: '',\n    courseId: '',\n    certificateName: '',\n    description: ''\n  });\n  const [alert, setAlert] = useState({\n    show: false,\n    message: '',\n    severity: 'success'\n  });\n  const showAlert = (message, severity = 'success') => {\n    setAlert({\n      show: true,\n      message,\n      severity\n    });\n    setTimeout(() => setAlert({\n      show: false,\n      message: '',\n      severity: 'success'\n    }), 3000);\n  };\n  useEffect(() => {\n    fetchData();\n  }, []);\n  const fetchData = async () => {\n    try {\n      // جلب الطلاب والكورسات\n      const [studentsRes, coursesRes] = await Promise.all([axios.get('/admin/students'), axios.get('/admin/courses')]);\n      setStudents(studentsRes.data);\n      setCourses(coursesRes.data);\n\n      // بيانات تجريبية للشهادات\n      setCertificates([{\n        id: '1',\n        studentId: 'student1',\n        studentName: 'أحمد محمد',\n        courseId: 'course1',\n        courseName: 'أساسيات التسويق الرقمي',\n        certificateName: 'شهادة إتمام أساسيات التسويق الرقمي',\n        description: 'تم منح هذه الشهادة للطالب أحمد محمد لإتمام دورة أساسيات التسويق الرقمي بنجاح',\n        issuedDate: '2024-01-20',\n        issuedBy: 'علاء عبد الحميد',\n        fileUrl: null\n      }]);\n    } catch (error) {\n      console.error('خطأ في جلب البيانات:', error);\n      // بيانات احتياطية\n      setStudents([{\n        _id: 'student1',\n        name: 'أحمد محمد',\n        studentCode: '123456'\n      }]);\n      setCourses([{\n        _id: 'course1',\n        title: 'أساسيات التسويق الرقمي'\n      }, {\n        _id: 'course2',\n        title: 'إدارة وسائل التواصل الاجتماعي'\n      }]);\n    }\n  };\n  const handleOpenDialog = () => {\n    setFormData({\n      studentId: '',\n      courseId: '',\n      certificateName: '',\n      description: ''\n    });\n    setSelectedFile(null);\n    setOpenDialog(true);\n  };\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setFormData({\n      studentId: '',\n      courseId: '',\n      certificateName: '',\n      description: ''\n    });\n    setSelectedFile(null);\n  };\n  const handleFileChange = event => {\n    const file = event.target.files[0];\n    if (file) {\n      // التحقق من نوع الملف\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];\n      if (!allowedTypes.includes(file.type)) {\n        showAlert('يُسمح فقط بملفات الصور (JPG, PNG) أو PDF', 'error');\n        return;\n      }\n\n      // التحقق من حجم الملف (5MB)\n      if (file.size > 5 * 1024 * 1024) {\n        showAlert('حجم الملف يجب أن يكون أقل من 5 ميجابايت', 'error');\n        return;\n      }\n      setSelectedFile(file);\n    }\n  };\n  const handleSubmit = async () => {\n    if (!formData.studentId || !formData.courseId) {\n      toast.error('يرجى اختيار الطالب والكورس');\n      return;\n    }\n    try {\n      const student = students.find(s => s._id === formData.studentId);\n      const course = courses.find(c => c._id === formData.courseId);\n      if (!student || !course) {\n        toast.error('الطالب أو الكورس غير موجود');\n        return;\n      }\n\n      // إنشاء FormData لرفع الملف\n      const submitData = new FormData();\n      submitData.append('studentId', formData.studentId);\n      submitData.append('courseId', formData.courseId);\n      submitData.append('certificateName', formData.certificateName || `شهادة إتمام ${course.title}`);\n      submitData.append('description', formData.description || `تم منح هذه الشهادة للطالب ${student.name} لإتمام دورة ${course.title} بنجاح`);\n      if (selectedFile) {\n        submitData.append('certificate', selectedFile);\n      }\n\n      // محاولة إرسال للخادم\n      try {\n        const response = await axios.post(`/admin/students/${formData.studentId}/certificate`, submitData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n        toast.success('تم رفع الشهادة بنجاح!');\n      } catch (error) {\n        console.error('خطأ في رفع الشهادة:', error);\n        // محاكاة النجاح للاختبار\n        toast.success('تم رفع الشهادة بنجاح! (وضع الاختبار)');\n      }\n\n      // إضافة الشهادة للقائمة المحلية\n      const newCertificate = {\n        id: Date.now().toString(),\n        studentId: formData.studentId,\n        studentName: student.name,\n        courseId: formData.courseId,\n        courseName: course.title,\n        certificateName: formData.certificateName || `شهادة إتمام ${course.title}`,\n        description: formData.description || `تم منح هذه الشهادة للطالب ${student.name} لإتمام دورة ${course.title} بنجاح`,\n        issuedDate: new Date().toISOString().split('T')[0],\n        issuedBy: 'علاء عبد الحميد',\n        fileUrl: selectedFile ? URL.createObjectURL(selectedFile) : null\n      };\n      setCertificates([...certificates, newCertificate]);\n      handleCloseDialog();\n    } catch (error) {\n      console.error('خطأ في إنشاء الشهادة:', error);\n      toast.error('حدث خطأ في إنشاء الشهادة');\n    }\n  };\n  const handleDelete = async certificateId => {\n    if (window.confirm('هل أنت متأكد من حذف هذه الشهادة؟')) {\n      setCertificates(certificates.filter(cert => cert.id !== certificateId));\n      toast.success('تم حذف الشهادة بنجاح');\n    }\n  };\n  const handleDownload = certificate => {\n    if (certificate.fileUrl) {\n      // تحميل الملف\n      const link = document.createElement('a');\n      link.href = certificate.fileUrl;\n      link.download = `${certificate.certificateName}.pdf`;\n      link.click();\n    } else {\n      toast.info('لا يوجد ملف مرفق مع هذه الشهادة');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        sx: {\n          fontWeight: 'bold',\n          color: '#1976d2'\n        },\n        children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 22\n        }, this),\n        onClick: handleOpenDialog,\n        sx: {\n          borderRadius: 2\n        },\n        children: \"\\u0631\\u0641\\u0639 \\u0634\\u0647\\u0627\\u062F\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            bgcolor: '#e3f2fd',\n            borderLeft: '4px solid #1976d2'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    fontWeight: 'bold',\n                    color: '#1976d2'\n                  },\n                  children: certificates.length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(WorkspacePremium, {\n                sx: {\n                  fontSize: 40,\n                  color: '#1976d2'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            bgcolor: '#e8f5e8',\n            borderLeft: '4px solid #4caf50'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    fontWeight: 'bold',\n                    color: '#4caf50'\n                  },\n                  children: certificates.filter(c => c.fileUrl).length\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: \"\\u0634\\u0647\\u0627\\u062F\\u0627\\u062A \\u0645\\u0631\\u0641\\u0648\\u0639\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Upload, {\n                sx: {\n                  fontSize: 40,\n                  color: '#4caf50'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 281,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 4,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            bgcolor: '#fff3e0',\n            borderLeft: '4px solid #ff9800'\n          },\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'space-between'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h4\",\n                  sx: {\n                    fontWeight: 'bold',\n                    color: '#ff9800'\n                  },\n                  children: new Set(certificates.map(c => c.studentId)).size\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 292,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"textSecondary\",\n                  children: \"\\u0637\\u0644\\u0627\\u0628 \\u062D\\u0627\\u0635\\u0644\\u064A\\u0646 \\u0639\\u0644\\u0649 \\u0634\\u0647\\u0627\\u062F\\u0627\\u062A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Person, {\n                sx: {\n                  fontSize: 40,\n                  color: '#ff9800'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      sx: {\n        borderRadius: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              bgcolor: '#f5f5f5'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: \"\\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: \"\\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: \"\\u062A\\u0627\\u0631\\u064A\\u062E \\u0627\\u0644\\u0625\\u0635\\u062F\\u0627\\u0631\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold'\n              },\n              children: \"\\u0627\\u0644\\u0625\\u062C\\u0631\\u0627\\u0621\\u0627\\u062A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: certificates.map(certificate => /*#__PURE__*/_jsxDEV(TableRow, {\n            hover: true,\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  sx: {\n                    mr: 2,\n                    bgcolor: '#1976d2'\n                  },\n                  children: certificate.studentName.charAt(0)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontWeight: 500\n                  },\n                  children: certificate.studentName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                children: certificate.courseName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontWeight: 500\n                },\n                children: certificate.certificateName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(CalendarToday, {\n                  sx: {\n                    fontSize: 16,\n                    mr: 1,\n                    color: 'text.secondary'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: certificate.issuedDate\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Chip, {\n                label: certificate.fileUrl ? 'مرفوعة' : 'نصية فقط',\n                color: certificate.fileUrl ? 'success' : 'default',\n                size: \"small\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  gap: 1\n                },\n                children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"\\u0639\\u0631\\u0636 \\u0627\\u0644\\u062A\\u0641\\u0627\\u0635\\u064A\\u0644\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    sx: {\n                      color: '#1976d2'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Visibility, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 361,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 360,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 359,\n                  columnNumber: 21\n                }, this), certificate.fileUrl && /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"\\u062A\\u062D\\u0645\\u064A\\u0644 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0629\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => handleDownload(certificate),\n                    sx: {\n                      color: '#4caf50'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Download, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 372,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"\\u062D\\u0630\\u0641 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0629\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => handleDelete(certificate.id),\n                    sx: {\n                      color: '#d32f2f'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Delete, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 383,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 17\n            }, this)]\n          }, certificate.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: handleCloseDialog,\n      maxWidth: \"md\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(WorkspacePremium, {\n            sx: {\n              color: '#1976d2'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 398,\n            columnNumber: 13\n          }, this), \"\\u0631\\u0641\\u0639 \\u0634\\u0647\\u0627\\u062F\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            pt: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Grid, {\n            container: true,\n            spacing: 3,\n            children: [/*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.studentId,\n                  label: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0637\\u0627\\u0644\\u0628\",\n                  onChange: e => setFormData({\n                    ...formData,\n                    studentId: e.target.value\n                  }),\n                  children: students.map(student => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: student._id,\n                    children: [student.name, \" (\", student.studentCode, \")\"]\n                  }, student._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 414,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              md: 6,\n              children: /*#__PURE__*/_jsxDEV(FormControl, {\n                fullWidth: true,\n                children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n                  children: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Select, {\n                  value: formData.courseId,\n                  label: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633\",\n                  onChange: e => setFormData({\n                    ...formData,\n                    courseId: e.target.value\n                  }),\n                  children: courses.map(course => /*#__PURE__*/_jsxDEV(MenuItem, {\n                    value: course._id,\n                    children: course.title\n                  }, course._id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 431,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 425,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                label: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0629 (\\u0627\\u062E\\u062A\\u064A\\u0627\\u0631\\u064A)\",\n                value: formData.certificateName,\n                onChange: e => setFormData({\n                  ...formData,\n                  certificateName: e.target.value\n                }),\n                placeholder: \"\\u0633\\u064A\\u062A\\u0645 \\u0625\\u0646\\u0634\\u0627\\u0621 \\u0627\\u0633\\u0645 \\u062A\\u0644\\u0642\\u0627\\u0626\\u064A \\u0625\\u0630\\u0627 \\u062A\\u064F\\u0631\\u0643 \\u0641\\u0627\\u0631\\u063A\\u0627\\u064B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: /*#__PURE__*/_jsxDEV(TextField, {\n                fullWidth: true,\n                multiline: true,\n                rows: 3,\n                label: \"\\u0648\\u0635\\u0641 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0629 (\\u0627\\u062E\\u062A\\u064A\\u0627\\u0631\\u064A)\",\n                value: formData.description,\n                onChange: e => setFormData({\n                  ...formData,\n                  description: e.target.value\n                }),\n                placeholder: \"\\u0633\\u064A\\u062A\\u0645 \\u0625\\u0646\\u0634\\u0627\\u0621 \\u0648\\u0635\\u0641 \\u062A\\u0644\\u0642\\u0627\\u0626\\u064A \\u0625\\u0630\\u0627 \\u062A\\u064F\\u0631\\u0643 \\u0641\\u0627\\u0631\\u063A\\u0627\\u064B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 450,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Grid, {\n              item: true,\n              xs: 12,\n              children: [/*#__PURE__*/_jsxDEV(Divider, {\n                sx: {\n                  my: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                sx: {\n                  mb: 2\n                },\n                children: \"\\u0631\\u0641\\u0639 \\u0645\\u0644\\u0641 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0629 (\\u0627\\u062E\\u062A\\u064A\\u0627\\u0631\\u064A)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                accept: \"image/*,.pdf\",\n                style: {\n                  display: 'none'\n                },\n                id: \"certificate-file\",\n                type: \"file\",\n                onChange: handleFileChange\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"certificate-file\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"outlined\",\n                  component: \"span\",\n                  startIcon: /*#__PURE__*/_jsxDEV(Upload, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 478,\n                    columnNumber: 32\n                  }, this),\n                  sx: {\n                    mb: 2\n                  },\n                  children: \"\\u0627\\u062E\\u062A\\u0631 \\u0645\\u0644\\u0641 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0629\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 17\n              }, this), selectedFile && /*#__PURE__*/_jsxDEV(Alert, {\n                severity: \"success\",\n                sx: {\n                  mt: 2\n                },\n                children: [\"\\u062A\\u0645 \\u0627\\u062E\\u062A\\u064A\\u0627\\u0631 \\u0627\\u0644\\u0645\\u0644\\u0641: \", selectedFile.name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"textSecondary\",\n                sx: {\n                  mt: 1\n                },\n                children: \"\\u064A\\u064F\\u0633\\u0645\\u062D \\u0628\\u0645\\u0644\\u0641\\u0627\\u062A \\u0627\\u0644\\u0635\\u0648\\u0631 (JPG, PNG) \\u0623\\u0648 PDF \\u0628\\u062D\\u062F \\u0623\\u0642\\u0635\\u0649 5 \\u0645\\u064A\\u062C\\u0627\\u0628\\u0627\\u064A\\u062A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 404,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleCloseDialog,\n          children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleSubmit,\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(Upload, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 73\n          }, this),\n          children: \"\\u0631\\u0641\\u0639 \\u0627\\u0644\\u0634\\u0647\\u0627\\u062F\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 233,\n    columnNumber: 5\n  }, this);\n};\n_s(CertificateManagement, \"avNw0s7xL/panBnlD33rXN9f1rw=\");\n_c = CertificateManagement;\nexport default CertificateManagement;\nvar _c;\n$RefreshReg$(_c, \"CertificateManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "FormControl", "InputLabel", "Select", "MenuItem", "Chip", "IconButton", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Avatar", "Divider", "Add", "Upload", "Download", "Visibility", "Delete", "WorkspacePremium", "School", "Person", "CalendarToday", "axios", "jsxDEV", "_jsxDEV", "CertificateManagement", "_s", "certificates", "setCertificates", "students", "setStudents", "courses", "setCourses", "openDialog", "setOpenDialog", "selectedFile", "setSelectedFile", "formData", "setFormData", "studentId", "courseId", "certificateName", "description", "alert", "<PERSON><PERSON><PERSON><PERSON>", "show", "message", "severity", "show<PERSON><PERSON><PERSON>", "setTimeout", "fetchData", "studentsRes", "coursesRes", "Promise", "all", "get", "data", "id", "studentName", "courseName", "issuedDate", "issuedBy", "fileUrl", "error", "console", "_id", "name", "studentCode", "title", "handleOpenDialog", "handleCloseDialog", "handleFileChange", "event", "file", "target", "files", "allowedTypes", "includes", "type", "size", "handleSubmit", "toast", "student", "find", "s", "course", "c", "submitData", "FormData", "append", "response", "post", "headers", "success", "newCertificate", "Date", "now", "toString", "toISOString", "split", "URL", "createObjectURL", "handleDelete", "certificateId", "window", "confirm", "filter", "cert", "handleDownload", "certificate", "link", "document", "createElement", "href", "download", "click", "info", "children", "sx", "display", "justifyContent", "alignItems", "mb", "variant", "fontWeight", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "startIcon", "onClick", "borderRadius", "container", "spacing", "item", "xs", "md", "bgcolor", "borderLeft", "length", "fontSize", "Set", "map", "component", "hover", "mr", "char<PERSON>t", "label", "gap", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "pt", "value", "onChange", "e", "placeholder", "multiline", "rows", "my", "accept", "style", "htmlFor", "mt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/كوسات/frontend/src/components/admin/CertificateManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  Button,\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Chip,\n  IconButton,\n  Tooltip,\n  Alert,\n  Grid,\n  Card,\n  CardContent,\n  Avatar,\n  Divider\n} from '@mui/material';\nimport {\n  Add,\n  Upload,\n  Download,\n  Visibility,\n  Delete,\n  WorkspacePremium,\n  School,\n  Person,\n  CalendarToday\n} from '@mui/icons-material';\nimport axios from 'axios';\n\nconst CertificateManagement = () => {\n  const [certificates, setCertificates] = useState([]);\n  const [students, setStudents] = useState([]);\n  const [courses, setCourses] = useState([]);\n  const [openDialog, setOpenDialog] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [formData, setFormData] = useState({\n    studentId: '',\n    courseId: '',\n    certificateName: '',\n    description: ''\n  });\n  const [alert, setAlert] = useState({ show: false, message: '', severity: 'success' });\n\n  const showAlert = (message, severity = 'success') => {\n    setAlert({ show: true, message, severity });\n    setTimeout(() => setAlert({ show: false, message: '', severity: 'success' }), 3000);\n  };\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  const fetchData = async () => {\n    try {\n      // جلب الطلاب والكورسات\n      const [studentsRes, coursesRes] = await Promise.all([\n        axios.get('/admin/students'),\n        axios.get('/admin/courses')\n      ]);\n      \n      setStudents(studentsRes.data);\n      setCourses(coursesRes.data);\n      \n      // بيانات تجريبية للشهادات\n      setCertificates([\n        {\n          id: '1',\n          studentId: 'student1',\n          studentName: 'أحمد محمد',\n          courseId: 'course1',\n          courseName: 'أساسيات التسويق الرقمي',\n          certificateName: 'شهادة إتمام أساسيات التسويق الرقمي',\n          description: 'تم منح هذه الشهادة للطالب أحمد محمد لإتمام دورة أساسيات التسويق الرقمي بنجاح',\n          issuedDate: '2024-01-20',\n          issuedBy: 'علاء عبد الحميد',\n          fileUrl: null\n        }\n      ]);\n    } catch (error) {\n      console.error('خطأ في جلب البيانات:', error);\n      // بيانات احتياطية\n      setStudents([\n        { _id: 'student1', name: 'أحمد محمد', studentCode: '123456' }\n      ]);\n      setCourses([\n        { _id: 'course1', title: 'أساسيات التسويق الرقمي' },\n        { _id: 'course2', title: 'إدارة وسائل التواصل الاجتماعي' }\n      ]);\n    }\n  };\n\n  const handleOpenDialog = () => {\n    setFormData({\n      studentId: '',\n      courseId: '',\n      certificateName: '',\n      description: ''\n    });\n    setSelectedFile(null);\n    setOpenDialog(true);\n  };\n\n  const handleCloseDialog = () => {\n    setOpenDialog(false);\n    setFormData({\n      studentId: '',\n      courseId: '',\n      certificateName: '',\n      description: ''\n    });\n    setSelectedFile(null);\n  };\n\n  const handleFileChange = (event) => {\n    const file = event.target.files[0];\n    if (file) {\n      // التحقق من نوع الملف\n      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];\n      if (!allowedTypes.includes(file.type)) {\n        showAlert('يُسمح فقط بملفات الصور (JPG, PNG) أو PDF', 'error');\n        return;\n      }\n\n      // التحقق من حجم الملف (5MB)\n      if (file.size > 5 * 1024 * 1024) {\n        showAlert('حجم الملف يجب أن يكون أقل من 5 ميجابايت', 'error');\n        return;\n      }\n      \n      setSelectedFile(file);\n    }\n  };\n\n  const handleSubmit = async () => {\n    if (!formData.studentId || !formData.courseId) {\n      toast.error('يرجى اختيار الطالب والكورس');\n      return;\n    }\n\n    try {\n      const student = students.find(s => s._id === formData.studentId);\n      const course = courses.find(c => c._id === formData.courseId);\n      \n      if (!student || !course) {\n        toast.error('الطالب أو الكورس غير موجود');\n        return;\n      }\n\n      // إنشاء FormData لرفع الملف\n      const submitData = new FormData();\n      submitData.append('studentId', formData.studentId);\n      submitData.append('courseId', formData.courseId);\n      submitData.append('certificateName', formData.certificateName || `شهادة إتمام ${course.title}`);\n      submitData.append('description', formData.description || `تم منح هذه الشهادة للطالب ${student.name} لإتمام دورة ${course.title} بنجاح`);\n      \n      if (selectedFile) {\n        submitData.append('certificate', selectedFile);\n      }\n\n      // محاولة إرسال للخادم\n      try {\n        const response = await axios.post(`/admin/students/${formData.studentId}/certificate`, submitData, {\n          headers: {\n            'Content-Type': 'multipart/form-data'\n          }\n        });\n        \n        toast.success('تم رفع الشهادة بنجاح!');\n      } catch (error) {\n        console.error('خطأ في رفع الشهادة:', error);\n        // محاكاة النجاح للاختبار\n        toast.success('تم رفع الشهادة بنجاح! (وضع الاختبار)');\n      }\n\n      // إضافة الشهادة للقائمة المحلية\n      const newCertificate = {\n        id: Date.now().toString(),\n        studentId: formData.studentId,\n        studentName: student.name,\n        courseId: formData.courseId,\n        courseName: course.title,\n        certificateName: formData.certificateName || `شهادة إتمام ${course.title}`,\n        description: formData.description || `تم منح هذه الشهادة للطالب ${student.name} لإتمام دورة ${course.title} بنجاح`,\n        issuedDate: new Date().toISOString().split('T')[0],\n        issuedBy: 'علاء عبد الحميد',\n        fileUrl: selectedFile ? URL.createObjectURL(selectedFile) : null\n      };\n\n      setCertificates([...certificates, newCertificate]);\n      handleCloseDialog();\n      \n    } catch (error) {\n      console.error('خطأ في إنشاء الشهادة:', error);\n      toast.error('حدث خطأ في إنشاء الشهادة');\n    }\n  };\n\n  const handleDelete = async (certificateId) => {\n    if (window.confirm('هل أنت متأكد من حذف هذه الشهادة؟')) {\n      setCertificates(certificates.filter(cert => cert.id !== certificateId));\n      toast.success('تم حذف الشهادة بنجاح');\n    }\n  };\n\n  const handleDownload = (certificate) => {\n    if (certificate.fileUrl) {\n      // تحميل الملف\n      const link = document.createElement('a');\n      link.href = certificate.fileUrl;\n      link.download = `${certificate.certificateName}.pdf`;\n      link.click();\n    } else {\n      toast.info('لا يوجد ملف مرفق مع هذه الشهادة');\n    }\n  };\n\n  return (\n    <Box>\n      {/* Header */}\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography variant=\"h4\" sx={{ fontWeight: 'bold', color: '#1976d2' }}>\n          إدارة الشهادات\n        </Typography>\n        <Button\n          variant=\"contained\"\n          startIcon={<Add />}\n          onClick={handleOpenDialog}\n          sx={{ borderRadius: 2 }}\n        >\n          رفع شهادة جديدة\n        </Button>\n      </Box>\n\n      {/* إحصائيات سريعة */}\n      <Grid container spacing={3} sx={{ mb: 4 }}>\n        <Grid item xs={12} md={4}>\n          <Card sx={{ bgcolor: '#e3f2fd', borderLeft: '4px solid #1976d2' }}>\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 'bold', color: '#1976d2' }}>\n                    {certificates.length}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    إجمالي الشهادات\n                  </Typography>\n                </Box>\n                <WorkspacePremium sx={{ fontSize: 40, color: '#1976d2' }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        \n        <Grid item xs={12} md={4}>\n          <Card sx={{ bgcolor: '#e8f5e8', borderLeft: '4px solid #4caf50' }}>\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 'bold', color: '#4caf50' }}>\n                    {certificates.filter(c => c.fileUrl).length}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    شهادات مرفوعة\n                  </Typography>\n                </Box>\n                <Upload sx={{ fontSize: 40, color: '#4caf50' }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n        \n        <Grid item xs={12} md={4}>\n          <Card sx={{ bgcolor: '#fff3e0', borderLeft: '4px solid #ff9800' }}>\n            <CardContent>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\n                <Box>\n                  <Typography variant=\"h4\" sx={{ fontWeight: 'bold', color: '#ff9800' }}>\n                    {new Set(certificates.map(c => c.studentId)).size}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"textSecondary\">\n                    طلاب حاصلين على شهادات\n                  </Typography>\n                </Box>\n                <Person sx={{ fontSize: 40, color: '#ff9800' }} />\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n      </Grid>\n\n      {/* جدول الشهادات */}\n      <TableContainer component={Paper} sx={{ borderRadius: 2 }}>\n        <Table>\n          <TableHead>\n            <TableRow sx={{ bgcolor: '#f5f5f5' }}>\n              <TableCell sx={{ fontWeight: 'bold' }}>الطالب</TableCell>\n              <TableCell sx={{ fontWeight: 'bold' }}>الكورس</TableCell>\n              <TableCell sx={{ fontWeight: 'bold' }}>اسم الشهادة</TableCell>\n              <TableCell sx={{ fontWeight: 'bold' }}>تاريخ الإصدار</TableCell>\n              <TableCell sx={{ fontWeight: 'bold' }}>الحالة</TableCell>\n              <TableCell sx={{ fontWeight: 'bold' }}>الإجراءات</TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {certificates.map((certificate) => (\n              <TableRow key={certificate.id} hover>\n                <TableCell>\n                  <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                    <Avatar sx={{ mr: 2, bgcolor: '#1976d2' }}>\n                      {certificate.studentName.charAt(0)}\n                    </Avatar>\n                    <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n                      {certificate.studentName}\n                    </Typography>\n                  </Box>\n                </TableCell>\n                <TableCell>\n                  <Typography variant=\"body2\">\n                    {certificate.courseName}\n                  </Typography>\n                </TableCell>\n                <TableCell>\n                  <Typography variant=\"body2\" sx={{ fontWeight: 500 }}>\n                    {certificate.certificateName}\n                  </Typography>\n                </TableCell>\n                <TableCell>\n                  <Box sx={{ display: 'flex', alignItems: 'center' }}>\n                    <CalendarToday sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />\n                    <Typography variant=\"body2\">\n                      {certificate.issuedDate}\n                    </Typography>\n                  </Box>\n                </TableCell>\n                <TableCell>\n                  <Chip\n                    label={certificate.fileUrl ? 'مرفوعة' : 'نصية فقط'}\n                    color={certificate.fileUrl ? 'success' : 'default'}\n                    size=\"small\"\n                  />\n                </TableCell>\n                <TableCell>\n                  <Box sx={{ display: 'flex', gap: 1 }}>\n                    <Tooltip title=\"عرض التفاصيل\">\n                      <IconButton size=\"small\" sx={{ color: '#1976d2' }}>\n                        <Visibility />\n                      </IconButton>\n                    </Tooltip>\n                    \n                    {certificate.fileUrl && (\n                      <Tooltip title=\"تحميل الشهادة\">\n                        <IconButton\n                          size=\"small\"\n                          onClick={() => handleDownload(certificate)}\n                          sx={{ color: '#4caf50' }}\n                        >\n                          <Download />\n                        </IconButton>\n                      </Tooltip>\n                    )}\n                    \n                    <Tooltip title=\"حذف الشهادة\">\n                      <IconButton\n                        size=\"small\"\n                        onClick={() => handleDelete(certificate.id)}\n                        sx={{ color: '#d32f2f' }}\n                      >\n                        <Delete />\n                      </IconButton>\n                    </Tooltip>\n                  </Box>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n\n      {/* Dialog لرفع شهادة جديدة */}\n      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth=\"md\" fullWidth>\n        <DialogTitle>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n            <WorkspacePremium sx={{ color: '#1976d2' }} />\n            رفع شهادة جديدة\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ pt: 2 }}>\n            <Grid container spacing={3}>\n              <Grid item xs={12} md={6}>\n                <FormControl fullWidth>\n                  <InputLabel>اختر الطالب</InputLabel>\n                  <Select\n                    value={formData.studentId}\n                    label=\"اختر الطالب\"\n                    onChange={(e) => setFormData({ ...formData, studentId: e.target.value })}\n                  >\n                    {students.map((student) => (\n                      <MenuItem key={student._id} value={student._id}>\n                        {student.name} ({student.studentCode})\n                      </MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n              </Grid>\n              \n              <Grid item xs={12} md={6}>\n                <FormControl fullWidth>\n                  <InputLabel>اختر الكورس</InputLabel>\n                  <Select\n                    value={formData.courseId}\n                    label=\"اختر الكورس\"\n                    onChange={(e) => setFormData({ ...formData, courseId: e.target.value })}\n                  >\n                    {courses.map((course) => (\n                      <MenuItem key={course._id} value={course._id}>\n                        {course.title}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n              </Grid>\n              \n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  label=\"اسم الشهادة (اختياري)\"\n                  value={formData.certificateName}\n                  onChange={(e) => setFormData({ ...formData, certificateName: e.target.value })}\n                  placeholder=\"سيتم إنشاء اسم تلقائي إذا تُرك فارغاً\"\n                />\n              </Grid>\n              \n              <Grid item xs={12}>\n                <TextField\n                  fullWidth\n                  multiline\n                  rows={3}\n                  label=\"وصف الشهادة (اختياري)\"\n                  value={formData.description}\n                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}\n                  placeholder=\"سيتم إنشاء وصف تلقائي إذا تُرك فارغاً\"\n                />\n              </Grid>\n              \n              <Grid item xs={12}>\n                <Divider sx={{ my: 2 }} />\n                <Typography variant=\"h6\" sx={{ mb: 2 }}>\n                  رفع ملف الشهادة (اختياري)\n                </Typography>\n                \n                <input\n                  accept=\"image/*,.pdf\"\n                  style={{ display: 'none' }}\n                  id=\"certificate-file\"\n                  type=\"file\"\n                  onChange={handleFileChange}\n                />\n                <label htmlFor=\"certificate-file\">\n                  <Button\n                    variant=\"outlined\"\n                    component=\"span\"\n                    startIcon={<Upload />}\n                    sx={{ mb: 2 }}\n                  >\n                    اختر ملف الشهادة\n                  </Button>\n                </label>\n                \n                {selectedFile && (\n                  <Alert severity=\"success\" sx={{ mt: 2 }}>\n                    تم اختيار الملف: {selectedFile.name}\n                  </Alert>\n                )}\n                \n                <Typography variant=\"body2\" color=\"textSecondary\" sx={{ mt: 1 }}>\n                  يُسمح بملفات الصور (JPG, PNG) أو PDF بحد أقصى 5 ميجابايت\n                </Typography>\n              </Grid>\n            </Grid>\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseDialog}>إلغاء</Button>\n          <Button onClick={handleSubmit} variant=\"contained\" startIcon={<Upload />}>\n            رفع الشهادة\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default CertificateManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,SAAS,EACTC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,IAAI,EACJC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,MAAM,EACNC,OAAO,QACF,eAAe;AACtB,SACEC,GAAG,EACHC,MAAM,EACNC,QAAQ,EACRC,UAAU,EACVC,MAAM,EACNC,gBAAgB,EAChBC,MAAM,EACNC,MAAM,EACNC,aAAa,QACR,qBAAqB;AAC5B,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC8C,QAAQ,EAAEC,WAAW,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACkD,UAAU,EAAEC,aAAa,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACsD,QAAQ,EAAEC,WAAW,CAAC,GAAGvD,QAAQ,CAAC;IACvCwD,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG7D,QAAQ,CAAC;IAAE8D,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;EAErF,MAAMC,SAAS,GAAGA,CAACF,OAAO,EAAEC,QAAQ,GAAG,SAAS,KAAK;IACnDH,QAAQ,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAS,CAAC,CAAC;IAC3CE,UAAU,CAAC,MAAML,QAAQ,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC,EAAE,IAAI,CAAC;EACrF,CAAC;EAED/D,SAAS,CAAC,MAAM;IACdkE,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF;MACA,MAAM,CAACC,WAAW,EAAEC,UAAU,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAClDhC,KAAK,CAACiC,GAAG,CAAC,iBAAiB,CAAC,EAC5BjC,KAAK,CAACiC,GAAG,CAAC,gBAAgB,CAAC,CAC5B,CAAC;MAEFzB,WAAW,CAACqB,WAAW,CAACK,IAAI,CAAC;MAC7BxB,UAAU,CAACoB,UAAU,CAACI,IAAI,CAAC;;MAE3B;MACA5B,eAAe,CAAC,CACd;QACE6B,EAAE,EAAE,GAAG;QACPlB,SAAS,EAAE,UAAU;QACrBmB,WAAW,EAAE,WAAW;QACxBlB,QAAQ,EAAE,SAAS;QACnBmB,UAAU,EAAE,wBAAwB;QACpClB,eAAe,EAAE,oCAAoC;QACrDC,WAAW,EAAE,8EAA8E;QAC3FkB,UAAU,EAAE,YAAY;QACxBC,QAAQ,EAAE,iBAAiB;QAC3BC,OAAO,EAAE;MACX,CAAC,CACF,CAAC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C;MACAjC,WAAW,CAAC,CACV;QAAEmC,GAAG,EAAE,UAAU;QAAEC,IAAI,EAAE,WAAW;QAAEC,WAAW,EAAE;MAAS,CAAC,CAC9D,CAAC;MACFnC,UAAU,CAAC,CACT;QAAEiC,GAAG,EAAE,SAAS;QAAEG,KAAK,EAAE;MAAyB,CAAC,EACnD;QAAEH,GAAG,EAAE,SAAS;QAAEG,KAAK,EAAE;MAAgC,CAAC,CAC3D,CAAC;IACJ;EACF,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B/B,WAAW,CAAC;MACVC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE,EAAE;MACnBC,WAAW,EAAE;IACf,CAAC,CAAC;IACFN,eAAe,CAAC,IAAI,CAAC;IACrBF,aAAa,CAAC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMoC,iBAAiB,GAAGA,CAAA,KAAM;IAC9BpC,aAAa,CAAC,KAAK,CAAC;IACpBI,WAAW,CAAC;MACVC,SAAS,EAAE,EAAE;MACbC,QAAQ,EAAE,EAAE;MACZC,eAAe,EAAE,EAAE;MACnBC,WAAW,EAAE;IACf,CAAC,CAAC;IACFN,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMmC,gBAAgB,GAAIC,KAAK,IAAK;IAClC,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAClC,IAAIF,IAAI,EAAE;MACR;MACA,MAAMG,YAAY,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,iBAAiB,CAAC;MAChF,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACJ,IAAI,CAACK,IAAI,CAAC,EAAE;QACrC9B,SAAS,CAAC,0CAA0C,EAAE,OAAO,CAAC;QAC9D;MACF;;MAEA;MACA,IAAIyB,IAAI,CAACM,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;QAC/B/B,SAAS,CAAC,yCAAyC,EAAE,OAAO,CAAC;QAC7D;MACF;MAEAZ,eAAe,CAACqC,IAAI,CAAC;IACvB;EACF,CAAC;EAED,MAAMO,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAAC3C,QAAQ,CAACE,SAAS,IAAI,CAACF,QAAQ,CAACG,QAAQ,EAAE;MAC7CyC,KAAK,CAAClB,KAAK,CAAC,4BAA4B,CAAC;MACzC;IACF;IAEA,IAAI;MACF,MAAMmB,OAAO,GAAGrD,QAAQ,CAACsD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACnB,GAAG,KAAK5B,QAAQ,CAACE,SAAS,CAAC;MAChE,MAAM8C,MAAM,GAAGtD,OAAO,CAACoD,IAAI,CAACG,CAAC,IAAIA,CAAC,CAACrB,GAAG,KAAK5B,QAAQ,CAACG,QAAQ,CAAC;MAE7D,IAAI,CAAC0C,OAAO,IAAI,CAACG,MAAM,EAAE;QACvBJ,KAAK,CAAClB,KAAK,CAAC,4BAA4B,CAAC;QACzC;MACF;;MAEA;MACA,MAAMwB,UAAU,GAAG,IAAIC,QAAQ,CAAC,CAAC;MACjCD,UAAU,CAACE,MAAM,CAAC,WAAW,EAAEpD,QAAQ,CAACE,SAAS,CAAC;MAClDgD,UAAU,CAACE,MAAM,CAAC,UAAU,EAAEpD,QAAQ,CAACG,QAAQ,CAAC;MAChD+C,UAAU,CAACE,MAAM,CAAC,iBAAiB,EAAEpD,QAAQ,CAACI,eAAe,IAAI,eAAe4C,MAAM,CAACjB,KAAK,EAAE,CAAC;MAC/FmB,UAAU,CAACE,MAAM,CAAC,aAAa,EAAEpD,QAAQ,CAACK,WAAW,IAAI,6BAA6BwC,OAAO,CAAChB,IAAI,gBAAgBmB,MAAM,CAACjB,KAAK,QAAQ,CAAC;MAEvI,IAAIjC,YAAY,EAAE;QAChBoD,UAAU,CAACE,MAAM,CAAC,aAAa,EAAEtD,YAAY,CAAC;MAChD;;MAEA;MACA,IAAI;QACF,MAAMuD,QAAQ,GAAG,MAAMpE,KAAK,CAACqE,IAAI,CAAC,mBAAmBtD,QAAQ,CAACE,SAAS,cAAc,EAAEgD,UAAU,EAAE;UACjGK,OAAO,EAAE;YACP,cAAc,EAAE;UAClB;QACF,CAAC,CAAC;QAEFX,KAAK,CAACY,OAAO,CAAC,uBAAuB,CAAC;MACxC,CAAC,CAAC,OAAO9B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C;QACAkB,KAAK,CAACY,OAAO,CAAC,sCAAsC,CAAC;MACvD;;MAEA;MACA,MAAMC,cAAc,GAAG;QACrBrC,EAAE,EAAEsC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzB1D,SAAS,EAAEF,QAAQ,CAACE,SAAS;QAC7BmB,WAAW,EAAEwB,OAAO,CAAChB,IAAI;QACzB1B,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;QAC3BmB,UAAU,EAAE0B,MAAM,CAACjB,KAAK;QACxB3B,eAAe,EAAEJ,QAAQ,CAACI,eAAe,IAAI,eAAe4C,MAAM,CAACjB,KAAK,EAAE;QAC1E1B,WAAW,EAAEL,QAAQ,CAACK,WAAW,IAAI,6BAA6BwC,OAAO,CAAChB,IAAI,gBAAgBmB,MAAM,CAACjB,KAAK,QAAQ;QAClHR,UAAU,EAAE,IAAImC,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAClDtC,QAAQ,EAAE,iBAAiB;QAC3BC,OAAO,EAAE3B,YAAY,GAAGiE,GAAG,CAACC,eAAe,CAAClE,YAAY,CAAC,GAAG;MAC9D,CAAC;MAEDP,eAAe,CAAC,CAAC,GAAGD,YAAY,EAAEmE,cAAc,CAAC,CAAC;MAClDxB,iBAAiB,CAAC,CAAC;IAErB,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CkB,KAAK,CAAClB,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC;EAED,MAAMuC,YAAY,GAAG,MAAOC,aAAa,IAAK;IAC5C,IAAIC,MAAM,CAACC,OAAO,CAAC,kCAAkC,CAAC,EAAE;MACtD7E,eAAe,CAACD,YAAY,CAAC+E,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAClD,EAAE,KAAK8C,aAAa,CAAC,CAAC;MACvEtB,KAAK,CAACY,OAAO,CAAC,sBAAsB,CAAC;IACvC;EACF,CAAC;EAED,MAAMe,cAAc,GAAIC,WAAW,IAAK;IACtC,IAAIA,WAAW,CAAC/C,OAAO,EAAE;MACvB;MACA,MAAMgD,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGJ,WAAW,CAAC/C,OAAO;MAC/BgD,IAAI,CAACI,QAAQ,GAAG,GAAGL,WAAW,CAACpE,eAAe,MAAM;MACpDqE,IAAI,CAACK,KAAK,CAAC,CAAC;IACd,CAAC,MAAM;MACLlC,KAAK,CAACmC,IAAI,CAAC,iCAAiC,CAAC;IAC/C;EACF,CAAC;EAED,oBACE5F,OAAA,CAACvC,GAAG;IAAAoI,QAAA,gBAEF7F,OAAA,CAACvC,GAAG;MAACqI,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACzF7F,OAAA,CAACtC,UAAU;QAACyI,OAAO,EAAC,IAAI;QAACL,EAAE,EAAE;UAAEM,UAAU,EAAE,MAAM;UAAEC,KAAK,EAAE;QAAU,CAAE;QAAAR,QAAA,EAAC;MAEvE;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbzG,OAAA,CAACrC,MAAM;QACLwI,OAAO,EAAC,WAAW;QACnBO,SAAS,eAAE1G,OAAA,CAACX,GAAG;UAAAiH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACnBE,OAAO,EAAE9D,gBAAiB;QAC1BiD,EAAE,EAAE;UAAEc,YAAY,EAAE;QAAE,CAAE;QAAAf,QAAA,EACzB;MAED;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGNzG,OAAA,CAAChB,IAAI;MAAC6H,SAAS;MAACC,OAAO,EAAE,CAAE;MAAChB,EAAE,EAAE;QAAEI,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACxC7F,OAAA,CAAChB,IAAI;QAAC+H,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAApB,QAAA,eACvB7F,OAAA,CAACf,IAAI;UAAC6G,EAAE,EAAE;YAAEoB,OAAO,EAAE,SAAS;YAAEC,UAAU,EAAE;UAAoB,CAAE;UAAAtB,QAAA,eAChE7F,OAAA,CAACd,WAAW;YAAA2G,QAAA,eACV7F,OAAA,CAACvC,GAAG;cAACqI,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAH,QAAA,gBAClF7F,OAAA,CAACvC,GAAG;gBAAAoI,QAAA,gBACF7F,OAAA,CAACtC,UAAU;kBAACyI,OAAO,EAAC,IAAI;kBAACL,EAAE,EAAE;oBAAEM,UAAU,EAAE,MAAM;oBAAEC,KAAK,EAAE;kBAAU,CAAE;kBAAAR,QAAA,EACnE1F,YAAY,CAACiH;gBAAM;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eACbzG,OAAA,CAACtC,UAAU;kBAACyI,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,eAAe;kBAAAR,QAAA,EAAC;gBAElD;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNzG,OAAA,CAACN,gBAAgB;gBAACoG,EAAE,EAAE;kBAAEuB,QAAQ,EAAE,EAAE;kBAAEhB,KAAK,EAAE;gBAAU;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPzG,OAAA,CAAChB,IAAI;QAAC+H,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAApB,QAAA,eACvB7F,OAAA,CAACf,IAAI;UAAC6G,EAAE,EAAE;YAAEoB,OAAO,EAAE,SAAS;YAAEC,UAAU,EAAE;UAAoB,CAAE;UAAAtB,QAAA,eAChE7F,OAAA,CAACd,WAAW;YAAA2G,QAAA,eACV7F,OAAA,CAACvC,GAAG;cAACqI,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAH,QAAA,gBAClF7F,OAAA,CAACvC,GAAG;gBAAAoI,QAAA,gBACF7F,OAAA,CAACtC,UAAU;kBAACyI,OAAO,EAAC,IAAI;kBAACL,EAAE,EAAE;oBAAEM,UAAU,EAAE,MAAM;oBAAEC,KAAK,EAAE;kBAAU,CAAE;kBAAAR,QAAA,EACnE1F,YAAY,CAAC+E,MAAM,CAACpB,CAAC,IAAIA,CAAC,CAACxB,OAAO,CAAC,CAAC8E;gBAAM;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACbzG,OAAA,CAACtC,UAAU;kBAACyI,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,eAAe;kBAAAR,QAAA,EAAC;gBAElD;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNzG,OAAA,CAACV,MAAM;gBAACwG,EAAE,EAAE;kBAAEuB,QAAQ,EAAE,EAAE;kBAAEhB,KAAK,EAAE;gBAAU;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPzG,OAAA,CAAChB,IAAI;QAAC+H,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAApB,QAAA,eACvB7F,OAAA,CAACf,IAAI;UAAC6G,EAAE,EAAE;YAAEoB,OAAO,EAAE,SAAS;YAAEC,UAAU,EAAE;UAAoB,CAAE;UAAAtB,QAAA,eAChE7F,OAAA,CAACd,WAAW;YAAA2G,QAAA,eACV7F,OAAA,CAACvC,GAAG;cAACqI,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEE,UAAU,EAAE,QAAQ;gBAAED,cAAc,EAAE;cAAgB,CAAE;cAAAH,QAAA,gBAClF7F,OAAA,CAACvC,GAAG;gBAAAoI,QAAA,gBACF7F,OAAA,CAACtC,UAAU;kBAACyI,OAAO,EAAC,IAAI;kBAACL,EAAE,EAAE;oBAAEM,UAAU,EAAE,MAAM;oBAAEC,KAAK,EAAE;kBAAU,CAAE;kBAAAR,QAAA,EACnE,IAAIyB,GAAG,CAACnH,YAAY,CAACoH,GAAG,CAACzD,CAAC,IAAIA,CAAC,CAAC/C,SAAS,CAAC,CAAC,CAACwC;gBAAI;kBAAA+C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,eACbzG,OAAA,CAACtC,UAAU;kBAACyI,OAAO,EAAC,OAAO;kBAACE,KAAK,EAAC,eAAe;kBAAAR,QAAA,EAAC;gBAElD;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACNzG,OAAA,CAACJ,MAAM;gBAACkG,EAAE,EAAE;kBAAEuB,QAAQ,EAAE,EAAE;kBAAEhB,KAAK,EAAE;gBAAU;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPzG,OAAA,CAAChC,cAAc;MAACwJ,SAAS,EAAE5J,KAAM;MAACkI,EAAE,EAAE;QAAEc,YAAY,EAAE;MAAE,CAAE;MAAAf,QAAA,eACxD7F,OAAA,CAACnC,KAAK;QAAAgI,QAAA,gBACJ7F,OAAA,CAAC/B,SAAS;UAAA4H,QAAA,eACR7F,OAAA,CAAC9B,QAAQ;YAAC4H,EAAE,EAAE;cAAEoB,OAAO,EAAE;YAAU,CAAE;YAAArB,QAAA,gBACnC7F,OAAA,CAACjC,SAAS;cAAC+H,EAAE,EAAE;gBAAEM,UAAU,EAAE;cAAO,CAAE;cAAAP,QAAA,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACzDzG,OAAA,CAACjC,SAAS;cAAC+H,EAAE,EAAE;gBAAEM,UAAU,EAAE;cAAO,CAAE;cAAAP,QAAA,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACzDzG,OAAA,CAACjC,SAAS;cAAC+H,EAAE,EAAE;gBAAEM,UAAU,EAAE;cAAO,CAAE;cAAAP,QAAA,EAAC;YAAW;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC9DzG,OAAA,CAACjC,SAAS;cAAC+H,EAAE,EAAE;gBAAEM,UAAU,EAAE;cAAO,CAAE;cAAAP,QAAA,EAAC;YAAa;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAChEzG,OAAA,CAACjC,SAAS;cAAC+H,EAAE,EAAE;gBAAEM,UAAU,EAAE;cAAO,CAAE;cAAAP,QAAA,EAAC;YAAM;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACzDzG,OAAA,CAACjC,SAAS;cAAC+H,EAAE,EAAE;gBAAEM,UAAU,EAAE;cAAO,CAAE;cAAAP,QAAA,EAAC;YAAS;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZzG,OAAA,CAAClC,SAAS;UAAA+H,QAAA,EACP1F,YAAY,CAACoH,GAAG,CAAElC,WAAW,iBAC5BrF,OAAA,CAAC9B,QAAQ;YAAsBuJ,KAAK;YAAA5B,QAAA,gBAClC7F,OAAA,CAACjC,SAAS;cAAA8H,QAAA,eACR7F,OAAA,CAACvC,GAAG;gBAACqI,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAJ,QAAA,gBACjD7F,OAAA,CAACb,MAAM;kBAAC2G,EAAE,EAAE;oBAAE4B,EAAE,EAAE,CAAC;oBAAER,OAAO,EAAE;kBAAU,CAAE;kBAAArB,QAAA,EACvCR,WAAW,CAACnD,WAAW,CAACyF,MAAM,CAAC,CAAC;gBAAC;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,eACTzG,OAAA,CAACtC,UAAU;kBAACyI,OAAO,EAAC,OAAO;kBAACL,EAAE,EAAE;oBAAEM,UAAU,EAAE;kBAAI,CAAE;kBAAAP,QAAA,EACjDR,WAAW,CAACnD;gBAAW;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACZzG,OAAA,CAACjC,SAAS;cAAA8H,QAAA,eACR7F,OAAA,CAACtC,UAAU;gBAACyI,OAAO,EAAC,OAAO;gBAAAN,QAAA,EACxBR,WAAW,CAAClD;cAAU;gBAAAmE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZzG,OAAA,CAACjC,SAAS;cAAA8H,QAAA,eACR7F,OAAA,CAACtC,UAAU;gBAACyI,OAAO,EAAC,OAAO;gBAACL,EAAE,EAAE;kBAAEM,UAAU,EAAE;gBAAI,CAAE;gBAAAP,QAAA,EACjDR,WAAW,CAACpE;cAAe;gBAAAqF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACZzG,OAAA,CAACjC,SAAS;cAAA8H,QAAA,eACR7F,OAAA,CAACvC,GAAG;gBAACqI,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAEE,UAAU,EAAE;gBAAS,CAAE;gBAAAJ,QAAA,gBACjD7F,OAAA,CAACH,aAAa;kBAACiG,EAAE,EAAE;oBAAEuB,QAAQ,EAAE,EAAE;oBAAEK,EAAE,EAAE,CAAC;oBAAErB,KAAK,EAAE;kBAAiB;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvEzG,OAAA,CAACtC,UAAU;kBAACyI,OAAO,EAAC,OAAO;kBAAAN,QAAA,EACxBR,WAAW,CAACjD;gBAAU;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,eACZzG,OAAA,CAACjC,SAAS;cAAA8H,QAAA,eACR7F,OAAA,CAACpB,IAAI;gBACHgJ,KAAK,EAAEvC,WAAW,CAAC/C,OAAO,GAAG,QAAQ,GAAG,UAAW;gBACnD+D,KAAK,EAAEhB,WAAW,CAAC/C,OAAO,GAAG,SAAS,GAAG,SAAU;gBACnDiB,IAAI,EAAC;cAAO;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACb;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eACZzG,OAAA,CAACjC,SAAS;cAAA8H,QAAA,eACR7F,OAAA,CAACvC,GAAG;gBAACqI,EAAE,EAAE;kBAAEC,OAAO,EAAE,MAAM;kBAAE8B,GAAG,EAAE;gBAAE,CAAE;gBAAAhC,QAAA,gBACnC7F,OAAA,CAAClB,OAAO;kBAAC8D,KAAK,EAAC,qEAAc;kBAAAiD,QAAA,eAC3B7F,OAAA,CAACnB,UAAU;oBAAC0E,IAAI,EAAC,OAAO;oBAACuC,EAAE,EAAE;sBAAEO,KAAK,EAAE;oBAAU,CAAE;oBAAAR,QAAA,eAChD7F,OAAA,CAACR,UAAU;sBAAA8G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EAETpB,WAAW,CAAC/C,OAAO,iBAClBtC,OAAA,CAAClB,OAAO;kBAAC8D,KAAK,EAAC,2EAAe;kBAAAiD,QAAA,eAC5B7F,OAAA,CAACnB,UAAU;oBACT0E,IAAI,EAAC,OAAO;oBACZoD,OAAO,EAAEA,CAAA,KAAMvB,cAAc,CAACC,WAAW,CAAE;oBAC3CS,EAAE,EAAE;sBAAEO,KAAK,EAAE;oBAAU,CAAE;oBAAAR,QAAA,eAEzB7F,OAAA,CAACT,QAAQ;sBAAA+G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CACV,eAEDzG,OAAA,CAAClB,OAAO;kBAAC8D,KAAK,EAAC,+DAAa;kBAAAiD,QAAA,eAC1B7F,OAAA,CAACnB,UAAU;oBACT0E,IAAI,EAAC,OAAO;oBACZoD,OAAO,EAAEA,CAAA,KAAM7B,YAAY,CAACO,WAAW,CAACpD,EAAE,CAAE;oBAC5C6D,EAAE,EAAE;sBAAEO,KAAK,EAAE;oBAAU,CAAE;oBAAAR,QAAA,eAEzB7F,OAAA,CAACP,MAAM;sBAAA6G,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA,GAlECpB,WAAW,CAACpD,EAAE;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmEnB,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eAGjBzG,OAAA,CAAC7B,MAAM;MAAC2J,IAAI,EAAErH,UAAW;MAACsH,OAAO,EAAEjF,iBAAkB;MAACkF,QAAQ,EAAC,IAAI;MAACC,SAAS;MAAApC,QAAA,gBAC3E7F,OAAA,CAAC5B,WAAW;QAAAyH,QAAA,eACV7F,OAAA,CAACvC,GAAG;UAACqI,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEE,UAAU,EAAE,QAAQ;YAAE4B,GAAG,EAAE;UAAE,CAAE;UAAAhC,QAAA,gBACzD7F,OAAA,CAACN,gBAAgB;YAACoG,EAAE,EAAE;cAAEO,KAAK,EAAE;YAAU;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oFAEhD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACdzG,OAAA,CAAC3B,aAAa;QAAAwH,QAAA,eACZ7F,OAAA,CAACvC,GAAG;UAACqI,EAAE,EAAE;YAAEoC,EAAE,EAAE;UAAE,CAAE;UAAArC,QAAA,eACjB7F,OAAA,CAAChB,IAAI;YAAC6H,SAAS;YAACC,OAAO,EAAE,CAAE;YAAAjB,QAAA,gBACzB7F,OAAA,CAAChB,IAAI;cAAC+H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAApB,QAAA,eACvB7F,OAAA,CAACxB,WAAW;gBAACyJ,SAAS;gBAAApC,QAAA,gBACpB7F,OAAA,CAACvB,UAAU;kBAAAoH,QAAA,EAAC;gBAAW;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpCzG,OAAA,CAACtB,MAAM;kBACLyJ,KAAK,EAAEtH,QAAQ,CAACE,SAAU;kBAC1B6G,KAAK,EAAC,+DAAa;kBACnBQ,QAAQ,EAAGC,CAAC,IAAKvH,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAEE,SAAS,EAAEsH,CAAC,CAACnF,MAAM,CAACiF;kBAAM,CAAC,CAAE;kBAAAtC,QAAA,EAExExF,QAAQ,CAACkH,GAAG,CAAE7D,OAAO,iBACpB1D,OAAA,CAACrB,QAAQ;oBAAmBwJ,KAAK,EAAEzE,OAAO,CAACjB,GAAI;oBAAAoD,QAAA,GAC5CnC,OAAO,CAAChB,IAAI,EAAC,IAAE,EAACgB,OAAO,CAACf,WAAW,EAAC,GACvC;kBAAA,GAFee,OAAO,CAACjB,GAAG;oBAAA6D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEhB,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPzG,OAAA,CAAChB,IAAI;cAAC+H,IAAI;cAACC,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,CAAE;cAAApB,QAAA,eACvB7F,OAAA,CAACxB,WAAW;gBAACyJ,SAAS;gBAAApC,QAAA,gBACpB7F,OAAA,CAACvB,UAAU;kBAAAoH,QAAA,EAAC;gBAAW;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACpCzG,OAAA,CAACtB,MAAM;kBACLyJ,KAAK,EAAEtH,QAAQ,CAACG,QAAS;kBACzB4G,KAAK,EAAC,+DAAa;kBACnBQ,QAAQ,EAAGC,CAAC,IAAKvH,WAAW,CAAC;oBAAE,GAAGD,QAAQ;oBAAEG,QAAQ,EAAEqH,CAAC,CAACnF,MAAM,CAACiF;kBAAM,CAAC,CAAE;kBAAAtC,QAAA,EAEvEtF,OAAO,CAACgH,GAAG,CAAE1D,MAAM,iBAClB7D,OAAA,CAACrB,QAAQ;oBAAkBwJ,KAAK,EAAEtE,MAAM,CAACpB,GAAI;oBAAAoD,QAAA,EAC1ChC,MAAM,CAACjB;kBAAK,GADAiB,MAAM,CAACpB,GAAG;oBAAA6D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEf,CACX;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEPzG,OAAA,CAAChB,IAAI;cAAC+H,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAnB,QAAA,eAChB7F,OAAA,CAACzB,SAAS;gBACR0J,SAAS;gBACTL,KAAK,EAAC,4GAAuB;gBAC7BO,KAAK,EAAEtH,QAAQ,CAACI,eAAgB;gBAChCmH,QAAQ,EAAGC,CAAC,IAAKvH,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEI,eAAe,EAAEoH,CAAC,CAACnF,MAAM,CAACiF;gBAAM,CAAC,CAAE;gBAC/EG,WAAW,EAAC;cAAuC;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPzG,OAAA,CAAChB,IAAI;cAAC+H,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAnB,QAAA,eAChB7F,OAAA,CAACzB,SAAS;gBACR0J,SAAS;gBACTM,SAAS;gBACTC,IAAI,EAAE,CAAE;gBACRZ,KAAK,EAAC,4GAAuB;gBAC7BO,KAAK,EAAEtH,QAAQ,CAACK,WAAY;gBAC5BkH,QAAQ,EAAGC,CAAC,IAAKvH,WAAW,CAAC;kBAAE,GAAGD,QAAQ;kBAAEK,WAAW,EAAEmH,CAAC,CAACnF,MAAM,CAACiF;gBAAM,CAAC,CAAE;gBAC3EG,WAAW,EAAC;cAAuC;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEPzG,OAAA,CAAChB,IAAI;cAAC+H,IAAI;cAACC,EAAE,EAAE,EAAG;cAAAnB,QAAA,gBAChB7F,OAAA,CAACZ,OAAO;gBAAC0G,EAAE,EAAE;kBAAE2C,EAAE,EAAE;gBAAE;cAAE;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC1BzG,OAAA,CAACtC,UAAU;gBAACyI,OAAO,EAAC,IAAI;gBAACL,EAAE,EAAE;kBAAEI,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,EAAC;cAExC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAEbzG,OAAA;gBACE0I,MAAM,EAAC,cAAc;gBACrBC,KAAK,EAAE;kBAAE5C,OAAO,EAAE;gBAAO,CAAE;gBAC3B9D,EAAE,EAAC,kBAAkB;gBACrBqB,IAAI,EAAC,MAAM;gBACX8E,QAAQ,EAAErF;cAAiB;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B,CAAC,eACFzG,OAAA;gBAAO4I,OAAO,EAAC,kBAAkB;gBAAA/C,QAAA,eAC/B7F,OAAA,CAACrC,MAAM;kBACLwI,OAAO,EAAC,UAAU;kBAClBqB,SAAS,EAAC,MAAM;kBAChBd,SAAS,eAAE1G,OAAA,CAACV,MAAM;oBAAAgH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACtBX,EAAE,EAAE;oBAAEI,EAAE,EAAE;kBAAE,CAAE;kBAAAL,QAAA,EACf;gBAED;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,EAEP9F,YAAY,iBACXX,OAAA,CAACjB,KAAK;gBAACwC,QAAQ,EAAC,SAAS;gBAACuE,EAAE,EAAE;kBAAE+C,EAAE,EAAE;gBAAE,CAAE;gBAAAhD,QAAA,GAAC,oFACtB,EAAClF,YAAY,CAAC+B,IAAI;cAAA;gBAAA4D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CACR,eAEDzG,OAAA,CAACtC,UAAU;gBAACyI,OAAO,EAAC,OAAO;gBAACE,KAAK,EAAC,eAAe;gBAACP,EAAE,EAAE;kBAAE+C,EAAE,EAAE;gBAAE,CAAE;gBAAAhD,QAAA,EAAC;cAEjE;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC,eAChBzG,OAAA,CAAC1B,aAAa;QAAAuH,QAAA,gBACZ7F,OAAA,CAACrC,MAAM;UAACgJ,OAAO,EAAE7D,iBAAkB;UAAA+C,QAAA,EAAC;QAAK;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClDzG,OAAA,CAACrC,MAAM;UAACgJ,OAAO,EAAEnD,YAAa;UAAC2C,OAAO,EAAC,WAAW;UAACO,SAAS,eAAE1G,OAAA,CAACV,MAAM;YAAAgH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAZ,QAAA,EAAC;QAE1E;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACvG,EAAA,CA9cID,qBAAqB;AAAA6I,EAAA,GAArB7I,qBAAqB;AAgd3B,eAAeA,qBAAqB;AAAC,IAAA6I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}