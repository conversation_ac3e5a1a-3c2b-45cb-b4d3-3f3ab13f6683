{"ast": null, "code": "/*!\n * Chart.js v4.5.0\n * https://www.chartjs.org\n * (c) 2025 Chart.js Contributors\n * Released under the MIT License\n */\nimport { Color } from '@kurkle/color';\n\n/**\n * @namespace Chart.helpers\n */ /**\n    * An empty function that can be used, for example, for optional callback.\n    */\nfunction noop() {\n  /* noop */}\n/**\n * Returns a unique id, sequentially generated from a global variable.\n */\nconst uid = (() => {\n  let id = 0;\n  return () => id++;\n})();\n/**\n * Returns true if `value` is neither null nor undefined, else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */\nfunction isNullOrUndef(value) {\n  return value === null || value === undefined;\n}\n/**\n * Returns true if `value` is an array (including typed arrays), else returns false.\n * @param value - The value to test.\n * @function\n */\nfunction isArray(value) {\n  if (Array.isArray && Array.isArray(value)) {\n    return true;\n  }\n  const type = Object.prototype.toString.call(value);\n  if (type.slice(0, 7) === '[object' && type.slice(-6) === 'Array]') {\n    return true;\n  }\n  return false;\n}\n/**\n * Returns true if `value` is an object (excluding null), else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */\nfunction isObject(value) {\n  return value !== null && Object.prototype.toString.call(value) === '[object Object]';\n}\n/**\n * Returns true if `value` is a finite number, else returns false\n * @param value  - The value to test.\n */\nfunction isNumberFinite(value) {\n  return (typeof value === 'number' || value instanceof Number) && isFinite(+value);\n}\n/**\n * Returns `value` if finite, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is not finite.\n */\nfunction finiteOrDefault(value, defaultValue) {\n  return isNumberFinite(value) ? value : defaultValue;\n}\n/**\n * Returns `value` if defined, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is undefined.\n */\nfunction valueOrDefault(value, defaultValue) {\n  return typeof value === 'undefined' ? defaultValue : value;\n}\nconst toPercentage = (value, dimension) => typeof value === 'string' && value.endsWith('%') ? parseFloat(value) / 100 : +value / dimension;\nconst toDimension = (value, dimension) => typeof value === 'string' && value.endsWith('%') ? parseFloat(value) / 100 * dimension : +value;\n/**\n * Calls `fn` with the given `args` in the scope defined by `thisArg` and returns the\n * value returned by `fn`. If `fn` is not a function, this method returns undefined.\n * @param fn - The function to call.\n * @param args - The arguments with which `fn` should be called.\n * @param [thisArg] - The value of `this` provided for the call to `fn`.\n */\nfunction callback(fn, args, thisArg) {\n  if (fn && typeof fn.call === 'function') {\n    return fn.apply(thisArg, args);\n  }\n}\nfunction each(loopable, fn, thisArg, reverse) {\n  let i, len, keys;\n  if (isArray(loopable)) {\n    len = loopable.length;\n    if (reverse) {\n      for (i = len - 1; i >= 0; i--) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    } else {\n      for (i = 0; i < len; i++) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    }\n  } else if (isObject(loopable)) {\n    keys = Object.keys(loopable);\n    len = keys.length;\n    for (i = 0; i < len; i++) {\n      fn.call(thisArg, loopable[keys[i]], keys[i]);\n    }\n  }\n}\n/**\n * Returns true if the `a0` and `a1` arrays have the same content, else returns false.\n * @param a0 - The array to compare\n * @param a1 - The array to compare\n * @private\n */\nfunction _elementsEqual(a0, a1) {\n  let i, ilen, v0, v1;\n  if (!a0 || !a1 || a0.length !== a1.length) {\n    return false;\n  }\n  for (i = 0, ilen = a0.length; i < ilen; ++i) {\n    v0 = a0[i];\n    v1 = a1[i];\n    if (v0.datasetIndex !== v1.datasetIndex || v0.index !== v1.index) {\n      return false;\n    }\n  }\n  return true;\n}\n/**\n * Returns a deep copy of `source` without keeping references on objects and arrays.\n * @param source - The value to clone.\n */\nfunction clone(source) {\n  if (isArray(source)) {\n    return source.map(clone);\n  }\n  if (isObject(source)) {\n    const target = Object.create(null);\n    const keys = Object.keys(source);\n    const klen = keys.length;\n    let k = 0;\n    for (; k < klen; ++k) {\n      target[keys[k]] = clone(source[keys[k]]);\n    }\n    return target;\n  }\n  return source;\n}\nfunction isValidKey(key) {\n  return ['__proto__', 'prototype', 'constructor'].indexOf(key) === -1;\n}\n/**\n * The default merger when Chart.helpers.merge is called without merger option.\n * Note(SB): also used by mergeConfig and mergeScaleConfig as fallback.\n * @private\n */\nfunction _merger(key, target, source, options) {\n  if (!isValidKey(key)) {\n    return;\n  }\n  const tval = target[key];\n  const sval = source[key];\n  if (isObject(tval) && isObject(sval)) {\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    merge(tval, sval, options);\n  } else {\n    target[key] = clone(sval);\n  }\n}\nfunction merge(target, source, options) {\n  const sources = isArray(source) ? source : [source];\n  const ilen = sources.length;\n  if (!isObject(target)) {\n    return target;\n  }\n  options = options || {};\n  const merger = options.merger || _merger;\n  let current;\n  for (let i = 0; i < ilen; ++i) {\n    current = sources[i];\n    if (!isObject(current)) {\n      continue;\n    }\n    const keys = Object.keys(current);\n    for (let k = 0, klen = keys.length; k < klen; ++k) {\n      merger(keys[k], target, current, options);\n    }\n  }\n  return target;\n}\nfunction mergeIf(target, source) {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  return merge(target, source, {\n    merger: _mergerIf\n  });\n}\n/**\n * Merges source[key] in target[key] only if target[key] is undefined.\n * @private\n */\nfunction _mergerIf(key, target, source) {\n  if (!isValidKey(key)) {\n    return;\n  }\n  const tval = target[key];\n  const sval = source[key];\n  if (isObject(tval) && isObject(sval)) {\n    mergeIf(tval, sval);\n  } else if (!Object.prototype.hasOwnProperty.call(target, key)) {\n    target[key] = clone(sval);\n  }\n}\n/**\n * @private\n */\nfunction _deprecated(scope, value, previous, current) {\n  if (value !== undefined) {\n    console.warn(scope + ': \"' + previous + '\" is deprecated. Please use \"' + current + '\" instead');\n  }\n}\n// resolveObjectKey resolver cache\nconst keyResolvers = {\n  // Chart.helpers.core resolveObjectKey should resolve empty key to root object\n  '': v => v,\n  // default resolvers\n  x: o => o.x,\n  y: o => o.y\n};\n/**\n * @private\n */\nfunction _splitKey(key) {\n  const parts = key.split('.');\n  const keys = [];\n  let tmp = '';\n  for (const part of parts) {\n    tmp += part;\n    if (tmp.endsWith('\\\\')) {\n      tmp = tmp.slice(0, -1) + '.';\n    } else {\n      keys.push(tmp);\n      tmp = '';\n    }\n  }\n  return keys;\n}\nfunction _getKeyResolver(key) {\n  const keys = _splitKey(key);\n  return obj => {\n    for (const k of keys) {\n      if (k === '') {\n        break;\n      }\n      obj = obj && obj[k];\n    }\n    return obj;\n  };\n}\nfunction resolveObjectKey(obj, key) {\n  const resolver = keyResolvers[key] || (keyResolvers[key] = _getKeyResolver(key));\n  return resolver(obj);\n}\n/**\n * @private\n */\nfunction _capitalize(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\nconst defined = value => typeof value !== 'undefined';\nconst isFunction = value => typeof value === 'function';\n// Adapted from https://stackoverflow.com/questions/31128855/comparing-ecma6-sets-for-equality#31129384\nconst setsEqual = (a, b) => {\n  if (a.size !== b.size) {\n    return false;\n  }\n  for (const item of a) {\n    if (!b.has(item)) {\n      return false;\n    }\n  }\n  return true;\n};\n/**\n * @param e - The event\n * @private\n */\nfunction _isClickEvent(e) {\n  return e.type === 'mouseup' || e.type === 'click' || e.type === 'contextmenu';\n}\n\n/**\n * @alias Chart.helpers.math\n * @namespace\n */\nconst PI = Math.PI;\nconst TAU = 2 * PI;\nconst PITAU = TAU + PI;\nconst INFINITY = Number.POSITIVE_INFINITY;\nconst RAD_PER_DEG = PI / 180;\nconst HALF_PI = PI / 2;\nconst QUARTER_PI = PI / 4;\nconst TWO_THIRDS_PI = PI * 2 / 3;\nconst log10 = Math.log10;\nconst sign = Math.sign;\nfunction almostEquals(x, y, epsilon) {\n  return Math.abs(x - y) < epsilon;\n}\n/**\n * Implementation of the nice number algorithm used in determining where axis labels will go\n */\nfunction niceNum(range) {\n  const roundedRange = Math.round(range);\n  range = almostEquals(range, roundedRange, range / 1000) ? roundedRange : range;\n  const niceRange = Math.pow(10, Math.floor(log10(range)));\n  const fraction = range / niceRange;\n  const niceFraction = fraction <= 1 ? 1 : fraction <= 2 ? 2 : fraction <= 5 ? 5 : 10;\n  return niceFraction * niceRange;\n}\n/**\n * Returns an array of factors sorted from 1 to sqrt(value)\n * @private\n */\nfunction _factorize(value) {\n  const result = [];\n  const sqrt = Math.sqrt(value);\n  let i;\n  for (i = 1; i < sqrt; i++) {\n    if (value % i === 0) {\n      result.push(i);\n      result.push(value / i);\n    }\n  }\n  if (sqrt === (sqrt | 0)) {\n    result.push(sqrt);\n  }\n  result.sort((a, b) => a - b).pop();\n  return result;\n}\n/**\n * Verifies that attempting to coerce n to string or number won't throw a TypeError.\n */\nfunction isNonPrimitive(n) {\n  return typeof n === 'symbol' || typeof n === 'object' && n !== null && !(Symbol.toPrimitive in n || 'toString' in n || 'valueOf' in n);\n}\nfunction isNumber(n) {\n  return !isNonPrimitive(n) && !isNaN(parseFloat(n)) && isFinite(n);\n}\nfunction almostWhole(x, epsilon) {\n  const rounded = Math.round(x);\n  return rounded - epsilon <= x && rounded + epsilon >= x;\n}\n/**\n * @private\n */\nfunction _setMinAndMaxByKey(array, target, property) {\n  let i, ilen, value;\n  for (i = 0, ilen = array.length; i < ilen; i++) {\n    value = array[i][property];\n    if (!isNaN(value)) {\n      target.min = Math.min(target.min, value);\n      target.max = Math.max(target.max, value);\n    }\n  }\n}\nfunction toRadians(degrees) {\n  return degrees * (PI / 180);\n}\nfunction toDegrees(radians) {\n  return radians * (180 / PI);\n}\n/**\n * Returns the number of decimal places\n * i.e. the number of digits after the decimal point, of the value of this Number.\n * @param x - A number.\n * @returns The number of decimal places.\n * @private\n */\nfunction _decimalPlaces(x) {\n  if (!isNumberFinite(x)) {\n    return;\n  }\n  let e = 1;\n  let p = 0;\n  while (Math.round(x * e) / e !== x) {\n    e *= 10;\n    p++;\n  }\n  return p;\n}\n// Gets the angle from vertical upright to the point about a centre.\nfunction getAngleFromPoint(centrePoint, anglePoint) {\n  const distanceFromXCenter = anglePoint.x - centrePoint.x;\n  const distanceFromYCenter = anglePoint.y - centrePoint.y;\n  const radialDistanceFromCenter = Math.sqrt(distanceFromXCenter * distanceFromXCenter + distanceFromYCenter * distanceFromYCenter);\n  let angle = Math.atan2(distanceFromYCenter, distanceFromXCenter);\n  if (angle < -0.5 * PI) {\n    angle += TAU; // make sure the returned angle is in the range of (-PI/2, 3PI/2]\n  }\n  return {\n    angle,\n    distance: radialDistanceFromCenter\n  };\n}\nfunction distanceBetweenPoints(pt1, pt2) {\n  return Math.sqrt(Math.pow(pt2.x - pt1.x, 2) + Math.pow(pt2.y - pt1.y, 2));\n}\n/**\n * Shortest distance between angles, in either direction.\n * @private\n */\nfunction _angleDiff(a, b) {\n  return (a - b + PITAU) % TAU - PI;\n}\n/**\n * Normalize angle to be between 0 and 2*PI\n * @private\n */\nfunction _normalizeAngle(a) {\n  return (a % TAU + TAU) % TAU;\n}\n/**\n * @private\n */\nfunction _angleBetween(angle, start, end, sameAngleIsFullCircle) {\n  const a = _normalizeAngle(angle);\n  const s = _normalizeAngle(start);\n  const e = _normalizeAngle(end);\n  const angleToStart = _normalizeAngle(s - a);\n  const angleToEnd = _normalizeAngle(e - a);\n  const startToAngle = _normalizeAngle(a - s);\n  const endToAngle = _normalizeAngle(a - e);\n  return a === s || a === e || sameAngleIsFullCircle && s === e || angleToStart > angleToEnd && startToAngle < endToAngle;\n}\n/**\n * Limit `value` between `min` and `max`\n * @param value\n * @param min\n * @param max\n * @private\n */\nfunction _limitValue(value, min, max) {\n  return Math.max(min, Math.min(max, value));\n}\n/**\n * @param {number} value\n * @private\n */\nfunction _int16Range(value) {\n  return _limitValue(value, -32768, 32767);\n}\n/**\n * @param value\n * @param start\n * @param end\n * @param [epsilon]\n * @private\n */\nfunction _isBetween(value, start, end, epsilon = 1e-6) {\n  return value >= Math.min(start, end) - epsilon && value <= Math.max(start, end) + epsilon;\n}\nfunction _lookup(table, value, cmp) {\n  cmp = cmp || (index => table[index] < value);\n  let hi = table.length - 1;\n  let lo = 0;\n  let mid;\n  while (hi - lo > 1) {\n    mid = lo + hi >> 1;\n    if (cmp(mid)) {\n      lo = mid;\n    } else {\n      hi = mid;\n    }\n  }\n  return {\n    lo,\n    hi\n  };\n}\n/**\n * Binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @param last - lookup last index\n * @private\n */\nconst _lookupByKey = (table, key, value, last) => _lookup(table, value, last ? index => {\n  const ti = table[index][key];\n  return ti < value || ti === value && table[index + 1][key] === value;\n} : index => table[index][key] < value);\n/**\n * Reverse binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @private\n */\nconst _rlookupByKey = (table, key, value) => _lookup(table, value, index => table[index][key] >= value);\n/**\n * Return subset of `values` between `min` and `max` inclusive.\n * Values are assumed to be in sorted order.\n * @param values - sorted array of values\n * @param min - min value\n * @param max - max value\n */\nfunction _filterBetween(values, min, max) {\n  let start = 0;\n  let end = values.length;\n  while (start < end && values[start] < min) {\n    start++;\n  }\n  while (end > start && values[end - 1] > max) {\n    end--;\n  }\n  return start > 0 || end < values.length ? values.slice(start, end) : values;\n}\nconst arrayEvents = ['push', 'pop', 'shift', 'splice', 'unshift'];\nfunction listenArrayEvents(array, listener) {\n  if (array._chartjs) {\n    array._chartjs.listeners.push(listener);\n    return;\n  }\n  Object.defineProperty(array, '_chartjs', {\n    configurable: true,\n    enumerable: false,\n    value: {\n      listeners: [listener]\n    }\n  });\n  arrayEvents.forEach(key => {\n    const method = '_onData' + _capitalize(key);\n    const base = array[key];\n    Object.defineProperty(array, key, {\n      configurable: true,\n      enumerable: false,\n      value(...args) {\n        const res = base.apply(this, args);\n        array._chartjs.listeners.forEach(object => {\n          if (typeof object[method] === 'function') {\n            object[method](...args);\n          }\n        });\n        return res;\n      }\n    });\n  });\n}\nfunction unlistenArrayEvents(array, listener) {\n  const stub = array._chartjs;\n  if (!stub) {\n    return;\n  }\n  const listeners = stub.listeners;\n  const index = listeners.indexOf(listener);\n  if (index !== -1) {\n    listeners.splice(index, 1);\n  }\n  if (listeners.length > 0) {\n    return;\n  }\n  arrayEvents.forEach(key => {\n    delete array[key];\n  });\n  delete array._chartjs;\n}\n/**\n * @param items\n */\nfunction _arrayUnique(items) {\n  const set = new Set(items);\n  if (set.size === items.length) {\n    return items;\n  }\n  return Array.from(set);\n}\nfunction fontString(pixelSize, fontStyle, fontFamily) {\n  return fontStyle + ' ' + pixelSize + 'px ' + fontFamily;\n}\n/**\n* Request animation polyfill\n*/\nconst requestAnimFrame = function () {\n  if (typeof window === 'undefined') {\n    return function (callback) {\n      return callback();\n    };\n  }\n  return window.requestAnimationFrame;\n}();\n/**\n * Throttles calling `fn` once per animation frame\n * Latest arguments are used on the actual call\n */\nfunction throttled(fn, thisArg) {\n  let argsToUse = [];\n  let ticking = false;\n  return function (...args) {\n    // Save the args for use later\n    argsToUse = args;\n    if (!ticking) {\n      ticking = true;\n      requestAnimFrame.call(window, () => {\n        ticking = false;\n        fn.apply(thisArg, argsToUse);\n      });\n    }\n  };\n}\n/**\n * Debounces calling `fn` for `delay` ms\n */\nfunction debounce(fn, delay) {\n  let timeout;\n  return function (...args) {\n    if (delay) {\n      clearTimeout(timeout);\n      timeout = setTimeout(fn, delay, args);\n    } else {\n      fn.apply(this, args);\n    }\n    return delay;\n  };\n}\n/**\n * Converts 'start' to 'left', 'end' to 'right' and others to 'center'\n * @private\n */\nconst _toLeftRightCenter = align => align === 'start' ? 'left' : align === 'end' ? 'right' : 'center';\n/**\n * Returns `start`, `end` or `(start + end) / 2` depending on `align`. Defaults to `center`\n * @private\n */\nconst _alignStartEnd = (align, start, end) => align === 'start' ? start : align === 'end' ? end : (start + end) / 2;\n/**\n * Returns `left`, `right` or `(left + right) / 2` depending on `align`. Defaults to `left`\n * @private\n */\nconst _textX = (align, left, right, rtl) => {\n  const check = rtl ? 'left' : 'right';\n  return align === check ? right : align === 'center' ? (left + right) / 2 : left;\n};\n/**\n * Return start and count of visible points.\n * @private\n */\nfunction _getStartAndCountOfVisiblePoints(meta, points, animationsDisabled) {\n  const pointCount = points.length;\n  let start = 0;\n  let count = pointCount;\n  if (meta._sorted) {\n    const {\n      iScale,\n      vScale,\n      _parsed\n    } = meta;\n    const spanGaps = meta.dataset ? meta.dataset.options ? meta.dataset.options.spanGaps : null : null;\n    const axis = iScale.axis;\n    const {\n      min,\n      max,\n      minDefined,\n      maxDefined\n    } = iScale.getUserBounds();\n    if (minDefined) {\n      start = Math.min(\n      // @ts-expect-error Need to type _parsed\n      _lookupByKey(_parsed, axis, min).lo,\n      // @ts-expect-error Need to fix types on _lookupByKey\n      animationsDisabled ? pointCount : _lookupByKey(points, axis, iScale.getPixelForValue(min)).lo);\n      if (spanGaps) {\n        const distanceToDefinedLo = _parsed.slice(0, start + 1).reverse().findIndex(point => !isNullOrUndef(point[vScale.axis]));\n        start -= Math.max(0, distanceToDefinedLo);\n      }\n      start = _limitValue(start, 0, pointCount - 1);\n    }\n    if (maxDefined) {\n      let end = Math.max(\n      // @ts-expect-error Need to type _parsed\n      _lookupByKey(_parsed, iScale.axis, max, true).hi + 1,\n      // @ts-expect-error Need to fix types on _lookupByKey\n      animationsDisabled ? 0 : _lookupByKey(points, axis, iScale.getPixelForValue(max), true).hi + 1);\n      if (spanGaps) {\n        const distanceToDefinedHi = _parsed.slice(end - 1).findIndex(point => !isNullOrUndef(point[vScale.axis]));\n        end += Math.max(0, distanceToDefinedHi);\n      }\n      count = _limitValue(end, start, pointCount) - start;\n    } else {\n      count = pointCount - start;\n    }\n  }\n  return {\n    start,\n    count\n  };\n}\n/**\n * Checks if the scale ranges have changed.\n * @param {object} meta - dataset meta.\n * @returns {boolean}\n * @private\n */\nfunction _scaleRangesChanged(meta) {\n  const {\n    xScale,\n    yScale,\n    _scaleRanges\n  } = meta;\n  const newRanges = {\n    xmin: xScale.min,\n    xmax: xScale.max,\n    ymin: yScale.min,\n    ymax: yScale.max\n  };\n  if (!_scaleRanges) {\n    meta._scaleRanges = newRanges;\n    return true;\n  }\n  const changed = _scaleRanges.xmin !== xScale.min || _scaleRanges.xmax !== xScale.max || _scaleRanges.ymin !== yScale.min || _scaleRanges.ymax !== yScale.max;\n  Object.assign(_scaleRanges, newRanges);\n  return changed;\n}\nconst atEdge = t => t === 0 || t === 1;\nconst elasticIn = (t, s, p) => -(Math.pow(2, 10 * (t -= 1)) * Math.sin((t - s) * TAU / p));\nconst elasticOut = (t, s, p) => Math.pow(2, -10 * t) * Math.sin((t - s) * TAU / p) + 1;\n/**\n * Easing functions adapted from Robert Penner's easing equations.\n * @namespace Chart.helpers.easing.effects\n * @see http://www.robertpenner.com/easing/\n */\nconst effects = {\n  linear: t => t,\n  easeInQuad: t => t * t,\n  easeOutQuad: t => -t * (t - 2),\n  easeInOutQuad: t => (t /= 0.5) < 1 ? 0.5 * t * t : -0.5 * (--t * (t - 2) - 1),\n  easeInCubic: t => t * t * t,\n  easeOutCubic: t => (t -= 1) * t * t + 1,\n  easeInOutCubic: t => (t /= 0.5) < 1 ? 0.5 * t * t * t : 0.5 * ((t -= 2) * t * t + 2),\n  easeInQuart: t => t * t * t * t,\n  easeOutQuart: t => -((t -= 1) * t * t * t - 1),\n  easeInOutQuart: t => (t /= 0.5) < 1 ? 0.5 * t * t * t * t : -0.5 * ((t -= 2) * t * t * t - 2),\n  easeInQuint: t => t * t * t * t * t,\n  easeOutQuint: t => (t -= 1) * t * t * t * t + 1,\n  easeInOutQuint: t => (t /= 0.5) < 1 ? 0.5 * t * t * t * t * t : 0.5 * ((t -= 2) * t * t * t * t + 2),\n  easeInSine: t => -Math.cos(t * HALF_PI) + 1,\n  easeOutSine: t => Math.sin(t * HALF_PI),\n  easeInOutSine: t => -0.5 * (Math.cos(PI * t) - 1),\n  easeInExpo: t => t === 0 ? 0 : Math.pow(2, 10 * (t - 1)),\n  easeOutExpo: t => t === 1 ? 1 : -Math.pow(2, -10 * t) + 1,\n  easeInOutExpo: t => atEdge(t) ? t : t < 0.5 ? 0.5 * Math.pow(2, 10 * (t * 2 - 1)) : 0.5 * (-Math.pow(2, -10 * (t * 2 - 1)) + 2),\n  easeInCirc: t => t >= 1 ? t : -(Math.sqrt(1 - t * t) - 1),\n  easeOutCirc: t => Math.sqrt(1 - (t -= 1) * t),\n  easeInOutCirc: t => (t /= 0.5) < 1 ? -0.5 * (Math.sqrt(1 - t * t) - 1) : 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1),\n  easeInElastic: t => atEdge(t) ? t : elasticIn(t, 0.075, 0.3),\n  easeOutElastic: t => atEdge(t) ? t : elasticOut(t, 0.075, 0.3),\n  easeInOutElastic(t) {\n    const s = 0.1125;\n    const p = 0.45;\n    return atEdge(t) ? t : t < 0.5 ? 0.5 * elasticIn(t * 2, s, p) : 0.5 + 0.5 * elasticOut(t * 2 - 1, s, p);\n  },\n  easeInBack(t) {\n    const s = 1.70158;\n    return t * t * ((s + 1) * t - s);\n  },\n  easeOutBack(t) {\n    const s = 1.70158;\n    return (t -= 1) * t * ((s + 1) * t + s) + 1;\n  },\n  easeInOutBack(t) {\n    let s = 1.70158;\n    if ((t /= 0.5) < 1) {\n      return 0.5 * (t * t * (((s *= 1.525) + 1) * t - s));\n    }\n    return 0.5 * ((t -= 2) * t * (((s *= 1.525) + 1) * t + s) + 2);\n  },\n  easeInBounce: t => 1 - effects.easeOutBounce(1 - t),\n  easeOutBounce(t) {\n    const m = 7.5625;\n    const d = 2.75;\n    if (t < 1 / d) {\n      return m * t * t;\n    }\n    if (t < 2 / d) {\n      return m * (t -= 1.5 / d) * t + 0.75;\n    }\n    if (t < 2.5 / d) {\n      return m * (t -= 2.25 / d) * t + 0.9375;\n    }\n    return m * (t -= 2.625 / d) * t + 0.984375;\n  },\n  easeInOutBounce: t => t < 0.5 ? effects.easeInBounce(t * 2) * 0.5 : effects.easeOutBounce(t * 2 - 1) * 0.5 + 0.5\n};\nfunction isPatternOrGradient(value) {\n  if (value && typeof value === 'object') {\n    const type = value.toString();\n    return type === '[object CanvasPattern]' || type === '[object CanvasGradient]';\n  }\n  return false;\n}\nfunction color(value) {\n  return isPatternOrGradient(value) ? value : new Color(value);\n}\nfunction getHoverColor(value) {\n  return isPatternOrGradient(value) ? value : new Color(value).saturate(0.5).darken(0.1).hexString();\n}\nconst numbers = ['x', 'y', 'borderWidth', 'radius', 'tension'];\nconst colors = ['color', 'borderColor', 'backgroundColor'];\nfunction applyAnimationsDefaults(defaults) {\n  defaults.set('animation', {\n    delay: undefined,\n    duration: 1000,\n    easing: 'easeOutQuart',\n    fn: undefined,\n    from: undefined,\n    loop: undefined,\n    to: undefined,\n    type: undefined\n  });\n  defaults.describe('animation', {\n    _fallback: false,\n    _indexable: false,\n    _scriptable: name => name !== 'onProgress' && name !== 'onComplete' && name !== 'fn'\n  });\n  defaults.set('animations', {\n    colors: {\n      type: 'color',\n      properties: colors\n    },\n    numbers: {\n      type: 'number',\n      properties: numbers\n    }\n  });\n  defaults.describe('animations', {\n    _fallback: 'animation'\n  });\n  defaults.set('transitions', {\n    active: {\n      animation: {\n        duration: 400\n      }\n    },\n    resize: {\n      animation: {\n        duration: 0\n      }\n    },\n    show: {\n      animations: {\n        colors: {\n          from: 'transparent'\n        },\n        visible: {\n          type: 'boolean',\n          duration: 0\n        }\n      }\n    },\n    hide: {\n      animations: {\n        colors: {\n          to: 'transparent'\n        },\n        visible: {\n          type: 'boolean',\n          easing: 'linear',\n          fn: v => v | 0\n        }\n      }\n    }\n  });\n}\nfunction applyLayoutsDefaults(defaults) {\n  defaults.set('layout', {\n    autoPadding: true,\n    padding: {\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0\n    }\n  });\n}\nconst intlCache = new Map();\nfunction getNumberFormat(locale, options) {\n  options = options || {};\n  const cacheKey = locale + JSON.stringify(options);\n  let formatter = intlCache.get(cacheKey);\n  if (!formatter) {\n    formatter = new Intl.NumberFormat(locale, options);\n    intlCache.set(cacheKey, formatter);\n  }\n  return formatter;\n}\nfunction formatNumber(num, locale, options) {\n  return getNumberFormat(locale, options).format(num);\n}\nconst formatters = {\n  values(value) {\n    return isArray(value) ? value : '' + value;\n  },\n  numeric(tickValue, index, ticks) {\n    if (tickValue === 0) {\n      return '0';\n    }\n    const locale = this.chart.options.locale;\n    let notation;\n    let delta = tickValue;\n    if (ticks.length > 1) {\n      const maxTick = Math.max(Math.abs(ticks[0].value), Math.abs(ticks[ticks.length - 1].value));\n      if (maxTick < 1e-4 || maxTick > 1e+15) {\n        notation = 'scientific';\n      }\n      delta = calculateDelta(tickValue, ticks);\n    }\n    const logDelta = log10(Math.abs(delta));\n    const numDecimal = isNaN(logDelta) ? 1 : Math.max(Math.min(-1 * Math.floor(logDelta), 20), 0);\n    const options = {\n      notation,\n      minimumFractionDigits: numDecimal,\n      maximumFractionDigits: numDecimal\n    };\n    Object.assign(options, this.options.ticks.format);\n    return formatNumber(tickValue, locale, options);\n  },\n  logarithmic(tickValue, index, ticks) {\n    if (tickValue === 0) {\n      return '0';\n    }\n    const remain = ticks[index].significand || tickValue / Math.pow(10, Math.floor(log10(tickValue)));\n    if ([1, 2, 3, 5, 10, 15].includes(remain) || index > 0.8 * ticks.length) {\n      return formatters.numeric.call(this, tickValue, index, ticks);\n    }\n    return '';\n  }\n};\nfunction calculateDelta(tickValue, ticks) {\n  let delta = ticks.length > 3 ? ticks[2].value - ticks[1].value : ticks[1].value - ticks[0].value;\n  if (Math.abs(delta) >= 1 && tickValue !== Math.floor(tickValue)) {\n    delta = tickValue - Math.floor(tickValue);\n  }\n  return delta;\n}\nvar Ticks = {\n  formatters\n};\nfunction applyScaleDefaults(defaults) {\n  defaults.set('scale', {\n    display: true,\n    offset: false,\n    reverse: false,\n    beginAtZero: false,\n    bounds: 'ticks',\n    clip: true,\n    grace: 0,\n    grid: {\n      display: true,\n      lineWidth: 1,\n      drawOnChartArea: true,\n      drawTicks: true,\n      tickLength: 8,\n      tickWidth: (_ctx, options) => options.lineWidth,\n      tickColor: (_ctx, options) => options.color,\n      offset: false\n    },\n    border: {\n      display: true,\n      dash: [],\n      dashOffset: 0.0,\n      width: 1\n    },\n    title: {\n      display: false,\n      text: '',\n      padding: {\n        top: 4,\n        bottom: 4\n      }\n    },\n    ticks: {\n      minRotation: 0,\n      maxRotation: 50,\n      mirror: false,\n      textStrokeWidth: 0,\n      textStrokeColor: '',\n      padding: 3,\n      display: true,\n      autoSkip: true,\n      autoSkipPadding: 3,\n      labelOffset: 0,\n      callback: Ticks.formatters.values,\n      minor: {},\n      major: {},\n      align: 'center',\n      crossAlign: 'near',\n      showLabelBackdrop: false,\n      backdropColor: 'rgba(255, 255, 255, 0.75)',\n      backdropPadding: 2\n    }\n  });\n  defaults.route('scale.ticks', 'color', '', 'color');\n  defaults.route('scale.grid', 'color', '', 'borderColor');\n  defaults.route('scale.border', 'color', '', 'borderColor');\n  defaults.route('scale.title', 'color', '', 'color');\n  defaults.describe('scale', {\n    _fallback: false,\n    _scriptable: name => !name.startsWith('before') && !name.startsWith('after') && name !== 'callback' && name !== 'parser',\n    _indexable: name => name !== 'borderDash' && name !== 'tickBorderDash' && name !== 'dash'\n  });\n  defaults.describe('scales', {\n    _fallback: 'scale'\n  });\n  defaults.describe('scale.ticks', {\n    _scriptable: name => name !== 'backdropPadding' && name !== 'callback',\n    _indexable: name => name !== 'backdropPadding'\n  });\n}\nconst overrides = Object.create(null);\nconst descriptors = Object.create(null);\nfunction getScope$1(node, key) {\n  if (!key) {\n    return node;\n  }\n  const keys = key.split('.');\n  for (let i = 0, n = keys.length; i < n; ++i) {\n    const k = keys[i];\n    node = node[k] || (node[k] = Object.create(null));\n  }\n  return node;\n}\nfunction set(root, scope, values) {\n  if (typeof scope === 'string') {\n    return merge(getScope$1(root, scope), values);\n  }\n  return merge(getScope$1(root, ''), scope);\n}\nclass Defaults {\n  constructor(_descriptors, _appliers) {\n    this.animation = undefined;\n    this.backgroundColor = 'rgba(0,0,0,0.1)';\n    this.borderColor = 'rgba(0,0,0,0.1)';\n    this.color = '#666';\n    this.datasets = {};\n    this.devicePixelRatio = context => context.chart.platform.getDevicePixelRatio();\n    this.elements = {};\n    this.events = ['mousemove', 'mouseout', 'click', 'touchstart', 'touchmove'];\n    this.font = {\n      family: \"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif\",\n      size: 12,\n      style: 'normal',\n      lineHeight: 1.2,\n      weight: null\n    };\n    this.hover = {};\n    this.hoverBackgroundColor = (ctx, options) => getHoverColor(options.backgroundColor);\n    this.hoverBorderColor = (ctx, options) => getHoverColor(options.borderColor);\n    this.hoverColor = (ctx, options) => getHoverColor(options.color);\n    this.indexAxis = 'x';\n    this.interaction = {\n      mode: 'nearest',\n      intersect: true,\n      includeInvisible: false\n    };\n    this.maintainAspectRatio = true;\n    this.onHover = null;\n    this.onClick = null;\n    this.parsing = true;\n    this.plugins = {};\n    this.responsive = true;\n    this.scale = undefined;\n    this.scales = {};\n    this.showLine = true;\n    this.drawActiveElementsOnTop = true;\n    this.describe(_descriptors);\n    this.apply(_appliers);\n  }\n  set(scope, values) {\n    return set(this, scope, values);\n  }\n  get(scope) {\n    return getScope$1(this, scope);\n  }\n  describe(scope, values) {\n    return set(descriptors, scope, values);\n  }\n  override(scope, values) {\n    return set(overrides, scope, values);\n  }\n  route(scope, name, targetScope, targetName) {\n    const scopeObject = getScope$1(this, scope);\n    const targetScopeObject = getScope$1(this, targetScope);\n    const privateName = '_' + name;\n    Object.defineProperties(scopeObject, {\n      [privateName]: {\n        value: scopeObject[name],\n        writable: true\n      },\n      [name]: {\n        enumerable: true,\n        get() {\n          const local = this[privateName];\n          const target = targetScopeObject[targetName];\n          if (isObject(local)) {\n            return Object.assign({}, target, local);\n          }\n          return valueOrDefault(local, target);\n        },\n        set(value) {\n          this[privateName] = value;\n        }\n      }\n    });\n  }\n  apply(appliers) {\n    appliers.forEach(apply => apply(this));\n  }\n}\nvar defaults = /* #__PURE__ */new Defaults({\n  _scriptable: name => !name.startsWith('on'),\n  _indexable: name => name !== 'events',\n  hover: {\n    _fallback: 'interaction'\n  },\n  interaction: {\n    _scriptable: false,\n    _indexable: false\n  }\n}, [applyAnimationsDefaults, applyLayoutsDefaults, applyScaleDefaults]);\n\n/**\n * Converts the given font object into a CSS font string.\n * @param font - A font object.\n * @return The CSS font string. See https://developer.mozilla.org/en-US/docs/Web/CSS/font\n * @private\n */\nfunction toFontString(font) {\n  if (!font || isNullOrUndef(font.size) || isNullOrUndef(font.family)) {\n    return null;\n  }\n  return (font.style ? font.style + ' ' : '') + (font.weight ? font.weight + ' ' : '') + font.size + 'px ' + font.family;\n}\n/**\n * @private\n */\nfunction _measureText(ctx, data, gc, longest, string) {\n  let textWidth = data[string];\n  if (!textWidth) {\n    textWidth = data[string] = ctx.measureText(string).width;\n    gc.push(string);\n  }\n  if (textWidth > longest) {\n    longest = textWidth;\n  }\n  return longest;\n}\n/**\n * @private\n */ // eslint-disable-next-line complexity\nfunction _longestText(ctx, font, arrayOfThings, cache) {\n  cache = cache || {};\n  let data = cache.data = cache.data || {};\n  let gc = cache.garbageCollect = cache.garbageCollect || [];\n  if (cache.font !== font) {\n    data = cache.data = {};\n    gc = cache.garbageCollect = [];\n    cache.font = font;\n  }\n  ctx.save();\n  ctx.font = font;\n  let longest = 0;\n  const ilen = arrayOfThings.length;\n  let i, j, jlen, thing, nestedThing;\n  for (i = 0; i < ilen; i++) {\n    thing = arrayOfThings[i];\n    // Undefined strings and arrays should not be measured\n    if (thing !== undefined && thing !== null && !isArray(thing)) {\n      longest = _measureText(ctx, data, gc, longest, thing);\n    } else if (isArray(thing)) {\n      // if it is an array lets measure each element\n      // to do maybe simplify this function a bit so we can do this more recursively?\n      for (j = 0, jlen = thing.length; j < jlen; j++) {\n        nestedThing = thing[j];\n        // Undefined strings and arrays should not be measured\n        if (nestedThing !== undefined && nestedThing !== null && !isArray(nestedThing)) {\n          longest = _measureText(ctx, data, gc, longest, nestedThing);\n        }\n      }\n    }\n  }\n  ctx.restore();\n  const gcLen = gc.length / 2;\n  if (gcLen > arrayOfThings.length) {\n    for (i = 0; i < gcLen; i++) {\n      delete data[gc[i]];\n    }\n    gc.splice(0, gcLen);\n  }\n  return longest;\n}\n/**\n * Returns the aligned pixel value to avoid anti-aliasing blur\n * @param chart - The chart instance.\n * @param pixel - A pixel value.\n * @param width - The width of the element.\n * @returns The aligned pixel value.\n * @private\n */\nfunction _alignPixel(chart, pixel, width) {\n  const devicePixelRatio = chart.currentDevicePixelRatio;\n  const halfWidth = width !== 0 ? Math.max(width / 2, 0.5) : 0;\n  return Math.round((pixel - halfWidth) * devicePixelRatio) / devicePixelRatio + halfWidth;\n}\n/**\n * Clears the entire canvas.\n */\nfunction clearCanvas(canvas, ctx) {\n  if (!ctx && !canvas) {\n    return;\n  }\n  ctx = ctx || canvas.getContext('2d');\n  ctx.save();\n  // canvas.width and canvas.height do not consider the canvas transform,\n  // while clearRect does\n  ctx.resetTransform();\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.restore();\n}\nfunction drawPoint(ctx, options, x, y) {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  drawPointLegend(ctx, options, x, y, null);\n}\n// eslint-disable-next-line complexity\nfunction drawPointLegend(ctx, options, x, y, w) {\n  let type, xOffset, yOffset, size, cornerRadius, width, xOffsetW, yOffsetW;\n  const style = options.pointStyle;\n  const rotation = options.rotation;\n  const radius = options.radius;\n  let rad = (rotation || 0) * RAD_PER_DEG;\n  if (style && typeof style === 'object') {\n    type = style.toString();\n    if (type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]') {\n      ctx.save();\n      ctx.translate(x, y);\n      ctx.rotate(rad);\n      ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n      ctx.restore();\n      return;\n    }\n  }\n  if (isNaN(radius) || radius <= 0) {\n    return;\n  }\n  ctx.beginPath();\n  switch (style) {\n    // Default includes circle\n    default:\n      if (w) {\n        ctx.ellipse(x, y, w / 2, radius, 0, 0, TAU);\n      } else {\n        ctx.arc(x, y, radius, 0, TAU);\n      }\n      ctx.closePath();\n      break;\n    case 'triangle':\n      width = w ? w / 2 : radius;\n      ctx.moveTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      ctx.closePath();\n      break;\n    case 'rectRounded':\n      // NOTE: the rounded rect implementation changed to use `arc` instead of\n      // `quadraticCurveTo` since it generates better results when rect is\n      // almost a circle. 0.516 (instead of 0.5) produces results with visually\n      // closer proportion to the previous impl and it is inscribed in the\n      // circle with `radius`. For more details, see the following PRs:\n      // https://github.com/chartjs/Chart.js/issues/5597\n      // https://github.com/chartjs/Chart.js/issues/5858\n      cornerRadius = radius * 0.516;\n      size = radius - cornerRadius;\n      xOffset = Math.cos(rad + QUARTER_PI) * size;\n      xOffsetW = Math.cos(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n      yOffset = Math.sin(rad + QUARTER_PI) * size;\n      yOffsetW = Math.sin(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n      ctx.arc(x - xOffsetW, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n      ctx.arc(x + yOffsetW, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n      ctx.arc(x + xOffsetW, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n      ctx.arc(x - yOffsetW, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n      ctx.closePath();\n      break;\n    case 'rect':\n      if (!rotation) {\n        size = Math.SQRT1_2 * radius;\n        width = w ? w / 2 : size;\n        ctx.rect(x - width, y - size, 2 * width, 2 * size);\n        break;\n      }\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'rectRot':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      ctx.closePath();\n      break;\n    case 'crossRot':\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'cross':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      break;\n    case 'star':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      rad += QUARTER_PI;\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      break;\n    case 'line':\n      xOffset = w ? w / 2 : Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      break;\n    case 'dash':\n      ctx.moveTo(x, y);\n      ctx.lineTo(x + Math.cos(rad) * (w ? w / 2 : radius), y + Math.sin(rad) * radius);\n      break;\n    case false:\n      ctx.closePath();\n      break;\n  }\n  ctx.fill();\n  if (options.borderWidth > 0) {\n    ctx.stroke();\n  }\n}\n/**\n * Returns true if the point is inside the rectangle\n * @param point - The point to test\n * @param area - The rectangle\n * @param margin - allowed margin\n * @private\n */\nfunction _isPointInArea(point, area, margin) {\n  margin = margin || 0.5; // margin - default is to match rounded decimals\n  return !area || point && point.x > area.left - margin && point.x < area.right + margin && point.y > area.top - margin && point.y < area.bottom + margin;\n}\nfunction clipArea(ctx, area) {\n  ctx.save();\n  ctx.beginPath();\n  ctx.rect(area.left, area.top, area.right - area.left, area.bottom - area.top);\n  ctx.clip();\n}\nfunction unclipArea(ctx) {\n  ctx.restore();\n}\n/**\n * @private\n */\nfunction _steppedLineTo(ctx, previous, target, flip, mode) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  if (mode === 'middle') {\n    const midpoint = (previous.x + target.x) / 2.0;\n    ctx.lineTo(midpoint, previous.y);\n    ctx.lineTo(midpoint, target.y);\n  } else if (mode === 'after' !== !!flip) {\n    ctx.lineTo(previous.x, target.y);\n  } else {\n    ctx.lineTo(target.x, previous.y);\n  }\n  ctx.lineTo(target.x, target.y);\n}\n/**\n * @private\n */\nfunction _bezierCurveTo(ctx, previous, target, flip) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  ctx.bezierCurveTo(flip ? previous.cp1x : previous.cp2x, flip ? previous.cp1y : previous.cp2y, flip ? target.cp2x : target.cp1x, flip ? target.cp2y : target.cp1y, target.x, target.y);\n}\nfunction setRenderOpts(ctx, opts) {\n  if (opts.translation) {\n    ctx.translate(opts.translation[0], opts.translation[1]);\n  }\n  if (!isNullOrUndef(opts.rotation)) {\n    ctx.rotate(opts.rotation);\n  }\n  if (opts.color) {\n    ctx.fillStyle = opts.color;\n  }\n  if (opts.textAlign) {\n    ctx.textAlign = opts.textAlign;\n  }\n  if (opts.textBaseline) {\n    ctx.textBaseline = opts.textBaseline;\n  }\n}\nfunction decorateText(ctx, x, y, line, opts) {\n  if (opts.strikethrough || opts.underline) {\n    /**\n    * Now that IE11 support has been dropped, we can use more\n    * of the TextMetrics object. The actual bounding boxes\n    * are unflagged in Chrome, Firefox, Edge, and Safari so they\n    * can be safely used.\n    * See https://developer.mozilla.org/en-US/docs/Web/API/TextMetrics#Browser_compatibility\n    */\n    const metrics = ctx.measureText(line);\n    const left = x - metrics.actualBoundingBoxLeft;\n    const right = x + metrics.actualBoundingBoxRight;\n    const top = y - metrics.actualBoundingBoxAscent;\n    const bottom = y + metrics.actualBoundingBoxDescent;\n    const yDecoration = opts.strikethrough ? (top + bottom) / 2 : bottom;\n    ctx.strokeStyle = ctx.fillStyle;\n    ctx.beginPath();\n    ctx.lineWidth = opts.decorationWidth || 2;\n    ctx.moveTo(left, yDecoration);\n    ctx.lineTo(right, yDecoration);\n    ctx.stroke();\n  }\n}\nfunction drawBackdrop(ctx, opts) {\n  const oldColor = ctx.fillStyle;\n  ctx.fillStyle = opts.color;\n  ctx.fillRect(opts.left, opts.top, opts.width, opts.height);\n  ctx.fillStyle = oldColor;\n}\n/**\n * Render text onto the canvas\n */\nfunction renderText(ctx, text, x, y, font, opts = {}) {\n  const lines = isArray(text) ? text : [text];\n  const stroke = opts.strokeWidth > 0 && opts.strokeColor !== '';\n  let i, line;\n  ctx.save();\n  ctx.font = font.string;\n  setRenderOpts(ctx, opts);\n  for (i = 0; i < lines.length; ++i) {\n    line = lines[i];\n    if (opts.backdrop) {\n      drawBackdrop(ctx, opts.backdrop);\n    }\n    if (stroke) {\n      if (opts.strokeColor) {\n        ctx.strokeStyle = opts.strokeColor;\n      }\n      if (!isNullOrUndef(opts.strokeWidth)) {\n        ctx.lineWidth = opts.strokeWidth;\n      }\n      ctx.strokeText(line, x, y, opts.maxWidth);\n    }\n    ctx.fillText(line, x, y, opts.maxWidth);\n    decorateText(ctx, x, y, line, opts);\n    y += Number(font.lineHeight);\n  }\n  ctx.restore();\n}\n/**\n * Add a path of a rectangle with rounded corners to the current sub-path\n * @param ctx - Context\n * @param rect - Bounding rect\n */\nfunction addRoundedRectPath(ctx, rect) {\n  const {\n    x,\n    y,\n    w,\n    h,\n    radius\n  } = rect;\n  // top left arc\n  ctx.arc(x + radius.topLeft, y + radius.topLeft, radius.topLeft, 1.5 * PI, PI, true);\n  // line from top left to bottom left\n  ctx.lineTo(x, y + h - radius.bottomLeft);\n  // bottom left arc\n  ctx.arc(x + radius.bottomLeft, y + h - radius.bottomLeft, radius.bottomLeft, PI, HALF_PI, true);\n  // line from bottom left to bottom right\n  ctx.lineTo(x + w - radius.bottomRight, y + h);\n  // bottom right arc\n  ctx.arc(x + w - radius.bottomRight, y + h - radius.bottomRight, radius.bottomRight, HALF_PI, 0, true);\n  // line from bottom right to top right\n  ctx.lineTo(x + w, y + radius.topRight);\n  // top right arc\n  ctx.arc(x + w - radius.topRight, y + radius.topRight, radius.topRight, 0, -HALF_PI, true);\n  // line from top right to top left\n  ctx.lineTo(x + radius.topLeft, y);\n}\nconst LINE_HEIGHT = /^(normal|(\\d+(?:\\.\\d+)?)(px|em|%)?)$/;\nconst FONT_STYLE = /^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;\n/**\n * @alias Chart.helpers.options\n * @namespace\n */ /**\n    * Converts the given line height `value` in pixels for a specific font `size`.\n    * @param value - The lineHeight to parse (eg. 1.6, '14px', '75%', '1.6em').\n    * @param size - The font size (in pixels) used to resolve relative `value`.\n    * @returns The effective line height in pixels (size * 1.2 if value is invalid).\n    * @see https://developer.mozilla.org/en-US/docs/Web/CSS/line-height\n    * @since 2.7.0\n    */\nfunction toLineHeight(value, size) {\n  const matches = ('' + value).match(LINE_HEIGHT);\n  if (!matches || matches[1] === 'normal') {\n    return size * 1.2;\n  }\n  value = +matches[2];\n  switch (matches[3]) {\n    case 'px':\n      return value;\n    case '%':\n      value /= 100;\n      break;\n  }\n  return size * value;\n}\nconst numberOrZero = v => +v || 0;\nfunction _readValueToProps(value, props) {\n  const ret = {};\n  const objProps = isObject(props);\n  const keys = objProps ? Object.keys(props) : props;\n  const read = isObject(value) ? objProps ? prop => valueOrDefault(value[prop], value[props[prop]]) : prop => value[prop] : () => value;\n  for (const prop of keys) {\n    ret[prop] = numberOrZero(read(prop));\n  }\n  return ret;\n}\n/**\n * Converts the given value into a TRBL object.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left)\n * @since 3.0.0\n */\nfunction toTRBL(value) {\n  return _readValueToProps(value, {\n    top: 'y',\n    right: 'x',\n    bottom: 'y',\n    left: 'x'\n  });\n}\n/**\n * Converts the given value into a TRBL corners object (similar with css border-radius).\n * @param value - If a number, set the value to all TRBL corner components,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n * @returns The TRBL corner values (topLeft, topRight, bottomLeft, bottomRight)\n * @since 3.0.0\n */\nfunction toTRBLCorners(value) {\n  return _readValueToProps(value, ['topLeft', 'topRight', 'bottomLeft', 'bottomRight']);\n}\n/**\n * Converts the given value into a padding object with pre-computed width/height.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left, width, height)\n * @since 2.7.0\n */\nfunction toPadding(value) {\n  const obj = toTRBL(value);\n  obj.width = obj.left + obj.right;\n  obj.height = obj.top + obj.bottom;\n  return obj;\n}\n/**\n * Parses font options and returns the font object.\n * @param options - A object that contains font options to be parsed.\n * @param fallback - A object that contains fallback font options.\n * @return The font object.\n * @private\n */\nfunction toFont(options, fallback) {\n  options = options || {};\n  fallback = fallback || defaults.font;\n  let size = valueOrDefault(options.size, fallback.size);\n  if (typeof size === 'string') {\n    size = parseInt(size, 10);\n  }\n  let style = valueOrDefault(options.style, fallback.style);\n  if (style && !('' + style).match(FONT_STYLE)) {\n    console.warn('Invalid font style specified: \"' + style + '\"');\n    style = undefined;\n  }\n  const font = {\n    family: valueOrDefault(options.family, fallback.family),\n    lineHeight: toLineHeight(valueOrDefault(options.lineHeight, fallback.lineHeight), size),\n    size,\n    style,\n    weight: valueOrDefault(options.weight, fallback.weight),\n    string: ''\n  };\n  font.string = toFontString(font);\n  return font;\n}\n/**\n * Evaluates the given `inputs` sequentially and returns the first defined value.\n * @param inputs - An array of values, falling back to the last value.\n * @param context - If defined and the current value is a function, the value\n * is called with `context` as first argument and the result becomes the new input.\n * @param index - If defined and the current value is an array, the value\n * at `index` become the new input.\n * @param info - object to return information about resolution in\n * @param info.cacheable - Will be set to `false` if option is not cacheable.\n * @since 2.7.0\n */\nfunction resolve(inputs, context, index, info) {\n  let cacheable = true;\n  let i, ilen, value;\n  for (i = 0, ilen = inputs.length; i < ilen; ++i) {\n    value = inputs[i];\n    if (value === undefined) {\n      continue;\n    }\n    if (context !== undefined && typeof value === 'function') {\n      value = value(context);\n      cacheable = false;\n    }\n    if (index !== undefined && isArray(value)) {\n      value = value[index % value.length];\n      cacheable = false;\n    }\n    if (value !== undefined) {\n      if (info && !cacheable) {\n        info.cacheable = false;\n      }\n      return value;\n    }\n  }\n}\n/**\n * @param minmax\n * @param grace\n * @param beginAtZero\n * @private\n */\nfunction _addGrace(minmax, grace, beginAtZero) {\n  const {\n    min,\n    max\n  } = minmax;\n  const change = toDimension(grace, (max - min) / 2);\n  const keepZero = (value, add) => beginAtZero && value === 0 ? 0 : value + add;\n  return {\n    min: keepZero(min, -Math.abs(change)),\n    max: keepZero(max, change)\n  };\n}\nfunction createContext(parentContext, context) {\n  return Object.assign(Object.create(parentContext), context);\n}\n\n/**\n * Creates a Proxy for resolving raw values for options.\n * @param scopes - The option scopes to look for values, in resolution order\n * @param prefixes - The prefixes for values, in resolution order.\n * @param rootScopes - The root option scopes\n * @param fallback - Parent scopes fallback\n * @param getTarget - callback for getting the target for changed values\n * @returns Proxy\n * @private\n */\nfunction _createResolver(scopes, prefixes = [''], rootScopes, fallback, getTarget = () => scopes[0]) {\n  const finalRootScopes = rootScopes || scopes;\n  if (typeof fallback === 'undefined') {\n    fallback = _resolve('_fallback', scopes);\n  }\n  const cache = {\n    [Symbol.toStringTag]: 'Object',\n    _cacheable: true,\n    _scopes: scopes,\n    _rootScopes: finalRootScopes,\n    _fallback: fallback,\n    _getTarget: getTarget,\n    override: scope => _createResolver([scope, ...scopes], prefixes, finalRootScopes, fallback)\n  };\n  return new Proxy(cache, {\n    /**\n    * A trap for the delete operator.\n    */\n    deleteProperty(target, prop) {\n      delete target[prop]; // remove from cache\n      delete target._keys; // remove cached keys\n      delete scopes[0][prop]; // remove from top level scope\n      return true;\n    },\n    /**\n    * A trap for getting property values.\n    */\n    get(target, prop) {\n      return _cached(target, prop, () => _resolveWithPrefixes(prop, prefixes, scopes, target));\n    },\n    /**\n    * A trap for Object.getOwnPropertyDescriptor.\n    * Also used by Object.hasOwnProperty.\n    */\n    getOwnPropertyDescriptor(target, prop) {\n      return Reflect.getOwnPropertyDescriptor(target._scopes[0], prop);\n    },\n    /**\n    * A trap for Object.getPrototypeOf.\n    */\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(scopes[0]);\n    },\n    /**\n    * A trap for the in operator.\n    */\n    has(target, prop) {\n      return getKeysFromAllScopes(target).includes(prop);\n    },\n    /**\n    * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n    */\n    ownKeys(target) {\n      return getKeysFromAllScopes(target);\n    },\n    /**\n    * A trap for setting property values.\n    */\n    set(target, prop, value) {\n      const storage = target._storage || (target._storage = getTarget());\n      target[prop] = storage[prop] = value; // set to top level scope + cache\n      delete target._keys; // remove cached keys\n      return true;\n    }\n  });\n}\n/**\n * Returns an Proxy for resolving option values with context.\n * @param proxy - The Proxy returned by `_createResolver`\n * @param context - Context object for scriptable/indexable options\n * @param subProxy - The proxy provided for scriptable options\n * @param descriptorDefaults - Defaults for descriptors\n * @private\n */\nfunction _attachContext(proxy, context, subProxy, descriptorDefaults) {\n  const cache = {\n    _cacheable: false,\n    _proxy: proxy,\n    _context: context,\n    _subProxy: subProxy,\n    _stack: new Set(),\n    _descriptors: _descriptors(proxy, descriptorDefaults),\n    setContext: ctx => _attachContext(proxy, ctx, subProxy, descriptorDefaults),\n    override: scope => _attachContext(proxy.override(scope), context, subProxy, descriptorDefaults)\n  };\n  return new Proxy(cache, {\n    /**\n    * A trap for the delete operator.\n    */\n    deleteProperty(target, prop) {\n      delete target[prop]; // remove from cache\n      delete proxy[prop]; // remove from proxy\n      return true;\n    },\n    /**\n    * A trap for getting property values.\n    */\n    get(target, prop, receiver) {\n      return _cached(target, prop, () => _resolveWithContext(target, prop, receiver));\n    },\n    /**\n    * A trap for Object.getOwnPropertyDescriptor.\n    * Also used by Object.hasOwnProperty.\n    */\n    getOwnPropertyDescriptor(target, prop) {\n      return target._descriptors.allKeys ? Reflect.has(proxy, prop) ? {\n        enumerable: true,\n        configurable: true\n      } : undefined : Reflect.getOwnPropertyDescriptor(proxy, prop);\n    },\n    /**\n    * A trap for Object.getPrototypeOf.\n    */\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(proxy);\n    },\n    /**\n    * A trap for the in operator.\n    */\n    has(target, prop) {\n      return Reflect.has(proxy, prop);\n    },\n    /**\n    * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n    */\n    ownKeys() {\n      return Reflect.ownKeys(proxy);\n    },\n    /**\n    * A trap for setting property values.\n    */\n    set(target, prop, value) {\n      proxy[prop] = value; // set to proxy\n      delete target[prop]; // remove from cache\n      return true;\n    }\n  });\n}\n/**\n * @private\n */\nfunction _descriptors(proxy, defaults = {\n  scriptable: true,\n  indexable: true\n}) {\n  const {\n    _scriptable = defaults.scriptable,\n    _indexable = defaults.indexable,\n    _allKeys = defaults.allKeys\n  } = proxy;\n  return {\n    allKeys: _allKeys,\n    scriptable: _scriptable,\n    indexable: _indexable,\n    isScriptable: isFunction(_scriptable) ? _scriptable : () => _scriptable,\n    isIndexable: isFunction(_indexable) ? _indexable : () => _indexable\n  };\n}\nconst readKey = (prefix, name) => prefix ? prefix + _capitalize(name) : name;\nconst needsSubResolver = (prop, value) => isObject(value) && prop !== 'adapters' && (Object.getPrototypeOf(value) === null || value.constructor === Object);\nfunction _cached(target, prop, resolve) {\n  if (Object.prototype.hasOwnProperty.call(target, prop) || prop === 'constructor') {\n    return target[prop];\n  }\n  const value = resolve();\n  // cache the resolved value\n  target[prop] = value;\n  return value;\n}\nfunction _resolveWithContext(target, prop, receiver) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _descriptors: descriptors\n  } = target;\n  let value = _proxy[prop]; // resolve from proxy\n  // resolve with context\n  if (isFunction(value) && descriptors.isScriptable(prop)) {\n    value = _resolveScriptable(prop, value, target, receiver);\n  }\n  if (isArray(value) && value.length) {\n    value = _resolveArray(prop, value, target, descriptors.isIndexable);\n  }\n  if (needsSubResolver(prop, value)) {\n    // if the resolved value is an object, create a sub resolver for it\n    value = _attachContext(value, _context, _subProxy && _subProxy[prop], descriptors);\n  }\n  return value;\n}\nfunction _resolveScriptable(prop, getValue, target, receiver) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _stack\n  } = target;\n  if (_stack.has(prop)) {\n    throw new Error('Recursion detected: ' + Array.from(_stack).join('->') + '->' + prop);\n  }\n  _stack.add(prop);\n  let value = getValue(_context, _subProxy || receiver);\n  _stack.delete(prop);\n  if (needsSubResolver(prop, value)) {\n    // When scriptable option returns an object, create a resolver on that.\n    value = createSubResolver(_proxy._scopes, _proxy, prop, value);\n  }\n  return value;\n}\nfunction _resolveArray(prop, value, target, isIndexable) {\n  const {\n    _proxy,\n    _context,\n    _subProxy,\n    _descriptors: descriptors\n  } = target;\n  if (typeof _context.index !== 'undefined' && isIndexable(prop)) {\n    return value[_context.index % value.length];\n  } else if (isObject(value[0])) {\n    // Array of objects, return array or resolvers\n    const arr = value;\n    const scopes = _proxy._scopes.filter(s => s !== arr);\n    value = [];\n    for (const item of arr) {\n      const resolver = createSubResolver(scopes, _proxy, prop, item);\n      value.push(_attachContext(resolver, _context, _subProxy && _subProxy[prop], descriptors));\n    }\n  }\n  return value;\n}\nfunction resolveFallback(fallback, prop, value) {\n  return isFunction(fallback) ? fallback(prop, value) : fallback;\n}\nconst getScope = (key, parent) => key === true ? parent : typeof key === 'string' ? resolveObjectKey(parent, key) : undefined;\nfunction addScopes(set, parentScopes, key, parentFallback, value) {\n  for (const parent of parentScopes) {\n    const scope = getScope(key, parent);\n    if (scope) {\n      set.add(scope);\n      const fallback = resolveFallback(scope._fallback, key, value);\n      if (typeof fallback !== 'undefined' && fallback !== key && fallback !== parentFallback) {\n        // When we reach the descriptor that defines a new _fallback, return that.\n        // The fallback will resume to that new scope.\n        return fallback;\n      }\n    } else if (scope === false && typeof parentFallback !== 'undefined' && key !== parentFallback) {\n      // Fallback to `false` results to `false`, when falling back to different key.\n      // For example `interaction` from `hover` or `plugins.tooltip` and `animation` from `animations`\n      return null;\n    }\n  }\n  return false;\n}\nfunction createSubResolver(parentScopes, resolver, prop, value) {\n  const rootScopes = resolver._rootScopes;\n  const fallback = resolveFallback(resolver._fallback, prop, value);\n  const allScopes = [...parentScopes, ...rootScopes];\n  const set = new Set();\n  set.add(value);\n  let key = addScopesFromKey(set, allScopes, prop, fallback || prop, value);\n  if (key === null) {\n    return false;\n  }\n  if (typeof fallback !== 'undefined' && fallback !== prop) {\n    key = addScopesFromKey(set, allScopes, fallback, key, value);\n    if (key === null) {\n      return false;\n    }\n  }\n  return _createResolver(Array.from(set), [''], rootScopes, fallback, () => subGetTarget(resolver, prop, value));\n}\nfunction addScopesFromKey(set, allScopes, key, fallback, item) {\n  while (key) {\n    key = addScopes(set, allScopes, key, fallback, item);\n  }\n  return key;\n}\nfunction subGetTarget(resolver, prop, value) {\n  const parent = resolver._getTarget();\n  if (!(prop in parent)) {\n    parent[prop] = {};\n  }\n  const target = parent[prop];\n  if (isArray(target) && isObject(value)) {\n    // For array of objects, the object is used to store updated values\n    return value;\n  }\n  return target || {};\n}\nfunction _resolveWithPrefixes(prop, prefixes, scopes, proxy) {\n  let value;\n  for (const prefix of prefixes) {\n    value = _resolve(readKey(prefix, prop), scopes);\n    if (typeof value !== 'undefined') {\n      return needsSubResolver(prop, value) ? createSubResolver(scopes, proxy, prop, value) : value;\n    }\n  }\n}\nfunction _resolve(key, scopes) {\n  for (const scope of scopes) {\n    if (!scope) {\n      continue;\n    }\n    const value = scope[key];\n    if (typeof value !== 'undefined') {\n      return value;\n    }\n  }\n}\nfunction getKeysFromAllScopes(target) {\n  let keys = target._keys;\n  if (!keys) {\n    keys = target._keys = resolveKeysFromAllScopes(target._scopes);\n  }\n  return keys;\n}\nfunction resolveKeysFromAllScopes(scopes) {\n  const set = new Set();\n  for (const scope of scopes) {\n    for (const key of Object.keys(scope).filter(k => !k.startsWith('_'))) {\n      set.add(key);\n    }\n  }\n  return Array.from(set);\n}\nfunction _parseObjectDataRadialScale(meta, data, start, count) {\n  const {\n    iScale\n  } = meta;\n  const {\n    key = 'r'\n  } = this._parsing;\n  const parsed = new Array(count);\n  let i, ilen, index, item;\n  for (i = 0, ilen = count; i < ilen; ++i) {\n    index = i + start;\n    item = data[index];\n    parsed[i] = {\n      r: iScale.parse(resolveObjectKey(item, key), index)\n    };\n  }\n  return parsed;\n}\nconst EPSILON = Number.EPSILON || 1e-14;\nconst getPoint = (points, i) => i < points.length && !points[i].skip && points[i];\nconst getValueAxis = indexAxis => indexAxis === 'x' ? 'y' : 'x';\nfunction splineCurve(firstPoint, middlePoint, afterPoint, t) {\n  // Props to Rob Spencer at scaled innovation for his post on splining between points\n  // http://scaledinnovation.com/analytics/splines/aboutSplines.html\n  // This function must also respect \"skipped\" points\n  const previous = firstPoint.skip ? middlePoint : firstPoint;\n  const current = middlePoint;\n  const next = afterPoint.skip ? middlePoint : afterPoint;\n  const d01 = distanceBetweenPoints(current, previous);\n  const d12 = distanceBetweenPoints(next, current);\n  let s01 = d01 / (d01 + d12);\n  let s12 = d12 / (d01 + d12);\n  // If all points are the same, s01 & s02 will be inf\n  s01 = isNaN(s01) ? 0 : s01;\n  s12 = isNaN(s12) ? 0 : s12;\n  const fa = t * s01; // scaling factor for triangle Ta\n  const fb = t * s12;\n  return {\n    previous: {\n      x: current.x - fa * (next.x - previous.x),\n      y: current.y - fa * (next.y - previous.y)\n    },\n    next: {\n      x: current.x + fb * (next.x - previous.x),\n      y: current.y + fb * (next.y - previous.y)\n    }\n  };\n}\n/**\n * Adjust tangents to ensure monotonic properties\n */\nfunction monotoneAdjust(points, deltaK, mK) {\n  const pointsLen = points.length;\n  let alphaK, betaK, tauK, squaredMagnitude, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen - 1; ++i) {\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent || !pointAfter) {\n      continue;\n    }\n    if (almostEquals(deltaK[i], 0, EPSILON)) {\n      mK[i] = mK[i + 1] = 0;\n      continue;\n    }\n    alphaK = mK[i] / deltaK[i];\n    betaK = mK[i + 1] / deltaK[i];\n    squaredMagnitude = Math.pow(alphaK, 2) + Math.pow(betaK, 2);\n    if (squaredMagnitude <= 9) {\n      continue;\n    }\n    tauK = 3 / Math.sqrt(squaredMagnitude);\n    mK[i] = alphaK * tauK * deltaK[i];\n    mK[i + 1] = betaK * tauK * deltaK[i];\n  }\n}\nfunction monotoneCompute(points, mK, indexAxis = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  let delta, pointBefore, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n    const iPixel = pointCurrent[indexAxis];\n    const vPixel = pointCurrent[valueAxis];\n    if (pointBefore) {\n      delta = (iPixel - pointBefore[indexAxis]) / 3;\n      pointCurrent[`cp1${indexAxis}`] = iPixel - delta;\n      pointCurrent[`cp1${valueAxis}`] = vPixel - delta * mK[i];\n    }\n    if (pointAfter) {\n      delta = (pointAfter[indexAxis] - iPixel) / 3;\n      pointCurrent[`cp2${indexAxis}`] = iPixel + delta;\n      pointCurrent[`cp2${valueAxis}`] = vPixel + delta * mK[i];\n    }\n  }\n}\n/**\n * This function calculates Bézier control points in a similar way than |splineCurve|,\n * but preserves monotonicity of the provided data and ensures no local extremums are added\n * between the dataset discrete points due to the interpolation.\n * See : https://en.wikipedia.org/wiki/Monotone_cubic_interpolation\n */\nfunction splineCurveMonotone(points, indexAxis = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  const deltaK = Array(pointsLen).fill(0);\n  const mK = Array(pointsLen);\n  // Calculate slopes (deltaK) and initialize tangents (mK)\n  let i, pointBefore, pointCurrent;\n  let pointAfter = getPoint(points, 0);\n  for (i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n    if (pointAfter) {\n      const slopeDelta = pointAfter[indexAxis] - pointCurrent[indexAxis];\n      // In the case of two points that appear at the same x pixel, slopeDeltaX is 0\n      deltaK[i] = slopeDelta !== 0 ? (pointAfter[valueAxis] - pointCurrent[valueAxis]) / slopeDelta : 0;\n    }\n    mK[i] = !pointBefore ? deltaK[i] : !pointAfter ? deltaK[i - 1] : sign(deltaK[i - 1]) !== sign(deltaK[i]) ? 0 : (deltaK[i - 1] + deltaK[i]) / 2;\n  }\n  monotoneAdjust(points, deltaK, mK);\n  monotoneCompute(points, mK, indexAxis);\n}\nfunction capControlPoint(pt, min, max) {\n  return Math.max(Math.min(pt, max), min);\n}\nfunction capBezierPoints(points, area) {\n  let i, ilen, point, inArea, inAreaPrev;\n  let inAreaNext = _isPointInArea(points[0], area);\n  for (i = 0, ilen = points.length; i < ilen; ++i) {\n    inAreaPrev = inArea;\n    inArea = inAreaNext;\n    inAreaNext = i < ilen - 1 && _isPointInArea(points[i + 1], area);\n    if (!inArea) {\n      continue;\n    }\n    point = points[i];\n    if (inAreaPrev) {\n      point.cp1x = capControlPoint(point.cp1x, area.left, area.right);\n      point.cp1y = capControlPoint(point.cp1y, area.top, area.bottom);\n    }\n    if (inAreaNext) {\n      point.cp2x = capControlPoint(point.cp2x, area.left, area.right);\n      point.cp2y = capControlPoint(point.cp2y, area.top, area.bottom);\n    }\n  }\n}\n/**\n * @private\n */\nfunction _updateBezierControlPoints(points, options, area, loop, indexAxis) {\n  let i, ilen, point, controlPoints;\n  // Only consider points that are drawn in case the spanGaps option is used\n  if (options.spanGaps) {\n    points = points.filter(pt => !pt.skip);\n  }\n  if (options.cubicInterpolationMode === 'monotone') {\n    splineCurveMonotone(points, indexAxis);\n  } else {\n    let prev = loop ? points[points.length - 1] : points[0];\n    for (i = 0, ilen = points.length; i < ilen; ++i) {\n      point = points[i];\n      controlPoints = splineCurve(prev, point, points[Math.min(i + 1, ilen - (loop ? 0 : 1)) % ilen], options.tension);\n      point.cp1x = controlPoints.previous.x;\n      point.cp1y = controlPoints.previous.y;\n      point.cp2x = controlPoints.next.x;\n      point.cp2y = controlPoints.next.y;\n      prev = point;\n    }\n  }\n  if (options.capBezierPoints) {\n    capBezierPoints(points, area);\n  }\n}\n\n/**\n * @private\n */\nfunction _isDomSupported() {\n  return typeof window !== 'undefined' && typeof document !== 'undefined';\n}\n/**\n * @private\n */\nfunction _getParentNode(domNode) {\n  let parent = domNode.parentNode;\n  if (parent && parent.toString() === '[object ShadowRoot]') {\n    parent = parent.host;\n  }\n  return parent;\n}\n/**\n * convert max-width/max-height values that may be percentages into a number\n * @private\n */\nfunction parseMaxStyle(styleValue, node, parentProperty) {\n  let valueInPixels;\n  if (typeof styleValue === 'string') {\n    valueInPixels = parseInt(styleValue, 10);\n    if (styleValue.indexOf('%') !== -1) {\n      // percentage * size in dimension\n      valueInPixels = valueInPixels / 100 * node.parentNode[parentProperty];\n    }\n  } else {\n    valueInPixels = styleValue;\n  }\n  return valueInPixels;\n}\nconst getComputedStyle = element => element.ownerDocument.defaultView.getComputedStyle(element, null);\nfunction getStyle(el, property) {\n  return getComputedStyle(el).getPropertyValue(property);\n}\nconst positions = ['top', 'right', 'bottom', 'left'];\nfunction getPositionedStyle(styles, style, suffix) {\n  const result = {};\n  suffix = suffix ? '-' + suffix : '';\n  for (let i = 0; i < 4; i++) {\n    const pos = positions[i];\n    result[pos] = parseFloat(styles[style + '-' + pos + suffix]) || 0;\n  }\n  result.width = result.left + result.right;\n  result.height = result.top + result.bottom;\n  return result;\n}\nconst useOffsetPos = (x, y, target) => (x > 0 || y > 0) && (!target || !target.shadowRoot);\n/**\n * @param e\n * @param canvas\n * @returns Canvas position\n */\nfunction getCanvasPosition(e, canvas) {\n  const touches = e.touches;\n  const source = touches && touches.length ? touches[0] : e;\n  const {\n    offsetX,\n    offsetY\n  } = source;\n  let box = false;\n  let x, y;\n  if (useOffsetPos(offsetX, offsetY, e.target)) {\n    x = offsetX;\n    y = offsetY;\n  } else {\n    const rect = canvas.getBoundingClientRect();\n    x = source.clientX - rect.left;\n    y = source.clientY - rect.top;\n    box = true;\n  }\n  return {\n    x,\n    y,\n    box\n  };\n}\n/**\n * Gets an event's x, y coordinates, relative to the chart area\n * @param event\n * @param chart\n * @returns x and y coordinates of the event\n */\nfunction getRelativePosition(event, chart) {\n  if ('native' in event) {\n    return event;\n  }\n  const {\n    canvas,\n    currentDevicePixelRatio\n  } = chart;\n  const style = getComputedStyle(canvas);\n  const borderBox = style.boxSizing === 'border-box';\n  const paddings = getPositionedStyle(style, 'padding');\n  const borders = getPositionedStyle(style, 'border', 'width');\n  const {\n    x,\n    y,\n    box\n  } = getCanvasPosition(event, canvas);\n  const xOffset = paddings.left + (box && borders.left);\n  const yOffset = paddings.top + (box && borders.top);\n  let {\n    width,\n    height\n  } = chart;\n  if (borderBox) {\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  return {\n    x: Math.round((x - xOffset) / width * canvas.width / currentDevicePixelRatio),\n    y: Math.round((y - yOffset) / height * canvas.height / currentDevicePixelRatio)\n  };\n}\nfunction getContainerSize(canvas, width, height) {\n  let maxWidth, maxHeight;\n  if (width === undefined || height === undefined) {\n    const container = canvas && _getParentNode(canvas);\n    if (!container) {\n      width = canvas.clientWidth;\n      height = canvas.clientHeight;\n    } else {\n      const rect = container.getBoundingClientRect(); // this is the border box of the container\n      const containerStyle = getComputedStyle(container);\n      const containerBorder = getPositionedStyle(containerStyle, 'border', 'width');\n      const containerPadding = getPositionedStyle(containerStyle, 'padding');\n      width = rect.width - containerPadding.width - containerBorder.width;\n      height = rect.height - containerPadding.height - containerBorder.height;\n      maxWidth = parseMaxStyle(containerStyle.maxWidth, container, 'clientWidth');\n      maxHeight = parseMaxStyle(containerStyle.maxHeight, container, 'clientHeight');\n    }\n  }\n  return {\n    width,\n    height,\n    maxWidth: maxWidth || INFINITY,\n    maxHeight: maxHeight || INFINITY\n  };\n}\nconst round1 = v => Math.round(v * 10) / 10;\n// eslint-disable-next-line complexity\nfunction getMaximumSize(canvas, bbWidth, bbHeight, aspectRatio) {\n  const style = getComputedStyle(canvas);\n  const margins = getPositionedStyle(style, 'margin');\n  const maxWidth = parseMaxStyle(style.maxWidth, canvas, 'clientWidth') || INFINITY;\n  const maxHeight = parseMaxStyle(style.maxHeight, canvas, 'clientHeight') || INFINITY;\n  const containerSize = getContainerSize(canvas, bbWidth, bbHeight);\n  let {\n    width,\n    height\n  } = containerSize;\n  if (style.boxSizing === 'content-box') {\n    const borders = getPositionedStyle(style, 'border', 'width');\n    const paddings = getPositionedStyle(style, 'padding');\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  width = Math.max(0, width - margins.width);\n  height = Math.max(0, aspectRatio ? width / aspectRatio : height - margins.height);\n  width = round1(Math.min(width, maxWidth, containerSize.maxWidth));\n  height = round1(Math.min(height, maxHeight, containerSize.maxHeight));\n  if (width && !height) {\n    // https://github.com/chartjs/Chart.js/issues/4659\n    // If the canvas has width, but no height, default to aspectRatio of 2 (canvas default)\n    height = round1(width / 2);\n  }\n  const maintainHeight = bbWidth !== undefined || bbHeight !== undefined;\n  if (maintainHeight && aspectRatio && containerSize.height && height > containerSize.height) {\n    height = containerSize.height;\n    width = round1(Math.floor(height * aspectRatio));\n  }\n  return {\n    width,\n    height\n  };\n}\n/**\n * @param chart\n * @param forceRatio\n * @param forceStyle\n * @returns True if the canvas context size or transformation has changed.\n */\nfunction retinaScale(chart, forceRatio, forceStyle) {\n  const pixelRatio = forceRatio || 1;\n  const deviceHeight = Math.floor(chart.height * pixelRatio);\n  const deviceWidth = Math.floor(chart.width * pixelRatio);\n  chart.height = Math.floor(chart.height);\n  chart.width = Math.floor(chart.width);\n  const canvas = chart.canvas;\n  // If no style has been set on the canvas, the render size is used as display size,\n  // making the chart visually bigger, so let's enforce it to the \"correct\" values.\n  // See https://github.com/chartjs/Chart.js/issues/3575\n  if (canvas.style && (forceStyle || !canvas.style.height && !canvas.style.width)) {\n    canvas.style.height = `${chart.height}px`;\n    canvas.style.width = `${chart.width}px`;\n  }\n  if (chart.currentDevicePixelRatio !== pixelRatio || canvas.height !== deviceHeight || canvas.width !== deviceWidth) {\n    chart.currentDevicePixelRatio = pixelRatio;\n    canvas.height = deviceHeight;\n    canvas.width = deviceWidth;\n    chart.ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);\n    return true;\n  }\n  return false;\n}\n/**\n * Detects support for options object argument in addEventListener.\n * https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Safely_detecting_option_support\n * @private\n */\nconst supportsEventListenerOptions = function () {\n  let passiveSupported = false;\n  try {\n    const options = {\n      get passive() {\n        passiveSupported = true;\n        return false;\n      }\n    };\n    if (_isDomSupported()) {\n      window.addEventListener('test', null, options);\n      window.removeEventListener('test', null, options);\n    }\n  } catch (e) {\n    // continue regardless of error\n  }\n  return passiveSupported;\n}();\n/**\n * The \"used\" size is the final value of a dimension property after all calculations have\n * been performed. This method uses the computed style of `element` but returns undefined\n * if the computed style is not expressed in pixels. That can happen in some cases where\n * `element` has a size relative to its parent and this last one is not yet displayed,\n * for example because of `display: none` on a parent node.\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/used_value\n * @returns Size in pixels or undefined if unknown.\n */\nfunction readUsedSize(element, property) {\n  const value = getStyle(element, property);\n  const matches = value && value.match(/^(\\d+)(\\.\\d+)?px$/);\n  return matches ? +matches[1] : undefined;\n}\n\n/**\n * @private\n */\nfunction _pointInLine(p1, p2, t, mode) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: p1.y + t * (p2.y - p1.y)\n  };\n}\n/**\n * @private\n */\nfunction _steppedInterpolation(p1, p2, t, mode) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: mode === 'middle' ? t < 0.5 ? p1.y : p2.y : mode === 'after' ? t < 1 ? p1.y : p2.y : t > 0 ? p2.y : p1.y\n  };\n}\n/**\n * @private\n */\nfunction _bezierInterpolation(p1, p2, t, mode) {\n  const cp1 = {\n    x: p1.cp2x,\n    y: p1.cp2y\n  };\n  const cp2 = {\n    x: p2.cp1x,\n    y: p2.cp1y\n  };\n  const a = _pointInLine(p1, cp1, t);\n  const b = _pointInLine(cp1, cp2, t);\n  const c = _pointInLine(cp2, p2, t);\n  const d = _pointInLine(a, b, t);\n  const e = _pointInLine(b, c, t);\n  return _pointInLine(d, e, t);\n}\nconst getRightToLeftAdapter = function (rectX, width) {\n  return {\n    x(x) {\n      return rectX + rectX + width - x;\n    },\n    setWidth(w) {\n      width = w;\n    },\n    textAlign(align) {\n      if (align === 'center') {\n        return align;\n      }\n      return align === 'right' ? 'left' : 'right';\n    },\n    xPlus(x, value) {\n      return x - value;\n    },\n    leftForLtr(x, itemWidth) {\n      return x - itemWidth;\n    }\n  };\n};\nconst getLeftToRightAdapter = function () {\n  return {\n    x(x) {\n      return x;\n    },\n    setWidth(w) {},\n    textAlign(align) {\n      return align;\n    },\n    xPlus(x, value) {\n      return x + value;\n    },\n    leftForLtr(x, _itemWidth) {\n      return x;\n    }\n  };\n};\nfunction getRtlAdapter(rtl, rectX, width) {\n  return rtl ? getRightToLeftAdapter(rectX, width) : getLeftToRightAdapter();\n}\nfunction overrideTextDirection(ctx, direction) {\n  let style, original;\n  if (direction === 'ltr' || direction === 'rtl') {\n    style = ctx.canvas.style;\n    original = [style.getPropertyValue('direction'), style.getPropertyPriority('direction')];\n    style.setProperty('direction', direction, 'important');\n    ctx.prevTextDirection = original;\n  }\n}\nfunction restoreTextDirection(ctx, original) {\n  if (original !== undefined) {\n    delete ctx.prevTextDirection;\n    ctx.canvas.style.setProperty('direction', original[0], original[1]);\n  }\n}\nfunction propertyFn(property) {\n  if (property === 'angle') {\n    return {\n      between: _angleBetween,\n      compare: _angleDiff,\n      normalize: _normalizeAngle\n    };\n  }\n  return {\n    between: _isBetween,\n    compare: (a, b) => a - b,\n    normalize: x => x\n  };\n}\nfunction normalizeSegment({\n  start,\n  end,\n  count,\n  loop,\n  style\n}) {\n  return {\n    start: start % count,\n    end: end % count,\n    loop: loop && (end - start + 1) % count === 0,\n    style\n  };\n}\nfunction getSegment(segment, points, bounds) {\n  const {\n    property,\n    start: startBound,\n    end: endBound\n  } = bounds;\n  const {\n    between,\n    normalize\n  } = propertyFn(property);\n  const count = points.length;\n  let {\n    start,\n    end,\n    loop\n  } = segment;\n  let i, ilen;\n  if (loop) {\n    start += count;\n    end += count;\n    for (i = 0, ilen = count; i < ilen; ++i) {\n      if (!between(normalize(points[start % count][property]), startBound, endBound)) {\n        break;\n      }\n      start--;\n      end--;\n    }\n    start %= count;\n    end %= count;\n  }\n  if (end < start) {\n    end += count;\n  }\n  return {\n    start,\n    end,\n    loop,\n    style: segment.style\n  };\n}\nfunction _boundSegment(segment, points, bounds) {\n  if (!bounds) {\n    return [segment];\n  }\n  const {\n    property,\n    start: startBound,\n    end: endBound\n  } = bounds;\n  const count = points.length;\n  const {\n    compare,\n    between,\n    normalize\n  } = propertyFn(property);\n  const {\n    start,\n    end,\n    loop,\n    style\n  } = getSegment(segment, points, bounds);\n  const result = [];\n  let inside = false;\n  let subStart = null;\n  let value, point, prevValue;\n  const startIsBefore = () => between(startBound, prevValue, value) && compare(startBound, prevValue) !== 0;\n  const endIsBefore = () => compare(endBound, value) === 0 || between(endBound, prevValue, value);\n  const shouldStart = () => inside || startIsBefore();\n  const shouldStop = () => !inside || endIsBefore();\n  for (let i = start, prev = start; i <= end; ++i) {\n    point = points[i % count];\n    if (point.skip) {\n      continue;\n    }\n    value = normalize(point[property]);\n    if (value === prevValue) {\n      continue;\n    }\n    inside = between(value, startBound, endBound);\n    if (subStart === null && shouldStart()) {\n      subStart = compare(value, startBound) === 0 ? i : prev;\n    }\n    if (subStart !== null && shouldStop()) {\n      result.push(normalizeSegment({\n        start: subStart,\n        end: i,\n        loop,\n        count,\n        style\n      }));\n      subStart = null;\n    }\n    prev = i;\n    prevValue = value;\n  }\n  if (subStart !== null) {\n    result.push(normalizeSegment({\n      start: subStart,\n      end,\n      loop,\n      count,\n      style\n    }));\n  }\n  return result;\n}\nfunction _boundSegments(line, bounds) {\n  const result = [];\n  const segments = line.segments;\n  for (let i = 0; i < segments.length; i++) {\n    const sub = _boundSegment(segments[i], line.points, bounds);\n    if (sub.length) {\n      result.push(...sub);\n    }\n  }\n  return result;\n}\nfunction findStartAndEnd(points, count, loop, spanGaps) {\n  let start = 0;\n  let end = count - 1;\n  if (loop && !spanGaps) {\n    while (start < count && !points[start].skip) {\n      start++;\n    }\n  }\n  while (start < count && points[start].skip) {\n    start++;\n  }\n  start %= count;\n  if (loop) {\n    end += start;\n  }\n  while (end > start && points[end % count].skip) {\n    end--;\n  }\n  end %= count;\n  return {\n    start,\n    end\n  };\n}\nfunction solidSegments(points, start, max, loop) {\n  const count = points.length;\n  const result = [];\n  let last = start;\n  let prev = points[start];\n  let end;\n  for (end = start + 1; end <= max; ++end) {\n    const cur = points[end % count];\n    if (cur.skip || cur.stop) {\n      if (!prev.skip) {\n        loop = false;\n        result.push({\n          start: start % count,\n          end: (end - 1) % count,\n          loop\n        });\n        start = last = cur.stop ? end : null;\n      }\n    } else {\n      last = end;\n      if (prev.skip) {\n        start = end;\n      }\n    }\n    prev = cur;\n  }\n  if (last !== null) {\n    result.push({\n      start: start % count,\n      end: last % count,\n      loop\n    });\n  }\n  return result;\n}\nfunction _computeSegments(line, segmentOptions) {\n  const points = line.points;\n  const spanGaps = line.options.spanGaps;\n  const count = points.length;\n  if (!count) {\n    return [];\n  }\n  const loop = !!line._loop;\n  const {\n    start,\n    end\n  } = findStartAndEnd(points, count, loop, spanGaps);\n  if (spanGaps === true) {\n    return splitByStyles(line, [{\n      start,\n      end,\n      loop\n    }], points, segmentOptions);\n  }\n  const max = end < start ? end + count : end;\n  const completeLoop = !!line._fullLoop && start === 0 && end === count - 1;\n  return splitByStyles(line, solidSegments(points, start, max, completeLoop), points, segmentOptions);\n}\nfunction splitByStyles(line, segments, points, segmentOptions) {\n  if (!segmentOptions || !segmentOptions.setContext || !points) {\n    return segments;\n  }\n  return doSplitByStyles(line, segments, points, segmentOptions);\n}\nfunction doSplitByStyles(line, segments, points, segmentOptions) {\n  const chartContext = line._chart.getContext();\n  const baseStyle = readStyle(line.options);\n  const {\n    _datasetIndex: datasetIndex,\n    options: {\n      spanGaps\n    }\n  } = line;\n  const count = points.length;\n  const result = [];\n  let prevStyle = baseStyle;\n  let start = segments[0].start;\n  let i = start;\n  function addStyle(s, e, l, st) {\n    const dir = spanGaps ? -1 : 1;\n    if (s === e) {\n      return;\n    }\n    s += count;\n    while (points[s % count].skip) {\n      s -= dir;\n    }\n    while (points[e % count].skip) {\n      e += dir;\n    }\n    if (s % count !== e % count) {\n      result.push({\n        start: s % count,\n        end: e % count,\n        loop: l,\n        style: st\n      });\n      prevStyle = st;\n      start = e % count;\n    }\n  }\n  for (const segment of segments) {\n    start = spanGaps ? start : segment.start;\n    let prev = points[start % count];\n    let style;\n    for (i = start + 1; i <= segment.end; i++) {\n      const pt = points[i % count];\n      style = readStyle(segmentOptions.setContext(createContext(chartContext, {\n        type: 'segment',\n        p0: prev,\n        p1: pt,\n        p0DataIndex: (i - 1) % count,\n        p1DataIndex: i % count,\n        datasetIndex\n      })));\n      if (styleChanged(style, prevStyle)) {\n        addStyle(start, i - 1, segment.loop, prevStyle);\n      }\n      prev = pt;\n      prevStyle = style;\n    }\n    if (start < i - 1) {\n      addStyle(start, i - 1, segment.loop, prevStyle);\n    }\n  }\n  return result;\n}\nfunction readStyle(options) {\n  return {\n    backgroundColor: options.backgroundColor,\n    borderCapStyle: options.borderCapStyle,\n    borderDash: options.borderDash,\n    borderDashOffset: options.borderDashOffset,\n    borderJoinStyle: options.borderJoinStyle,\n    borderWidth: options.borderWidth,\n    borderColor: options.borderColor\n  };\n}\nfunction styleChanged(style, prevStyle) {\n  if (!prevStyle) {\n    return false;\n  }\n  const cache = [];\n  const replacer = function (key, value) {\n    if (!isPatternOrGradient(value)) {\n      return value;\n    }\n    if (!cache.includes(value)) {\n      cache.push(value);\n    }\n    return cache.indexOf(value);\n  };\n  return JSON.stringify(style, replacer) !== JSON.stringify(prevStyle, replacer);\n}\nfunction getSizeForArea(scale, chartArea, field) {\n  return scale.options.clip ? scale[field] : chartArea[field];\n}\nfunction getDatasetArea(meta, chartArea) {\n  const {\n    xScale,\n    yScale\n  } = meta;\n  if (xScale && yScale) {\n    return {\n      left: getSizeForArea(xScale, chartArea, 'left'),\n      right: getSizeForArea(xScale, chartArea, 'right'),\n      top: getSizeForArea(yScale, chartArea, 'top'),\n      bottom: getSizeForArea(yScale, chartArea, 'bottom')\n    };\n  }\n  return chartArea;\n}\nfunction getDatasetClipArea(chart, meta) {\n  const clip = meta._clip;\n  if (clip.disabled) {\n    return false;\n  }\n  const area = getDatasetArea(meta, chart.chartArea);\n  return {\n    left: clip.left === false ? 0 : area.left - (clip.left === true ? 0 : clip.left),\n    right: clip.right === false ? chart.width : area.right + (clip.right === true ? 0 : clip.right),\n    top: clip.top === false ? 0 : area.top - (clip.top === true ? 0 : clip.top),\n    bottom: clip.bottom === false ? chart.height : area.bottom + (clip.bottom === true ? 0 : clip.bottom)\n  };\n}\nexport { unclipArea as $, _rlookupByKey as A, _lookupByKey as B, _isPointInArea as C, getAngleFromPoint as D, toPadding as E, each as F, getMaximumSize as G, HALF_PI as H, _getParentNode as I, readUsedSize as J, supportsEventListenerOptions as K, throttled as L, _isDomSupported as M, _factorize as N, finiteOrDefault as O, PI as P, callback as Q, _addGrace as R, _limitValue as S, TAU as T, toDegrees as U, _measureText as V, _int16Range as W, _alignPixel as X, clipArea as Y, renderText as Z, _arrayUnique as _, resolve as a, getStyle as a$, toFont as a0, _toLeftRightCenter as a1, _alignStartEnd as a2, overrides as a3, merge as a4, _capitalize as a5, descriptors as a6, isFunction as a7, _attachContext as a8, _createResolver as a9, getRtlAdapter as aA, overrideTextDirection as aB, _textX as aC, restoreTextDirection as aD, drawPointLegend as aE, distanceBetweenPoints as aF, noop as aG, _setMinAndMaxByKey as aH, niceNum as aI, almostWhole as aJ, almostEquals as aK, _decimalPlaces as aL, Ticks as aM, log10 as aN, _longestText as aO, _filterBetween as aP, _lookup as aQ, isPatternOrGradient as aR, getHoverColor as aS, clone as aT, _merger as aU, _mergerIf as aV, _deprecated as aW, _splitKey as aX, toFontString as aY, splineCurve as aZ, splineCurveMonotone as a_, _descriptors as aa, mergeIf as ab, uid as ac, debounce as ad, retinaScale as ae, clearCanvas as af, setsEqual as ag, getDatasetClipArea as ah, _elementsEqual as ai, _isClickEvent as aj, _isBetween as ak, _normalizeAngle as al, _readValueToProps as am, _updateBezierControlPoints as an, _computeSegments as ao, _boundSegments as ap, _steppedInterpolation as aq, _bezierInterpolation as ar, _pointInLine as as, _steppedLineTo as at, _bezierCurveTo as au, drawPoint as av, addRoundedRectPath as aw, toTRBL as ax, toTRBLCorners as ay, _boundSegment as az, isArray as b, fontString as b0, toLineHeight as b1, PITAU as b2, INFINITY as b3, RAD_PER_DEG as b4, QUARTER_PI as b5, TWO_THIRDS_PI as b6, _angleDiff as b7, color as c, defaults as d, effects as e, resolveObjectKey as f, isNumberFinite as g, defined as h, isObject as i, createContext as j, isNullOrUndef as k, listenArrayEvents as l, toPercentage as m, toDimension as n, formatNumber as o, _angleBetween as p, _getStartAndCountOfVisiblePoints as q, requestAnimFrame as r, sign as s, toRadians as t, unlistenArrayEvents as u, valueOrDefault as v, _scaleRangesChanged as w, isNumber as x, _parseObjectDataRadialScale as y, getRelativePosition as z };", "map": {"version": 3, "names": ["noop", "uid", "id", "isNullOrUndef", "value", "undefined", "isArray", "Array", "type", "Object", "prototype", "toString", "call", "slice", "isObject", "isNumberFinite", "Number", "isFinite", "finiteOrDefault", "defaultValue", "valueOrDefault", "toPercentage", "dimension", "endsWith", "parseFloat", "toDimension", "callback", "fn", "args", "thisArg", "apply", "each", "loopable", "reverse", "i", "len", "keys", "length", "_elementsEqual", "a0", "a1", "ilen", "v0", "v1", "datasetIndex", "index", "clone", "source", "map", "target", "create", "klen", "k", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "indexOf", "_merger", "options", "tval", "sval", "merge", "sources", "merger", "current", "mergeIf", "_mergerIf", "hasOwnProperty", "_deprecated", "scope", "previous", "console", "warn", "keyResolvers", "v", "x", "o", "y", "_splitKey", "parts", "split", "tmp", "part", "push", "_getKeyResolver", "obj", "resolveObjectKey", "resolver", "_capitalize", "str", "char<PERSON>t", "toUpperCase", "defined", "isFunction", "setsEqual", "a", "b", "size", "item", "has", "_isClickEvent", "e", "PI", "Math", "TAU", "PITAU", "INFINITY", "POSITIVE_INFINITY", "RAD_PER_DEG", "HALF_PI", "QUARTER_PI", "TWO_THIRDS_PI", "log10", "sign", "almostEquals", "epsilon", "abs", "niceNum", "range", "roundedRange", "round", "niceRange", "pow", "floor", "fraction", "niceFraction", "_factorize", "result", "sqrt", "sort", "pop", "isNonPrimitive", "n", "Symbol", "toPrimitive", "isNumber", "isNaN", "almostWhole", "rounded", "_setMinAndMaxByKey", "array", "property", "min", "max", "toRadians", "degrees", "toDegrees", "radians", "_decimalPlaces", "p", "getAngleFromPoint", "centrePoint", "anglePoint", "distanceFromXCenter", "distanceFromYCenter", "radialDistanceFromCenter", "angle", "atan2", "distance", "distanceBetweenPoints", "pt1", "pt2", "_angleDiff", "_normalizeAngle", "_angleBetween", "start", "end", "sameAngleIsFullCircle", "s", "angleToStart", "angleToEnd", "startToAngle", "endToAngle", "_limitValue", "_int16Range", "_isBetween", "_lookup", "table", "cmp", "hi", "lo", "mid", "_lookup<PERSON><PERSON><PERSON><PERSON>", "last", "ti", "_rlookupByKey", "_filterBetween", "values", "arrayEvents", "listenArrayEvents", "listener", "_chartjs", "listeners", "defineProperty", "configurable", "enumerable", "for<PERSON>ach", "method", "base", "res", "object", "unlistenArrayEvents", "stub", "splice", "_arrayUnique", "items", "set", "Set", "from", "fontString", "pixelSize", "fontStyle", "fontFamily", "requestAnimFrame", "window", "requestAnimationFrame", "throttled", "argsToUse", "ticking", "debounce", "delay", "timeout", "clearTimeout", "setTimeout", "_toLeftRightCenter", "align", "_alignStartEnd", "_textX", "left", "right", "rtl", "check", "_getStartAndCountOfVisiblePoints", "meta", "points", "animationsDisabled", "pointCount", "count", "_sorted", "iScale", "vScale", "_parsed", "spanGaps", "dataset", "axis", "minDefined", "maxDefined", "getUserBounds", "getPixelForValue", "distanceToDefinedLo", "findIndex", "point", "distanceToDefinedHi", "_scaleRangesChanged", "xScale", "yScale", "_scaleRanges", "newRang<PERSON>", "xmin", "xmax", "ymin", "ymax", "changed", "assign", "atEdge", "t", "elasticIn", "sin", "elasticOut", "effects", "linear", "easeInQuad", "easeOutQuad", "easeInOutQuad", "easeInCubic", "easeOutCubic", "easeInOutCubic", "easeInQuart", "easeOutQuart", "easeInOutQuart", "easeInQuint", "easeOutQuint", "easeInOutQuint", "easeInSine", "cos", "easeOutSine", "easeInOutSine", "easeInExpo", "easeOutExpo", "easeInOutExpo", "easeInCirc", "easeOutCirc", "easeInOutCirc", "easeInElastic", "easeOutElastic", "easeInOutElastic", "easeInBack", "easeOutBack", "easeInOutBack", "easeInBounce", "easeOutBounce", "m", "d", "easeInOutBounce", "isPatternOrGradient", "color", "Color", "getHoverColor", "saturate", "darken", "hexString", "numbers", "colors", "applyAnimationsDefaults", "defaults", "duration", "easing", "loop", "to", "describe", "_fallback", "_indexable", "_scriptable", "name", "properties", "active", "animation", "resize", "show", "animations", "visible", "hide", "applyLayoutsDefaults", "autoPadding", "padding", "top", "bottom", "intlCache", "Map", "getNumberFormat", "locale", "cache<PERSON>ey", "JSON", "stringify", "formatter", "get", "Intl", "NumberFormat", "formatNumber", "num", "format", "formatters", "numeric", "tickValue", "ticks", "chart", "notation", "delta", "maxTick", "calculateDelta", "log<PERSON><PERSON><PERSON>", "numDecimal", "minimumFractionDigits", "maximumFractionDigits", "logarithmic", "remain", "significand", "includes", "Ticks", "applyScaleDefaults", "display", "offset", "beginAtZero", "bounds", "clip", "grace", "grid", "lineWidth", "drawOnChartArea", "drawTicks", "tick<PERSON><PERSON>th", "tickWidth", "_ctx", "tickColor", "border", "dash", "dashOffset", "width", "title", "text", "minRotation", "maxRotation", "mirror", "textStrokeWidth", "textStrokeColor", "autoSkip", "autoSkipPadding", "labelOffset", "minor", "major", "crossAlign", "showLabelBackdrop", "backdropColor", "backdropPadding", "route", "startsWith", "overrides", "descriptors", "getScope$1", "node", "root", "De<PERSON>ults", "constructor", "_descriptors", "_appliers", "backgroundColor", "borderColor", "datasets", "devicePixelRatio", "context", "platform", "getDevicePixelRatio", "elements", "events", "font", "family", "style", "lineHeight", "weight", "hover", "hoverBackgroundColor", "ctx", "hoverBorderColor", "hoverColor", "indexAxis", "interaction", "mode", "intersect", "includeInvisible", "maintainAspectRatio", "onHover", "onClick", "parsing", "plugins", "responsive", "scale", "scales", "showLine", "drawActiveElementsOnTop", "override", "targetScope", "targetName", "scopeObject", "targetScopeObject", "privateName", "defineProperties", "writable", "local", "appliers", "toFontString", "_measureText", "data", "gc", "longest", "string", "textWidth", "measureText", "_longestText", "arrayOfThings", "cache", "garbageCollect", "save", "j", "jlen", "thing", "nestedThing", "restore", "gcLen", "_alignPixel", "pixel", "currentDevicePixelRatio", "halfWidth", "clearCanvas", "canvas", "getContext", "resetTransform", "clearRect", "height", "drawPoint", "drawPointLegend", "w", "xOffset", "yOffset", "cornerRadius", "xOffsetW", "yOffsetW", "pointStyle", "rotation", "radius", "rad", "translate", "rotate", "drawImage", "beginPath", "ellipse", "arc", "closePath", "moveTo", "lineTo", "SQRT1_2", "rect", "fill", "borderWidth", "stroke", "_isPointInArea", "area", "margin", "clipArea", "unclipArea", "_steppedLineTo", "flip", "midpoint", "_bezierCurveTo", "bezierCurveTo", "cp1x", "cp2x", "cp1y", "cp2y", "setRenderOpts", "opts", "translation", "fillStyle", "textAlign", "textBaseline", "decorateText", "line", "strikethrough", "underline", "metrics", "actualBoundingBoxLeft", "actualBoundingBoxRight", "actualBoundingBoxAscent", "actualBoundingBoxDescent", "yDecoration", "strokeStyle", "decorationWidth", "drawBackdrop", "oldColor", "fillRect", "renderText", "lines", "strokeWidth", "strokeColor", "backdrop", "strokeText", "max<PERSON><PERSON><PERSON>", "fillText", "addRoundedRectPath", "h", "topLeft", "bottomLeft", "bottomRight", "topRight", "LINE_HEIGHT", "FONT_STYLE", "toLineHeight", "matches", "match", "numberOrZero", "_readValueToProps", "props", "ret", "objProps", "read", "prop", "toTRBL", "toTRBLCorners", "toPadding", "toFont", "fallback", "parseInt", "resolve", "inputs", "info", "cacheable", "_addGrace", "minmax", "change", "keepZero", "add", "createContext", "parentContext", "_createResolver", "scopes", "prefixes", "rootScopes", "get<PERSON><PERSON><PERSON>", "finalRootScopes", "_resolve", "toStringTag", "_cacheable", "_scopes", "_rootScopes", "_getTarget", "Proxy", "deleteProperty", "_keys", "_cached", "_resolveWithPrefixes", "getOwnPropertyDescriptor", "Reflect", "getPrototypeOf", "getKeysFromAllScopes", "ownKeys", "storage", "_storage", "_attachContext", "proxy", "subProxy", "descriptor<PERSON><PERSON><PERSON><PERSON>", "_proxy", "_context", "_subProxy", "_stack", "setContext", "receiver", "_resolveWithContext", "allKeys", "scriptable", "indexable", "_allKeys", "isScriptable", "isIndexable", "read<PERSON><PERSON>", "prefix", "needsSubResolver", "_resolveScriptable", "_resolveArray", "getValue", "Error", "join", "delete", "createSubResolver", "arr", "filter", "<PERSON><PERSON><PERSON><PERSON>", "getScope", "parent", "addScopes", "parentScopes", "parentFallback", "allScopes", "addScopesFromKey", "subGetTarget", "resolveKeysFromAllScopes", "_parseObjectDataRadialScale", "_parsing", "parsed", "r", "parse", "EPSILON", "getPoint", "skip", "getValueAxis", "splineCurve", "firstPoint", "middlePoint", "afterPoint", "next", "d01", "d12", "s01", "s12", "fa", "fb", "monotoneAdjust", "deltaK", "mK", "pointsLen", "alphaK", "betaK", "tauK", "squaredMagnitude", "pointCurrent", "pointAfter", "monotoneCompute", "valueAxis", "pointBefore", "iPixel", "vPixel", "splineCurveMonotone", "slopeDel<PERSON>", "capControlPoint", "pt", "capBezierPoints", "inArea", "inAreaPrev", "inAreaNext", "_updateBezierControlPoints", "controlPoints", "cubicInterpolationMode", "prev", "tension", "_isDomSupported", "document", "_getParentNode", "domNode", "parentNode", "host", "parseMaxStyle", "styleValue", "parentProperty", "valueInPixels", "getComputedStyle", "element", "ownerDocument", "defaultView", "getStyle", "el", "getPropertyValue", "positions", "getPositionedStyle", "styles", "suffix", "pos", "useOffsetPos", "shadowRoot", "getCanvasPosition", "touches", "offsetX", "offsetY", "box", "getBoundingClientRect", "clientX", "clientY", "getRelativePosition", "event", "borderBox", "boxSizing", "paddings", "borders", "getContainerSize", "maxHeight", "container", "clientWidth", "clientHeight", "containerStyle", "containerBorder", "containerPadding", "round1", "getMaximumSize", "bb<PERSON><PERSON><PERSON>", "bbHeight", "aspectRatio", "margins", "containerSize", "maintainHeight", "retinaScale", "forceRatio", "forceStyle", "pixelRatio", "deviceHeight", "deviceWidth", "setTransform", "supportsEventListenerOptions", "passiveSupported", "passive", "addEventListener", "removeEventListener", "readUsedSize", "_pointInLine", "p1", "p2", "_steppedInterpolation", "_bezierInterpolation", "cp1", "cp2", "c", "getRightToLeftAdapter", "rectX", "<PERSON><PERSON><PERSON><PERSON>", "xPlus", "leftForLtr", "itemWidth", "getLeftToRightAdapter", "_itemWidth", "getRtlAdapter", "overrideTextDirection", "direction", "original", "getPropertyPriority", "setProperty", "prevTextDirection", "restoreTextDirection", "propertyFn", "between", "compare", "normalize", "normalizeSegment", "getSegment", "segment", "startBound", "endBound", "_boundSegment", "inside", "subStart", "prevValue", "startIsBefore", "endIsBefore", "shouldStart", "shouldStop", "_boundSegments", "segments", "sub", "findStartAndEnd", "solidSegments", "cur", "stop", "_computeSegments", "segmentOptions", "_loop", "splitByStyles", "completeLoop", "_fullLoop", "doSplitByStyles", "chartContext", "_chart", "baseStyle", "readStyle", "_datasetIndex", "prevStyle", "addStyle", "l", "st", "dir", "p0", "p0DataIndex", "p1DataIndex", "styleChanged", "borderCapStyle", "borderDash", "borderDashOffset", "borderJoinStyle", "replacer", "getSizeForArea", "chartArea", "field", "getDatasetArea", "getDatasetClipArea", "_clip", "disabled"], "sources": ["C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.core.ts", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.math.ts", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.collection.ts", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.extras.ts", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.easing.ts", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.color.ts", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\node_modules\\chart.js\\src\\core\\core.animations.defaults.js", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\node_modules\\chart.js\\src\\core\\core.layouts.defaults.js", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.intl.ts", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\node_modules\\chart.js\\src\\core\\core.ticks.js", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\node_modules\\chart.js\\src\\core\\core.scale.defaults.js", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\node_modules\\chart.js\\src\\core\\core.defaults.js", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.canvas.ts", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.options.ts", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.config.ts", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.curve.ts", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.dom.ts", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.interpolation.ts", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.rtl.ts", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.segment.js", "C:\\Users\\<USER>\\Desktop\\كوسات\\frontend\\node_modules\\chart.js\\src\\helpers\\helpers.dataset.ts"], "sourcesContent": ["/**\n * @namespace Chart.helpers\n */\n\nimport type {AnyObject} from '../types/basic.js';\nimport type {ActiveDataPoint, ChartEvent} from '../types/index.js';\n\n/**\n * An empty function that can be used, for example, for optional callback.\n */\nexport function noop() {\n  /* noop */\n}\n\n/**\n * Returns a unique id, sequentially generated from a global variable.\n */\nexport const uid = (() => {\n  let id = 0;\n  return () => id++;\n})();\n\n/**\n * Returns true if `value` is neither null nor undefined, else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */\nexport function isNullOrUndef(value: unknown): value is null | undefined {\n  return value === null || value === undefined;\n}\n\n/**\n * Returns true if `value` is an array (including typed arrays), else returns false.\n * @param value - The value to test.\n * @function\n */\nexport function isArray<T = unknown>(value: unknown): value is T[] {\n  if (Array.isArray && Array.isArray(value)) {\n    return true;\n  }\n  const type = Object.prototype.toString.call(value);\n  if (type.slice(0, 7) === '[object' && type.slice(-6) === 'Array]') {\n    return true;\n  }\n  return false;\n}\n\n/**\n * Returns true if `value` is an object (excluding null), else returns false.\n * @param value - The value to test.\n * @since 2.7.0\n */\nexport function isObject(value: unknown): value is AnyObject {\n  return value !== null && Object.prototype.toString.call(value) === '[object Object]';\n}\n\n/**\n * Returns true if `value` is a finite number, else returns false\n * @param value  - The value to test.\n */\nfunction isNumberFinite(value: unknown): value is number {\n  return (typeof value === 'number' || value instanceof Number) && isFinite(+value);\n}\nexport {\n  isNumberFinite as isFinite,\n};\n\n/**\n * Returns `value` if finite, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is not finite.\n */\nexport function finiteOrDefault(value: unknown, defaultValue: number) {\n  return isNumberFinite(value) ? value : defaultValue;\n}\n\n/**\n * Returns `value` if defined, else returns `defaultValue`.\n * @param value - The value to return if defined.\n * @param defaultValue - The value to return if `value` is undefined.\n */\nexport function valueOrDefault<T>(value: T | undefined, defaultValue: T) {\n  return typeof value === 'undefined' ? defaultValue : value;\n}\n\nexport const toPercentage = (value: number | string, dimension: number) =>\n  typeof value === 'string' && value.endsWith('%') ?\n    parseFloat(value) / 100\n    : +value / dimension;\n\nexport const toDimension = (value: number | string, dimension: number) =>\n  typeof value === 'string' && value.endsWith('%') ?\n    parseFloat(value) / 100 * dimension\n    : +value;\n\n/**\n * Calls `fn` with the given `args` in the scope defined by `thisArg` and returns the\n * value returned by `fn`. If `fn` is not a function, this method returns undefined.\n * @param fn - The function to call.\n * @param args - The arguments with which `fn` should be called.\n * @param [thisArg] - The value of `this` provided for the call to `fn`.\n */\nexport function callback<T extends (this: TA, ...restArgs: unknown[]) => R, TA, R>(\n  fn: T | undefined,\n  args: unknown[],\n  thisArg?: TA\n): R | undefined {\n  if (fn && typeof fn.call === 'function') {\n    return fn.apply(thisArg, args);\n  }\n}\n\n/**\n * Note(SB) for performance sake, this method should only be used when loopable type\n * is unknown or in none intensive code (not called often and small loopable). Else\n * it's preferable to use a regular for() loop and save extra function calls.\n * @param loopable - The object or array to be iterated.\n * @param fn - The function to call for each item.\n * @param [thisArg] - The value of `this` provided for the call to `fn`.\n * @param [reverse] - If true, iterates backward on the loopable.\n */\nexport function each<T, TA>(\n  loopable: Record<string, T>,\n  fn: (this: TA, v: T, i: string) => void,\n  thisArg?: TA,\n  reverse?: boolean\n): void;\nexport function each<T, TA>(\n  loopable: T[],\n  fn: (this: TA, v: T, i: number) => void,\n  thisArg?: TA,\n  reverse?: boolean\n): void;\nexport function each<T, TA>(\n  loopable: T[] | Record<string, T>,\n  fn: (this: TA, v: T, i: any) => void,\n  thisArg?: TA,\n  reverse?: boolean\n) {\n  let i: number, len: number, keys: string[];\n  if (isArray(loopable)) {\n    len = loopable.length;\n    if (reverse) {\n      for (i = len - 1; i >= 0; i--) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    } else {\n      for (i = 0; i < len; i++) {\n        fn.call(thisArg, loopable[i], i);\n      }\n    }\n  } else if (isObject(loopable)) {\n    keys = Object.keys(loopable);\n    len = keys.length;\n    for (i = 0; i < len; i++) {\n      fn.call(thisArg, loopable[keys[i]], keys[i]);\n    }\n  }\n}\n\n/**\n * Returns true if the `a0` and `a1` arrays have the same content, else returns false.\n * @param a0 - The array to compare\n * @param a1 - The array to compare\n * @private\n */\nexport function _elementsEqual(a0: ActiveDataPoint[], a1: ActiveDataPoint[]) {\n  let i: number, ilen: number, v0: ActiveDataPoint, v1: ActiveDataPoint;\n\n  if (!a0 || !a1 || a0.length !== a1.length) {\n    return false;\n  }\n\n  for (i = 0, ilen = a0.length; i < ilen; ++i) {\n    v0 = a0[i];\n    v1 = a1[i];\n\n    if (v0.datasetIndex !== v1.datasetIndex || v0.index !== v1.index) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Returns a deep copy of `source` without keeping references on objects and arrays.\n * @param source - The value to clone.\n */\nexport function clone<T>(source: T): T {\n  if (isArray(source)) {\n    return source.map(clone) as unknown as T;\n  }\n\n  if (isObject(source)) {\n    const target = Object.create(null);\n    const keys = Object.keys(source);\n    const klen = keys.length;\n    let k = 0;\n\n    for (; k < klen; ++k) {\n      target[keys[k]] = clone(source[keys[k]]);\n    }\n\n    return target;\n  }\n\n  return source;\n}\n\nfunction isValidKey(key: string) {\n  return ['__proto__', 'prototype', 'constructor'].indexOf(key) === -1;\n}\n\n/**\n * The default merger when Chart.helpers.merge is called without merger option.\n * Note(SB): also used by mergeConfig and mergeScaleConfig as fallback.\n * @private\n */\nexport function _merger(key: string, target: AnyObject, source: AnyObject, options: AnyObject) {\n  if (!isValidKey(key)) {\n    return;\n  }\n\n  const tval = target[key];\n  const sval = source[key];\n\n  if (isObject(tval) && isObject(sval)) {\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    merge(tval, sval, options);\n  } else {\n    target[key] = clone(sval);\n  }\n}\n\nexport interface MergeOptions {\n  merger?: (key: string, target: AnyObject, source: AnyObject, options?: AnyObject) => void;\n}\n\n/**\n * Recursively deep copies `source` properties into `target` with the given `options`.\n * IMPORTANT: `target` is not cloned and will be updated with `source` properties.\n * @param target - The target object in which all sources are merged into.\n * @param source - Object(s) to merge into `target`.\n * @param [options] - Merging options:\n * @param [options.merger] - The merge method (key, target, source, options)\n * @returns The `target` object.\n */\nexport function merge<T>(target: T, source: [], options?: MergeOptions): T;\nexport function merge<T, S1>(target: T, source: S1, options?: MergeOptions): T & S1;\nexport function merge<T, S1>(target: T, source: [S1], options?: MergeOptions): T & S1;\nexport function merge<T, S1, S2>(target: T, source: [S1, S2], options?: MergeOptions): T & S1 & S2;\nexport function merge<T, S1, S2, S3>(target: T, source: [S1, S2, S3], options?: MergeOptions): T & S1 & S2 & S3;\nexport function merge<T, S1, S2, S3, S4>(\n  target: T,\n  source: [S1, S2, S3, S4],\n  options?: MergeOptions\n): T & S1 & S2 & S3 & S4;\nexport function merge<T>(target: T, source: AnyObject[], options?: MergeOptions): AnyObject;\nexport function merge<T>(target: T, source: AnyObject[], options?: MergeOptions): AnyObject {\n  const sources = isArray(source) ? source : [source];\n  const ilen = sources.length;\n\n  if (!isObject(target)) {\n    return target as AnyObject;\n  }\n\n  options = options || {};\n  const merger = options.merger || _merger;\n  let current: AnyObject;\n\n  for (let i = 0; i < ilen; ++i) {\n    current = sources[i];\n    if (!isObject(current)) {\n      continue;\n    }\n\n    const keys = Object.keys(current);\n    for (let k = 0, klen = keys.length; k < klen; ++k) {\n      merger(keys[k], target, current, options as AnyObject);\n    }\n  }\n\n  return target;\n}\n\n/**\n * Recursively deep copies `source` properties into `target` *only* if not defined in target.\n * IMPORTANT: `target` is not cloned and will be updated with `source` properties.\n * @param target - The target object in which all sources are merged into.\n * @param source - Object(s) to merge into `target`.\n * @returns The `target` object.\n */\nexport function mergeIf<T>(target: T, source: []): T;\nexport function mergeIf<T, S1>(target: T, source: S1): T & S1;\nexport function mergeIf<T, S1>(target: T, source: [S1]): T & S1;\nexport function mergeIf<T, S1, S2>(target: T, source: [S1, S2]): T & S1 & S2;\nexport function mergeIf<T, S1, S2, S3>(target: T, source: [S1, S2, S3]): T & S1 & S2 & S3;\nexport function mergeIf<T, S1, S2, S3, S4>(target: T, source: [S1, S2, S3, S4]): T & S1 & S2 & S3 & S4;\nexport function mergeIf<T>(target: T, source: AnyObject[]): AnyObject;\nexport function mergeIf<T>(target: T, source: AnyObject[]): AnyObject {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  return merge<T>(target, source, {merger: _mergerIf});\n}\n\n/**\n * Merges source[key] in target[key] only if target[key] is undefined.\n * @private\n */\nexport function _mergerIf(key: string, target: AnyObject, source: AnyObject) {\n  if (!isValidKey(key)) {\n    return;\n  }\n\n  const tval = target[key];\n  const sval = source[key];\n\n  if (isObject(tval) && isObject(sval)) {\n    mergeIf(tval, sval);\n  } else if (!Object.prototype.hasOwnProperty.call(target, key)) {\n    target[key] = clone(sval);\n  }\n}\n\n/**\n * @private\n */\nexport function _deprecated(scope: string, value: unknown, previous: string, current: string) {\n  if (value !== undefined) {\n    console.warn(scope + ': \"' + previous +\n      '\" is deprecated. Please use \"' + current + '\" instead');\n  }\n}\n\n// resolveObjectKey resolver cache\nconst keyResolvers = {\n  // Chart.helpers.core resolveObjectKey should resolve empty key to root object\n  '': v => v,\n  // default resolvers\n  x: o => o.x,\n  y: o => o.y\n};\n\n/**\n * @private\n */\nexport function _splitKey(key: string) {\n  const parts = key.split('.');\n  const keys: string[] = [];\n  let tmp = '';\n  for (const part of parts) {\n    tmp += part;\n    if (tmp.endsWith('\\\\')) {\n      tmp = tmp.slice(0, -1) + '.';\n    } else {\n      keys.push(tmp);\n      tmp = '';\n    }\n  }\n  return keys;\n}\n\nfunction _getKeyResolver(key: string) {\n  const keys = _splitKey(key);\n  return obj => {\n    for (const k of keys) {\n      if (k === '') {\n        // For backward compatibility:\n        // Chart.helpers.core resolveObjectKey should break at empty key\n        break;\n      }\n      obj = obj && obj[k];\n    }\n    return obj;\n  };\n}\n\nexport function resolveObjectKey(obj: AnyObject, key: string): any {\n  const resolver = keyResolvers[key] || (keyResolvers[key] = _getKeyResolver(key));\n  return resolver(obj);\n}\n\n/**\n * @private\n */\nexport function _capitalize(str: string) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n}\n\n\nexport const defined = (value: unknown) => typeof value !== 'undefined';\n\nexport const isFunction = (value: unknown): value is (...args: any[]) => any => typeof value === 'function';\n\n// Adapted from https://stackoverflow.com/questions/31128855/comparing-ecma6-sets-for-equality#31129384\nexport const setsEqual = <T>(a: Set<T>, b: Set<T>) => {\n  if (a.size !== b.size) {\n    return false;\n  }\n\n  for (const item of a) {\n    if (!b.has(item)) {\n      return false;\n    }\n  }\n\n  return true;\n};\n\n/**\n * @param e - The event\n * @private\n */\nexport function _isClickEvent(e: ChartEvent) {\n  return e.type === 'mouseup' || e.type === 'click' || e.type === 'contextmenu';\n}\n", "import type {Point} from '../types/geometric.js';\nimport {isFinite as isFiniteNumber} from './helpers.core.js';\n\n/**\n * @alias Chart.helpers.math\n * @namespace\n */\n\nexport const PI = Math.PI;\nexport const TAU = 2 * PI;\nexport const PITAU = TAU + PI;\nexport const INFINITY = Number.POSITIVE_INFINITY;\nexport const RAD_PER_DEG = PI / 180;\nexport const HALF_PI = PI / 2;\nexport const QUARTER_PI = PI / 4;\nexport const TWO_THIRDS_PI = PI * 2 / 3;\n\nexport const log10 = Math.log10;\nexport const sign = Math.sign;\n\nexport function almostEquals(x: number, y: number, epsilon: number) {\n  return Math.abs(x - y) < epsilon;\n}\n\n/**\n * Implementation of the nice number algorithm used in determining where axis labels will go\n */\nexport function niceNum(range: number) {\n  const roundedRange = Math.round(range);\n  range = almostEquals(range, roundedRange, range / 1000) ? roundedRange : range;\n  const niceRange = Math.pow(10, Math.floor(log10(range)));\n  const fraction = range / niceRange;\n  const niceFraction = fraction <= 1 ? 1 : fraction <= 2 ? 2 : fraction <= 5 ? 5 : 10;\n  return niceFraction * niceRange;\n}\n\n/**\n * Returns an array of factors sorted from 1 to sqrt(value)\n * @private\n */\nexport function _factorize(value: number) {\n  const result: number[] = [];\n  const sqrt = Math.sqrt(value);\n  let i: number;\n\n  for (i = 1; i < sqrt; i++) {\n    if (value % i === 0) {\n      result.push(i);\n      result.push(value / i);\n    }\n  }\n  if (sqrt === (sqrt | 0)) { // if value is a square number\n    result.push(sqrt);\n  }\n\n  result.sort((a, b) => a - b).pop();\n  return result;\n}\n\n/**\n * Verifies that attempting to coerce n to string or number won't throw a TypeError.\n */\nfunction isNonPrimitive(n: unknown) {\n  return typeof n === 'symbol' || (typeof n === 'object' && n !== null && !(Symbol.toPrimitive in n || 'toString' in n || 'valueOf' in n));\n}\n\nexport function isNumber(n: unknown): n is number {\n  return !isNonPrimitive(n) && !isNaN(parseFloat(n as string)) && isFinite(n as number);\n}\n\nexport function almostWhole(x: number, epsilon: number) {\n  const rounded = Math.round(x);\n  return ((rounded - epsilon) <= x) && ((rounded + epsilon) >= x);\n}\n\n/**\n * @private\n */\nexport function _setMinAndMaxByKey(\n  array: Record<string, number>[],\n  target: { min: number, max: number },\n  property: string\n) {\n  let i: number, ilen: number, value: number;\n\n  for (i = 0, ilen = array.length; i < ilen; i++) {\n    value = array[i][property];\n    if (!isNaN(value)) {\n      target.min = Math.min(target.min, value);\n      target.max = Math.max(target.max, value);\n    }\n  }\n}\n\nexport function toRadians(degrees: number) {\n  return degrees * (PI / 180);\n}\n\nexport function toDegrees(radians: number) {\n  return radians * (180 / PI);\n}\n\n/**\n * Returns the number of decimal places\n * i.e. the number of digits after the decimal point, of the value of this Number.\n * @param x - A number.\n * @returns The number of decimal places.\n * @private\n */\nexport function _decimalPlaces(x: number) {\n  if (!isFiniteNumber(x)) {\n    return;\n  }\n  let e = 1;\n  let p = 0;\n  while (Math.round(x * e) / e !== x) {\n    e *= 10;\n    p++;\n  }\n  return p;\n}\n\n// Gets the angle from vertical upright to the point about a centre.\nexport function getAngleFromPoint(\n  centrePoint: Point,\n  anglePoint: Point\n) {\n  const distanceFromXCenter = anglePoint.x - centrePoint.x;\n  const distanceFromYCenter = anglePoint.y - centrePoint.y;\n  const radialDistanceFromCenter = Math.sqrt(distanceFromXCenter * distanceFromXCenter + distanceFromYCenter * distanceFromYCenter);\n\n  let angle = Math.atan2(distanceFromYCenter, distanceFromXCenter);\n\n  if (angle < (-0.5 * PI)) {\n    angle += TAU; // make sure the returned angle is in the range of (-PI/2, 3PI/2]\n  }\n\n  return {\n    angle,\n    distance: radialDistanceFromCenter\n  };\n}\n\nexport function distanceBetweenPoints(pt1: Point, pt2: Point) {\n  return Math.sqrt(Math.pow(pt2.x - pt1.x, 2) + Math.pow(pt2.y - pt1.y, 2));\n}\n\n/**\n * Shortest distance between angles, in either direction.\n * @private\n */\nexport function _angleDiff(a: number, b: number) {\n  return (a - b + PITAU) % TAU - PI;\n}\n\n/**\n * Normalize angle to be between 0 and 2*PI\n * @private\n */\nexport function _normalizeAngle(a: number) {\n  return (a % TAU + TAU) % TAU;\n}\n\n/**\n * @private\n */\nexport function _angleBetween(angle: number, start: number, end: number, sameAngleIsFullCircle?: boolean) {\n  const a = _normalizeAngle(angle);\n  const s = _normalizeAngle(start);\n  const e = _normalizeAngle(end);\n  const angleToStart = _normalizeAngle(s - a);\n  const angleToEnd = _normalizeAngle(e - a);\n  const startToAngle = _normalizeAngle(a - s);\n  const endToAngle = _normalizeAngle(a - e);\n  return a === s || a === e || (sameAngleIsFullCircle && s === e)\n    || (angleToStart > angleToEnd && startToAngle < endToAngle);\n}\n\n/**\n * Limit `value` between `min` and `max`\n * @param value\n * @param min\n * @param max\n * @private\n */\nexport function _limitValue(value: number, min: number, max: number) {\n  return Math.max(min, Math.min(max, value));\n}\n\n/**\n * @param {number} value\n * @private\n */\nexport function _int16Range(value: number) {\n  return _limitValue(value, -32768, 32767);\n}\n\n/**\n * @param value\n * @param start\n * @param end\n * @param [epsilon]\n * @private\n */\nexport function _isBetween(value: number, start: number, end: number, epsilon = 1e-6) {\n  return value >= Math.min(start, end) - epsilon && value <= Math.max(start, end) + epsilon;\n}\n", "import {_capitalize} from './helpers.core.js';\n\n/**\n * Binary search\n * @param table - the table search. must be sorted!\n * @param value - value to find\n * @param cmp\n * @private\n */\nexport function _lookup(\n  table: number[],\n  value: number,\n  cmp?: (value: number) => boolean\n): {lo: number, hi: number};\nexport function _lookup<T>(\n  table: T[],\n  value: number,\n  cmp: (value: number) => boolean\n): {lo: number, hi: number};\nexport function _lookup(\n  table: unknown[],\n  value: number,\n  cmp?: (value: number) => boolean\n) {\n  cmp = cmp || ((index) => table[index] < value);\n  let hi = table.length - 1;\n  let lo = 0;\n  let mid: number;\n\n  while (hi - lo > 1) {\n    mid = (lo + hi) >> 1;\n    if (cmp(mid)) {\n      lo = mid;\n    } else {\n      hi = mid;\n    }\n  }\n\n  return {lo, hi};\n}\n\n/**\n * Binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @param last - lookup last index\n * @private\n */\nexport const _lookupByKey = (\n  table: Record<string, number>[],\n  key: string,\n  value: number,\n  last?: boolean\n) =>\n  _lookup(table, value, last\n    ? index => {\n      const ti = table[index][key];\n      return ti < value || ti === value && table[index + 1][key] === value;\n    }\n    : index => table[index][key] < value);\n\n/**\n * Reverse binary search\n * @param table - the table search. must be sorted!\n * @param key - property name for the value in each entry\n * @param value - value to find\n * @private\n */\nexport const _rlookupByKey = (\n  table: Record<string, number>[],\n  key: string,\n  value: number\n) =>\n  _lookup(table, value, index => table[index][key] >= value);\n\n/**\n * Return subset of `values` between `min` and `max` inclusive.\n * Values are assumed to be in sorted order.\n * @param values - sorted array of values\n * @param min - min value\n * @param max - max value\n */\nexport function _filterBetween(values: number[], min: number, max: number) {\n  let start = 0;\n  let end = values.length;\n\n  while (start < end && values[start] < min) {\n    start++;\n  }\n  while (end > start && values[end - 1] > max) {\n    end--;\n  }\n\n  return start > 0 || end < values.length\n    ? values.slice(start, end)\n    : values;\n}\n\nconst arrayEvents = ['push', 'pop', 'shift', 'splice', 'unshift'] as const;\n\nexport interface ArrayListener<T> {\n  _onDataPush?(...item: T[]): void;\n  _onDataPop?(): void;\n  _onDataShift?(): void;\n  _onDataSplice?(index: number, deleteCount: number, ...items: T[]): void;\n  _onDataUnshift?(...item: T[]): void;\n}\n\n/**\n * Hooks the array methods that add or remove values ('push', pop', 'shift', 'splice',\n * 'unshift') and notify the listener AFTER the array has been altered. Listeners are\n * called on the '_onData*' callbacks (e.g. _onDataPush, etc.) with same arguments.\n */\nexport function listenArrayEvents<T>(array: T[], listener: ArrayListener<T>): void;\nexport function listenArrayEvents(array, listener) {\n  if (array._chartjs) {\n    array._chartjs.listeners.push(listener);\n    return;\n  }\n\n  Object.defineProperty(array, '_chartjs', {\n    configurable: true,\n    enumerable: false,\n    value: {\n      listeners: [listener]\n    }\n  });\n\n  arrayEvents.forEach((key) => {\n    const method = '_onData' + _capitalize(key);\n    const base = array[key];\n\n    Object.defineProperty(array, key, {\n      configurable: true,\n      enumerable: false,\n      value(...args) {\n        const res = base.apply(this, args);\n\n        array._chartjs.listeners.forEach((object) => {\n          if (typeof object[method] === 'function') {\n            object[method](...args);\n          }\n        });\n\n        return res;\n      }\n    });\n  });\n}\n\n\n/**\n * Removes the given array event listener and cleanup extra attached properties (such as\n * the _chartjs stub and overridden methods) if array doesn't have any more listeners.\n */\nexport function unlistenArrayEvents<T>(array: T[], listener: ArrayListener<T>): void;\nexport function unlistenArrayEvents(array, listener) {\n  const stub = array._chartjs;\n  if (!stub) {\n    return;\n  }\n\n  const listeners = stub.listeners;\n  const index = listeners.indexOf(listener);\n  if (index !== -1) {\n    listeners.splice(index, 1);\n  }\n\n  if (listeners.length > 0) {\n    return;\n  }\n\n  arrayEvents.forEach((key) => {\n    delete array[key];\n  });\n\n  delete array._chartjs;\n}\n\n/**\n * @param items\n */\nexport function _arrayUnique<T>(items: T[]) {\n  const set = new Set<T>(items);\n\n  if (set.size === items.length) {\n    return items;\n  }\n\n  return Array.from(set);\n}\n", "import type {ChartMeta, PointElement} from '../types/index.js';\n\nimport {_limitValue} from './helpers.math.js';\nimport {_lookupByKey} from './helpers.collection.js';\nimport {isNullOrUndef} from './helpers.core.js';\n\nexport function fontString(pixelSize: number, fontStyle: string, fontFamily: string) {\n  return fontStyle + ' ' + pixelSize + 'px ' + fontFamily;\n}\n\n/**\n* Request animation polyfill\n*/\nexport const requestAnimFrame = (function() {\n  if (typeof window === 'undefined') {\n    return function(callback) {\n      return callback();\n    };\n  }\n  return window.requestAnimationFrame;\n}());\n\n/**\n * Throttles calling `fn` once per animation frame\n * Latest arguments are used on the actual call\n */\nexport function throttled<TArgs extends Array<any>>(\n  fn: (...args: TArgs) => void,\n  thisArg: any,\n) {\n  let argsToUse = [] as TArgs;\n  let ticking = false;\n\n  return function(...args: TArgs) {\n    // Save the args for use later\n    argsToUse = args;\n    if (!ticking) {\n      ticking = true;\n      requestAnimFrame.call(window, () => {\n        ticking = false;\n        fn.apply(thisArg, argsToUse);\n      });\n    }\n  };\n}\n\n/**\n * Debounces calling `fn` for `delay` ms\n */\nexport function debounce<TArgs extends Array<any>>(fn: (...args: TArgs) => void, delay: number) {\n  let timeout;\n  return function(...args: TArgs) {\n    if (delay) {\n      clearTimeout(timeout);\n      timeout = setTimeout(fn, delay, args);\n    } else {\n      fn.apply(this, args);\n    }\n    return delay;\n  };\n}\n\n/**\n * Converts 'start' to 'left', 'end' to 'right' and others to 'center'\n * @private\n */\nexport const _toLeftRightCenter = (align: 'start' | 'end' | 'center') => align === 'start' ? 'left' : align === 'end' ? 'right' : 'center';\n\n/**\n * Returns `start`, `end` or `(start + end) / 2` depending on `align`. Defaults to `center`\n * @private\n */\nexport const _alignStartEnd = (align: 'start' | 'end' | 'center', start: number, end: number) => align === 'start' ? start : align === 'end' ? end : (start + end) / 2;\n\n/**\n * Returns `left`, `right` or `(left + right) / 2` depending on `align`. Defaults to `left`\n * @private\n */\nexport const _textX = (align: 'left' | 'right' | 'center', left: number, right: number, rtl: boolean) => {\n  const check = rtl ? 'left' : 'right';\n  return align === check ? right : align === 'center' ? (left + right) / 2 : left;\n};\n\n/**\n * Return start and count of visible points.\n * @private\n */\nexport function _getStartAndCountOfVisiblePoints(meta: ChartMeta<'line' | 'scatter'>, points: PointElement[], animationsDisabled: boolean) {\n  const pointCount = points.length;\n\n  let start = 0;\n  let count = pointCount;\n\n  if (meta._sorted) {\n    const {iScale, vScale, _parsed} = meta;\n    const spanGaps = meta.dataset ? meta.dataset.options ? meta.dataset.options.spanGaps : null : null;\n    const axis = iScale.axis;\n    const {min, max, minDefined, maxDefined} = iScale.getUserBounds();\n\n    if (minDefined) {\n      start = Math.min(\n        // @ts-expect-error Need to type _parsed\n        _lookupByKey(_parsed, axis, min).lo,\n        // @ts-expect-error Need to fix types on _lookupByKey\n        animationsDisabled ? pointCount : _lookupByKey(points, axis, iScale.getPixelForValue(min)).lo);\n      if (spanGaps) {\n        const distanceToDefinedLo = (_parsed\n          .slice(0, start + 1)\n          .reverse()\n          .findIndex(\n            point => !isNullOrUndef(point[vScale.axis])));\n        start -= Math.max(0, distanceToDefinedLo);\n      }\n      start = _limitValue(start, 0, pointCount - 1);\n    }\n    if (maxDefined) {\n      let end = Math.max(\n        // @ts-expect-error Need to type _parsed\n        _lookupByKey(_parsed, iScale.axis, max, true).hi + 1,\n        // @ts-expect-error Need to fix types on _lookupByKey\n        animationsDisabled ? 0 : _lookupByKey(points, axis, iScale.getPixelForValue(max), true).hi + 1);\n      if (spanGaps) {\n        const distanceToDefinedHi = (_parsed\n          .slice(end - 1)\n          .findIndex(\n            point => !isNullOrUndef(point[vScale.axis])));\n        end += Math.max(0, distanceToDefinedHi);\n      }\n      count = _limitValue(end, start, pointCount) - start;\n    } else {\n      count = pointCount - start;\n    }\n  }\n\n  return {start, count};\n}\n\n/**\n * Checks if the scale ranges have changed.\n * @param {object} meta - dataset meta.\n * @returns {boolean}\n * @private\n */\nexport function _scaleRangesChanged(meta) {\n  const {xScale, yScale, _scaleRanges} = meta;\n  const newRanges = {\n    xmin: xScale.min,\n    xmax: xScale.max,\n    ymin: yScale.min,\n    ymax: yScale.max\n  };\n  if (!_scaleRanges) {\n    meta._scaleRanges = newRanges;\n    return true;\n  }\n  const changed = _scaleRanges.xmin !== xScale.min\n\t\t|| _scaleRanges.xmax !== xScale.max\n\t\t|| _scaleRanges.ymin !== yScale.min\n\t\t|| _scaleRanges.ymax !== yScale.max;\n\n  Object.assign(_scaleRanges, newRanges);\n  return changed;\n}\n", "import {PI, TAU, HALF_PI} from './helpers.math.js';\n\nconst atEdge = (t: number) => t === 0 || t === 1;\nconst elasticIn = (t: number, s: number, p: number) => -(Math.pow(2, 10 * (t -= 1)) * Math.sin((t - s) * TAU / p));\nconst elasticOut = (t: number, s: number, p: number) => Math.pow(2, -10 * t) * Math.sin((t - s) * TAU / p) + 1;\n\n/**\n * Easing functions adapted from <PERSON>'s easing equations.\n * @namespace Chart.helpers.easing.effects\n * @see http://www.robertpenner.com/easing/\n */\nconst effects = {\n  linear: (t: number) => t,\n\n  easeInQuad: (t: number) => t * t,\n\n  easeOutQuad: (t: number) => -t * (t - 2),\n\n  easeInOutQuad: (t: number) => ((t /= 0.5) < 1)\n    ? 0.5 * t * t\n    : -0.5 * ((--t) * (t - 2) - 1),\n\n  easeInCubic: (t: number) => t * t * t,\n\n  easeOutCubic: (t: number) => (t -= 1) * t * t + 1,\n\n  easeInOutCubic: (t: number) => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t\n    : 0.5 * ((t -= 2) * t * t + 2),\n\n  easeInQuart: (t: number) => t * t * t * t,\n\n  easeOutQuart: (t: number) => -((t -= 1) * t * t * t - 1),\n\n  easeInOutQuart: (t: number) => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t * t\n    : -0.5 * ((t -= 2) * t * t * t - 2),\n\n  easeInQuint: (t: number) => t * t * t * t * t,\n\n  easeOutQuint: (t: number) => (t -= 1) * t * t * t * t + 1,\n\n  easeInOutQuint: (t: number) => ((t /= 0.5) < 1)\n    ? 0.5 * t * t * t * t * t\n    : 0.5 * ((t -= 2) * t * t * t * t + 2),\n\n  easeInSine: (t: number) => -Math.cos(t * HALF_PI) + 1,\n\n  easeOutSine: (t: number) => Math.sin(t * HALF_PI),\n\n  easeInOutSine: (t: number) => -0.5 * (Math.cos(PI * t) - 1),\n\n  easeInExpo: (t: number) => (t === 0) ? 0 : Math.pow(2, 10 * (t - 1)),\n\n  easeOutExpo: (t: number) => (t === 1) ? 1 : -Math.pow(2, -10 * t) + 1,\n\n  easeInOutExpo: (t: number) => atEdge(t) ? t : t < 0.5\n    ? 0.5 * Math.pow(2, 10 * (t * 2 - 1))\n    : 0.5 * (-Math.pow(2, -10 * (t * 2 - 1)) + 2),\n\n  easeInCirc: (t: number) => (t >= 1) ? t : -(Math.sqrt(1 - t * t) - 1),\n\n  easeOutCirc: (t: number) => Math.sqrt(1 - (t -= 1) * t),\n\n  easeInOutCirc: (t: number) => ((t /= 0.5) < 1)\n    ? -0.5 * (Math.sqrt(1 - t * t) - 1)\n    : 0.5 * (Math.sqrt(1 - (t -= 2) * t) + 1),\n\n  easeInElastic: (t: number) => atEdge(t) ? t : elasticIn(t, 0.075, 0.3),\n\n  easeOutElastic: (t: number) => atEdge(t) ? t : elasticOut(t, 0.075, 0.3),\n\n  easeInOutElastic(t: number) {\n    const s = 0.1125;\n    const p = 0.45;\n    return atEdge(t) ? t :\n      t < 0.5\n        ? 0.5 * elasticIn(t * 2, s, p)\n        : 0.5 + 0.5 * elasticOut(t * 2 - 1, s, p);\n  },\n\n  easeInBack(t: number) {\n    const s = 1.70158;\n    return t * t * ((s + 1) * t - s);\n  },\n\n  easeOutBack(t: number) {\n    const s = 1.70158;\n    return (t -= 1) * t * ((s + 1) * t + s) + 1;\n  },\n\n  easeInOutBack(t: number) {\n    let s = 1.70158;\n    if ((t /= 0.5) < 1) {\n      return 0.5 * (t * t * (((s *= (1.525)) + 1) * t - s));\n    }\n    return 0.5 * ((t -= 2) * t * (((s *= (1.525)) + 1) * t + s) + 2);\n  },\n\n  easeInBounce: (t: number) => 1 - effects.easeOutBounce(1 - t),\n\n  easeOutBounce(t: number) {\n    const m = 7.5625;\n    const d = 2.75;\n    if (t < (1 / d)) {\n      return m * t * t;\n    }\n    if (t < (2 / d)) {\n      return m * (t -= (1.5 / d)) * t + 0.75;\n    }\n    if (t < (2.5 / d)) {\n      return m * (t -= (2.25 / d)) * t + 0.9375;\n    }\n    return m * (t -= (2.625 / d)) * t + 0.984375;\n  },\n\n  easeInOutBounce: (t: number) => (t < 0.5)\n    ? effects.easeInBounce(t * 2) * 0.5\n    : effects.easeOutBounce(t * 2 - 1) * 0.5 + 0.5,\n} as const;\n\nexport type EasingFunction = keyof typeof effects\n\nexport default effects;\n", "import {Color} from '@kurkle/color';\n\nexport function isPatternOrGradient(value: unknown): value is CanvasPattern | CanvasGradient {\n  if (value && typeof value === 'object') {\n    const type = value.toString();\n    return type === '[object CanvasPattern]' || type === '[object CanvasGradient]';\n  }\n\n  return false;\n}\n\nexport function color(value: CanvasGradient): CanvasGradient;\nexport function color(value: CanvasPattern): CanvasPattern;\nexport function color(\n  value:\n  | string\n  | { r: number; g: number; b: number; a: number }\n  | [number, number, number]\n  | [number, number, number, number]\n): Color;\nexport function color(value) {\n  return isPatternOrGradient(value) ? value : new Color(value);\n}\n\nexport function getHoverColor(value: CanvasGradient): CanvasGradient;\nexport function getHoverColor(value: CanvasPattern): CanvasPattern;\nexport function getHoverColor(value: string): string;\nexport function getHoverColor(value) {\n  return isPatternOrGradient(value)\n    ? value\n    : new Color(value).saturate(0.5).darken(0.1).hexString();\n}\n", "const numbers = ['x', 'y', 'borderWidth', 'radius', 'tension'];\nconst colors = ['color', 'borderColor', 'backgroundColor'];\n\nexport function applyAnimationsDefaults(defaults) {\n  defaults.set('animation', {\n    delay: undefined,\n    duration: 1000,\n    easing: 'easeOutQuart',\n    fn: undefined,\n    from: undefined,\n    loop: undefined,\n    to: undefined,\n    type: undefined,\n  });\n\n  defaults.describe('animation', {\n    _fallback: false,\n    _indexable: false,\n    _scriptable: (name) => name !== 'onProgress' && name !== 'onComplete' && name !== 'fn',\n  });\n\n  defaults.set('animations', {\n    colors: {\n      type: 'color',\n      properties: colors\n    },\n    numbers: {\n      type: 'number',\n      properties: numbers\n    },\n  });\n\n  defaults.describe('animations', {\n    _fallback: 'animation',\n  });\n\n  defaults.set('transitions', {\n    active: {\n      animation: {\n        duration: 400\n      }\n    },\n    resize: {\n      animation: {\n        duration: 0\n      }\n    },\n    show: {\n      animations: {\n        colors: {\n          from: 'transparent'\n        },\n        visible: {\n          type: 'boolean',\n          duration: 0 // show immediately\n        },\n      }\n    },\n    hide: {\n      animations: {\n        colors: {\n          to: 'transparent'\n        },\n        visible: {\n          type: 'boolean',\n          easing: 'linear',\n          fn: v => v | 0 // for keeping the dataset visible all the way through the animation\n        },\n      }\n    }\n  });\n}\n", "export function applyLayoutsDefaults(defaults) {\n  defaults.set('layout', {\n    autoPadding: true,\n    padding: {\n      top: 0,\n      right: 0,\n      bottom: 0,\n      left: 0\n    }\n  });\n}\n", "\nconst intlCache = new Map<string, Intl.NumberFormat>();\n\nfunction getNumberFormat(locale: string, options?: Intl.NumberFormatOptions) {\n  options = options || {};\n  const cacheKey = locale + JSON.stringify(options);\n  let formatter = intlCache.get(cacheKey);\n  if (!formatter) {\n    formatter = new Intl.NumberFormat(locale, options);\n    intlCache.set(cacheKey, formatter);\n  }\n  return formatter;\n}\n\nexport function formatNumber(num: number, locale: string, options?: Intl.NumberFormatOptions) {\n  return getNumberFormat(locale, options).format(num);\n}\n", "import {isArray} from '../helpers/helpers.core.js';\nimport {formatNumber} from '../helpers/helpers.intl.js';\nimport {log10} from '../helpers/helpers.math.js';\n\n/**\n * Namespace to hold formatters for different types of ticks\n * @namespace Chart.Ticks.formatters\n */\nconst formatters = {\n  /**\n   * Formatter for value labels\n   * @method Chart.Ticks.formatters.values\n   * @param value the value to display\n   * @return {string|string[]} the label to display\n   */\n  values(value) {\n    return isArray(value) ? /** @type {string[]} */ (value) : '' + value;\n  },\n\n  /**\n   * Formatter for numeric ticks\n   * @method Chart.Ticks.formatters.numeric\n   * @param tickValue {number} the value to be formatted\n   * @param index {number} the position of the tickValue parameter in the ticks array\n   * @param ticks {object[]} the list of ticks being converted\n   * @return {string} string representation of the tickValue parameter\n   */\n  numeric(tickValue, index, ticks) {\n    if (tickValue === 0) {\n      return '0'; // never show decimal places for 0\n    }\n\n    const locale = this.chart.options.locale;\n    let notation;\n    let delta = tickValue; // This is used when there are less than 2 ticks as the tick interval.\n\n    if (ticks.length > 1) {\n      // all ticks are small or there huge numbers; use scientific notation\n      const maxTick = Math.max(Math.abs(ticks[0].value), Math.abs(ticks[ticks.length - 1].value));\n      if (maxTick < 1e-4 || maxTick > 1e+15) {\n        notation = 'scientific';\n      }\n\n      delta = calculateDelta(tickValue, ticks);\n    }\n\n    const logDelta = log10(Math.abs(delta));\n\n    // When datasets have values approaching Number.MAX_VALUE, the tick calculations might result in\n    // infinity and eventually NaN. Passing NaN for minimumFractionDigits or maximumFractionDigits\n    // will make the number formatter throw. So instead we check for isNaN and use a fallback value.\n    //\n    // toFixed has a max of 20 decimal places\n    const numDecimal = isNaN(logDelta) ? 1 : Math.max(Math.min(-1 * Math.floor(logDelta), 20), 0);\n\n    const options = {notation, minimumFractionDigits: numDecimal, maximumFractionDigits: numDecimal};\n    Object.assign(options, this.options.ticks.format);\n\n    return formatNumber(tickValue, locale, options);\n  },\n\n\n  /**\n   * Formatter for logarithmic ticks\n   * @method Chart.Ticks.formatters.logarithmic\n   * @param tickValue {number} the value to be formatted\n   * @param index {number} the position of the tickValue parameter in the ticks array\n   * @param ticks {object[]} the list of ticks being converted\n   * @return {string} string representation of the tickValue parameter\n   */\n  logarithmic(tickValue, index, ticks) {\n    if (tickValue === 0) {\n      return '0';\n    }\n    const remain = ticks[index].significand || (tickValue / (Math.pow(10, Math.floor(log10(tickValue)))));\n    if ([1, 2, 3, 5, 10, 15].includes(remain) || index > 0.8 * ticks.length) {\n      return formatters.numeric.call(this, tickValue, index, ticks);\n    }\n    return '';\n  }\n\n};\n\n\nfunction calculateDelta(tickValue, ticks) {\n  // Figure out how many digits to show\n  // The space between the first two ticks might be smaller than normal spacing\n  let delta = ticks.length > 3 ? ticks[2].value - ticks[1].value : ticks[1].value - ticks[0].value;\n\n  // If we have a number like 2.5 as the delta, figure out how many decimal places we need\n  if (Math.abs(delta) >= 1 && tickValue !== Math.floor(tickValue)) {\n    // not an integer\n    delta = tickValue - Math.floor(tickValue);\n  }\n  return delta;\n}\n\n/**\n * Namespace to hold static tick generation functions\n * @namespace Chart.Ticks\n */\nexport default {formatters};\n", "import Ticks from './core.ticks.js';\n\nexport function applyScaleDefaults(defaults) {\n  defaults.set('scale', {\n    display: true,\n    offset: false,\n    reverse: false,\n    beginAtZero: false,\n\n    /**\n     * Scale boundary strategy (bypassed by min/max time options)\n     * - `data`: make sure data are fully visible, ticks outside are removed\n     * - `ticks`: make sure ticks are fully visible, data outside are truncated\n     * @see https://github.com/chartjs/Chart.js/pull/4556\n     * @since 3.0.0\n     */\n    bounds: 'ticks',\n\n    clip: true,\n\n    /**\n     * Addition grace added to max and reduced from min data value.\n     * @since 3.0.0\n     */\n    grace: 0,\n\n    // grid line settings\n    grid: {\n      display: true,\n      lineWidth: 1,\n      drawOnChartArea: true,\n      drawTicks: true,\n      tickLength: 8,\n      tickWidth: (_ctx, options) => options.lineWidth,\n      tickColor: (_ctx, options) => options.color,\n      offset: false,\n    },\n\n    border: {\n      display: true,\n      dash: [],\n      dashOffset: 0.0,\n      width: 1\n    },\n\n    // scale title\n    title: {\n      // display property\n      display: false,\n\n      // actual label\n      text: '',\n\n      // top/bottom padding\n      padding: {\n        top: 4,\n        bottom: 4\n      }\n    },\n\n    // label settings\n    ticks: {\n      minRotation: 0,\n      maxRotation: 50,\n      mirror: false,\n      textStrokeWidth: 0,\n      textStrokeColor: '',\n      padding: 3,\n      display: true,\n      autoSkip: true,\n      autoSkipPadding: 3,\n      labelOffset: 0,\n      // We pass through arrays to be rendered as multiline labels, we convert Others to strings here.\n      callback: Ticks.formatters.values,\n      minor: {},\n      major: {},\n      align: 'center',\n      crossAlign: 'near',\n\n      showLabelBackdrop: false,\n      backdropColor: 'rgba(255, 255, 255, 0.75)',\n      backdropPadding: 2,\n    }\n  });\n\n  defaults.route('scale.ticks', 'color', '', 'color');\n  defaults.route('scale.grid', 'color', '', 'borderColor');\n  defaults.route('scale.border', 'color', '', 'borderColor');\n  defaults.route('scale.title', 'color', '', 'color');\n\n  defaults.describe('scale', {\n    _fallback: false,\n    _scriptable: (name) => !name.startsWith('before') && !name.startsWith('after') && name !== 'callback' && name !== 'parser',\n    _indexable: (name) => name !== 'borderDash' && name !== 'tickBorderDash' && name !== 'dash',\n  });\n\n  defaults.describe('scales', {\n    _fallback: 'scale',\n  });\n\n  defaults.describe('scale.ticks', {\n    _scriptable: (name) => name !== 'backdropPadding' && name !== 'callback',\n    _indexable: (name) => name !== 'backdropPadding',\n  });\n}\n", "import {getHoverColor} from '../helpers/helpers.color.js';\nimport {isObject, merge, valueOrDefault} from '../helpers/helpers.core.js';\nimport {applyAnimationsDefaults} from './core.animations.defaults.js';\nimport {applyLayoutsDefaults} from './core.layouts.defaults.js';\nimport {applyScaleDefaults} from './core.scale.defaults.js';\n\nexport const overrides = Object.create(null);\nexport const descriptors = Object.create(null);\n\n/**\n * @param {object} node\n * @param {string} key\n * @return {object}\n */\nfunction getScope(node, key) {\n  if (!key) {\n    return node;\n  }\n  const keys = key.split('.');\n  for (let i = 0, n = keys.length; i < n; ++i) {\n    const k = keys[i];\n    node = node[k] || (node[k] = Object.create(null));\n  }\n  return node;\n}\n\nfunction set(root, scope, values) {\n  if (typeof scope === 'string') {\n    return merge(getScope(root, scope), values);\n  }\n  return merge(getScope(root, ''), scope);\n}\n\n/**\n * Please use the module's default export which provides a singleton instance\n * Note: class is exported for typedoc\n */\nexport class Defaults {\n  constructor(_descriptors, _appliers) {\n    this.animation = undefined;\n    this.backgroundColor = 'rgba(0,0,0,0.1)';\n    this.borderColor = 'rgba(0,0,0,0.1)';\n    this.color = '#666';\n    this.datasets = {};\n    this.devicePixelRatio = (context) => context.chart.platform.getDevicePixelRatio();\n    this.elements = {};\n    this.events = [\n      'mousemove',\n      'mouseout',\n      'click',\n      'touchstart',\n      'touchmove'\n    ];\n    this.font = {\n      family: \"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif\",\n      size: 12,\n      style: 'normal',\n      lineHeight: 1.2,\n      weight: null\n    };\n    this.hover = {};\n    this.hoverBackgroundColor = (ctx, options) => getHoverColor(options.backgroundColor);\n    this.hoverBorderColor = (ctx, options) => getHoverColor(options.borderColor);\n    this.hoverColor = (ctx, options) => getHoverColor(options.color);\n    this.indexAxis = 'x';\n    this.interaction = {\n      mode: 'nearest',\n      intersect: true,\n      includeInvisible: false\n    };\n    this.maintainAspectRatio = true;\n    this.onHover = null;\n    this.onClick = null;\n    this.parsing = true;\n    this.plugins = {};\n    this.responsive = true;\n    this.scale = undefined;\n    this.scales = {};\n    this.showLine = true;\n    this.drawActiveElementsOnTop = true;\n\n    this.describe(_descriptors);\n    this.apply(_appliers);\n  }\n\n  /**\n\t * @param {string|object} scope\n\t * @param {object} [values]\n\t */\n  set(scope, values) {\n    return set(this, scope, values);\n  }\n\n  /**\n\t * @param {string} scope\n\t */\n  get(scope) {\n    return getScope(this, scope);\n  }\n\n  /**\n\t * @param {string|object} scope\n\t * @param {object} [values]\n\t */\n  describe(scope, values) {\n    return set(descriptors, scope, values);\n  }\n\n  override(scope, values) {\n    return set(overrides, scope, values);\n  }\n\n  /**\n\t * Routes the named defaults to fallback to another scope/name.\n\t * This routing is useful when those target values, like defaults.color, are changed runtime.\n\t * If the values would be copied, the runtime change would not take effect. By routing, the\n\t * fallback is evaluated at each access, so its always up to date.\n\t *\n\t * Example:\n\t *\n\t * \tdefaults.route('elements.arc', 'backgroundColor', '', 'color')\n\t *   - reads the backgroundColor from defaults.color when undefined locally\n\t *\n\t * @param {string} scope Scope this route applies to.\n\t * @param {string} name Property name that should be routed to different namespace when not defined here.\n\t * @param {string} targetScope The namespace where those properties should be routed to.\n\t * Empty string ('') is the root of defaults.\n\t * @param {string} targetName The target name in the target scope the property should be routed to.\n\t */\n  route(scope, name, targetScope, targetName) {\n    const scopeObject = getScope(this, scope);\n    const targetScopeObject = getScope(this, targetScope);\n    const privateName = '_' + name;\n\n    Object.defineProperties(scopeObject, {\n      // A private property is defined to hold the actual value, when this property is set in its scope (set in the setter)\n      [privateName]: {\n        value: scopeObject[name],\n        writable: true\n      },\n      // The actual property is defined as getter/setter so we can do the routing when value is not locally set.\n      [name]: {\n        enumerable: true,\n        get() {\n          const local = this[privateName];\n          const target = targetScopeObject[targetName];\n          if (isObject(local)) {\n            return Object.assign({}, target, local);\n          }\n          return valueOrDefault(local, target);\n        },\n        set(value) {\n          this[privateName] = value;\n        }\n      }\n    });\n  }\n\n  apply(appliers) {\n    appliers.forEach((apply) => apply(this));\n  }\n}\n\n// singleton instance\nexport default /* #__PURE__ */ new Defaults({\n  _scriptable: (name) => !name.startsWith('on'),\n  _indexable: (name) => name !== 'events',\n  hover: {\n    _fallback: 'interaction'\n  },\n  interaction: {\n    _scriptable: false,\n    _indexable: false,\n  }\n}, [applyAnimationsDefaults, applyLayoutsDefaults, applyScaleDefaults]);\n", "import type {\n  Chart,\n  Point,\n  FontSpec,\n  CanvasFontSpec,\n  PointStyle,\n  RenderTextOpts,\n  BackdropOptions\n} from '../types/index.js';\nimport type {\n  TRBL,\n  SplinePoint,\n  RoundedRect,\n  TRBLCorners\n} from '../types/geometric.js';\nimport {isArray, isNullOrUndef} from './helpers.core.js';\nimport {PI, TAU, HALF_PI, QUARTER_PI, TWO_THIRDS_PI, RAD_PER_DEG} from './helpers.math.js';\n\n/**\n * Converts the given font object into a CSS font string.\n * @param font - A font object.\n * @return The CSS font string. See https://developer.mozilla.org/en-US/docs/Web/CSS/font\n * @private\n */\nexport function toFontString(font: FontSpec) {\n  if (!font || isNullOrUndef(font.size) || isNullOrUndef(font.family)) {\n    return null;\n  }\n\n  return (font.style ? font.style + ' ' : '')\n\t\t+ (font.weight ? font.weight + ' ' : '')\n\t\t+ font.size + 'px '\n\t\t+ font.family;\n}\n\n/**\n * @private\n */\nexport function _measureText(\n  ctx: CanvasRenderingContext2D,\n  data: Record<string, number>,\n  gc: string[],\n  longest: number,\n  string: string\n) {\n  let textWidth = data[string];\n  if (!textWidth) {\n    textWidth = data[string] = ctx.measureText(string).width;\n    gc.push(string);\n  }\n  if (textWidth > longest) {\n    longest = textWidth;\n  }\n  return longest;\n}\n\ntype Thing = string | undefined | null\ntype Things = (Thing | Thing[])[]\n\n/**\n * @private\n */\n// eslint-disable-next-line complexity\nexport function _longestText(\n  ctx: CanvasRenderingContext2D,\n  font: string,\n  arrayOfThings: Things,\n  cache?: {data?: Record<string, number>, garbageCollect?: string[], font?: string}\n) {\n  cache = cache || {};\n  let data = cache.data = cache.data || {};\n  let gc = cache.garbageCollect = cache.garbageCollect || [];\n\n  if (cache.font !== font) {\n    data = cache.data = {};\n    gc = cache.garbageCollect = [];\n    cache.font = font;\n  }\n\n  ctx.save();\n\n  ctx.font = font;\n  let longest = 0;\n  const ilen = arrayOfThings.length;\n  let i: number, j: number, jlen: number, thing: Thing | Thing[], nestedThing: Thing | Thing[];\n  for (i = 0; i < ilen; i++) {\n    thing = arrayOfThings[i];\n\n    // Undefined strings and arrays should not be measured\n    if (thing !== undefined && thing !== null && !isArray(thing)) {\n      longest = _measureText(ctx, data, gc, longest, thing);\n    } else if (isArray(thing)) {\n      // if it is an array lets measure each element\n      // to do maybe simplify this function a bit so we can do this more recursively?\n      for (j = 0, jlen = thing.length; j < jlen; j++) {\n        nestedThing = thing[j];\n        // Undefined strings and arrays should not be measured\n        if (nestedThing !== undefined && nestedThing !== null && !isArray(nestedThing)) {\n          longest = _measureText(ctx, data, gc, longest, nestedThing);\n        }\n      }\n    }\n  }\n\n  ctx.restore();\n\n  const gcLen = gc.length / 2;\n  if (gcLen > arrayOfThings.length) {\n    for (i = 0; i < gcLen; i++) {\n      delete data[gc[i]];\n    }\n    gc.splice(0, gcLen);\n  }\n  return longest;\n}\n\n/**\n * Returns the aligned pixel value to avoid anti-aliasing blur\n * @param chart - The chart instance.\n * @param pixel - A pixel value.\n * @param width - The width of the element.\n * @returns The aligned pixel value.\n * @private\n */\nexport function _alignPixel(chart: Chart, pixel: number, width: number) {\n  const devicePixelRatio = chart.currentDevicePixelRatio;\n  const halfWidth = width !== 0 ? Math.max(width / 2, 0.5) : 0;\n  return Math.round((pixel - halfWidth) * devicePixelRatio) / devicePixelRatio + halfWidth;\n}\n\n/**\n * Clears the entire canvas.\n */\nexport function clearCanvas(canvas?: HTMLCanvasElement, ctx?: CanvasRenderingContext2D) {\n  if (!ctx && !canvas) {\n    return;\n  }\n\n  ctx = ctx || canvas.getContext('2d');\n\n  ctx.save();\n  // canvas.width and canvas.height do not consider the canvas transform,\n  // while clearRect does\n  ctx.resetTransform();\n  ctx.clearRect(0, 0, canvas.width, canvas.height);\n  ctx.restore();\n}\n\nexport interface DrawPointOptions {\n  pointStyle: PointStyle;\n  rotation?: number;\n  radius: number;\n  borderWidth: number;\n}\n\nexport function drawPoint(\n  ctx: CanvasRenderingContext2D,\n  options: DrawPointOptions,\n  x: number,\n  y: number\n) {\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  drawPointLegend(ctx, options, x, y, null);\n}\n\n// eslint-disable-next-line complexity\nexport function drawPointLegend(\n  ctx: CanvasRenderingContext2D,\n  options: DrawPointOptions,\n  x: number,\n  y: number,\n  w: number\n) {\n  let type: string, xOffset: number, yOffset: number, size: number, cornerRadius: number, width: number, xOffsetW: number, yOffsetW: number;\n  const style = options.pointStyle;\n  const rotation = options.rotation;\n  const radius = options.radius;\n  let rad = (rotation || 0) * RAD_PER_DEG;\n\n  if (style && typeof style === 'object') {\n    type = style.toString();\n    if (type === '[object HTMLImageElement]' || type === '[object HTMLCanvasElement]') {\n      ctx.save();\n      ctx.translate(x, y);\n      ctx.rotate(rad);\n      ctx.drawImage(style, -style.width / 2, -style.height / 2, style.width, style.height);\n      ctx.restore();\n      return;\n    }\n  }\n\n  if (isNaN(radius) || radius <= 0) {\n    return;\n  }\n\n  ctx.beginPath();\n\n  switch (style) {\n  // Default includes circle\n    default:\n      if (w) {\n        ctx.ellipse(x, y, w / 2, radius, 0, 0, TAU);\n      } else {\n        ctx.arc(x, y, radius, 0, TAU);\n      }\n      ctx.closePath();\n      break;\n    case 'triangle':\n      width = w ? w / 2 : radius;\n      ctx.moveTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      rad += TWO_THIRDS_PI;\n      ctx.lineTo(x + Math.sin(rad) * width, y - Math.cos(rad) * radius);\n      ctx.closePath();\n      break;\n    case 'rectRounded':\n    // NOTE: the rounded rect implementation changed to use `arc` instead of\n    // `quadraticCurveTo` since it generates better results when rect is\n    // almost a circle. 0.516 (instead of 0.5) produces results with visually\n    // closer proportion to the previous impl and it is inscribed in the\n    // circle with `radius`. For more details, see the following PRs:\n    // https://github.com/chartjs/Chart.js/issues/5597\n    // https://github.com/chartjs/Chart.js/issues/5858\n      cornerRadius = radius * 0.516;\n      size = radius - cornerRadius;\n      xOffset = Math.cos(rad + QUARTER_PI) * size;\n      xOffsetW = Math.cos(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n      yOffset = Math.sin(rad + QUARTER_PI) * size;\n      yOffsetW = Math.sin(rad + QUARTER_PI) * (w ? w / 2 - cornerRadius : size);\n      ctx.arc(x - xOffsetW, y - yOffset, cornerRadius, rad - PI, rad - HALF_PI);\n      ctx.arc(x + yOffsetW, y - xOffset, cornerRadius, rad - HALF_PI, rad);\n      ctx.arc(x + xOffsetW, y + yOffset, cornerRadius, rad, rad + HALF_PI);\n      ctx.arc(x - yOffsetW, y + xOffset, cornerRadius, rad + HALF_PI, rad + PI);\n      ctx.closePath();\n      break;\n    case 'rect':\n      if (!rotation) {\n        size = Math.SQRT1_2 * radius;\n        width = w ? w / 2 : size;\n        ctx.rect(x - width, y - size, 2 * width, 2 * size);\n        break;\n      }\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'rectRot':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      ctx.closePath();\n      break;\n    case 'crossRot':\n      rad += QUARTER_PI;\n    /* falls through */\n    case 'cross':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      break;\n    case 'star':\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      rad += QUARTER_PI;\n      xOffsetW = Math.cos(rad) * (w ? w / 2 : radius);\n      xOffset = Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      yOffsetW = Math.sin(rad) * (w ? w / 2 : radius);\n      ctx.moveTo(x - xOffsetW, y - yOffset);\n      ctx.lineTo(x + xOffsetW, y + yOffset);\n      ctx.moveTo(x + yOffsetW, y - xOffset);\n      ctx.lineTo(x - yOffsetW, y + xOffset);\n      break;\n    case 'line':\n      xOffset = w ? w / 2 : Math.cos(rad) * radius;\n      yOffset = Math.sin(rad) * radius;\n      ctx.moveTo(x - xOffset, y - yOffset);\n      ctx.lineTo(x + xOffset, y + yOffset);\n      break;\n    case 'dash':\n      ctx.moveTo(x, y);\n      ctx.lineTo(x + Math.cos(rad) * (w ? w / 2 : radius), y + Math.sin(rad) * radius);\n      break;\n    case false:\n      ctx.closePath();\n      break;\n  }\n\n  ctx.fill();\n  if (options.borderWidth > 0) {\n    ctx.stroke();\n  }\n}\n\n/**\n * Returns true if the point is inside the rectangle\n * @param point - The point to test\n * @param area - The rectangle\n * @param margin - allowed margin\n * @private\n */\nexport function _isPointInArea(\n  point: Point,\n  area: TRBL,\n  margin?: number\n) {\n  margin = margin || 0.5; // margin - default is to match rounded decimals\n\n  return !area || (point && point.x > area.left - margin && point.x < area.right + margin &&\n\t\tpoint.y > area.top - margin && point.y < area.bottom + margin);\n}\n\nexport function clipArea(ctx: CanvasRenderingContext2D, area: TRBL) {\n  ctx.save();\n  ctx.beginPath();\n  ctx.rect(area.left, area.top, area.right - area.left, area.bottom - area.top);\n  ctx.clip();\n}\n\nexport function unclipArea(ctx: CanvasRenderingContext2D) {\n  ctx.restore();\n}\n\n/**\n * @private\n */\nexport function _steppedLineTo(\n  ctx: CanvasRenderingContext2D,\n  previous: Point,\n  target: Point,\n  flip?: boolean,\n  mode?: string\n) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  if (mode === 'middle') {\n    const midpoint = (previous.x + target.x) / 2.0;\n    ctx.lineTo(midpoint, previous.y);\n    ctx.lineTo(midpoint, target.y);\n  } else if (mode === 'after' !== !!flip) {\n    ctx.lineTo(previous.x, target.y);\n  } else {\n    ctx.lineTo(target.x, previous.y);\n  }\n  ctx.lineTo(target.x, target.y);\n}\n\n/**\n * @private\n */\nexport function _bezierCurveTo(\n  ctx: CanvasRenderingContext2D,\n  previous: SplinePoint,\n  target: SplinePoint,\n  flip?: boolean\n) {\n  if (!previous) {\n    return ctx.lineTo(target.x, target.y);\n  }\n  ctx.bezierCurveTo(\n    flip ? previous.cp1x : previous.cp2x,\n    flip ? previous.cp1y : previous.cp2y,\n    flip ? target.cp2x : target.cp1x,\n    flip ? target.cp2y : target.cp1y,\n    target.x,\n    target.y);\n}\n\nfunction setRenderOpts(ctx: CanvasRenderingContext2D, opts: RenderTextOpts) {\n  if (opts.translation) {\n    ctx.translate(opts.translation[0], opts.translation[1]);\n  }\n\n  if (!isNullOrUndef(opts.rotation)) {\n    ctx.rotate(opts.rotation);\n  }\n\n  if (opts.color) {\n    ctx.fillStyle = opts.color;\n  }\n\n  if (opts.textAlign) {\n    ctx.textAlign = opts.textAlign;\n  }\n\n  if (opts.textBaseline) {\n    ctx.textBaseline = opts.textBaseline;\n  }\n}\n\nfunction decorateText(\n  ctx: CanvasRenderingContext2D,\n  x: number,\n  y: number,\n  line: string,\n  opts: RenderTextOpts\n) {\n  if (opts.strikethrough || opts.underline) {\n    /**\n     * Now that IE11 support has been dropped, we can use more\n     * of the TextMetrics object. The actual bounding boxes\n     * are unflagged in Chrome, Firefox, Edge, and Safari so they\n     * can be safely used.\n     * See https://developer.mozilla.org/en-US/docs/Web/API/TextMetrics#Browser_compatibility\n     */\n    const metrics = ctx.measureText(line);\n    const left = x - metrics.actualBoundingBoxLeft;\n    const right = x + metrics.actualBoundingBoxRight;\n    const top = y - metrics.actualBoundingBoxAscent;\n    const bottom = y + metrics.actualBoundingBoxDescent;\n    const yDecoration = opts.strikethrough ? (top + bottom) / 2 : bottom;\n\n    ctx.strokeStyle = ctx.fillStyle;\n    ctx.beginPath();\n    ctx.lineWidth = opts.decorationWidth || 2;\n    ctx.moveTo(left, yDecoration);\n    ctx.lineTo(right, yDecoration);\n    ctx.stroke();\n  }\n}\n\nfunction drawBackdrop(ctx: CanvasRenderingContext2D, opts: BackdropOptions) {\n  const oldColor = ctx.fillStyle;\n\n  ctx.fillStyle = opts.color as string;\n  ctx.fillRect(opts.left, opts.top, opts.width, opts.height);\n  ctx.fillStyle = oldColor;\n}\n\n/**\n * Render text onto the canvas\n */\nexport function renderText(\n  ctx: CanvasRenderingContext2D,\n  text: string | string[],\n  x: number,\n  y: number,\n  font: CanvasFontSpec,\n  opts: RenderTextOpts = {}\n) {\n  const lines = isArray(text) ? text : [text];\n  const stroke = opts.strokeWidth > 0 && opts.strokeColor !== '';\n  let i: number, line: string;\n\n  ctx.save();\n  ctx.font = font.string;\n  setRenderOpts(ctx, opts);\n\n  for (i = 0; i < lines.length; ++i) {\n    line = lines[i];\n\n    if (opts.backdrop) {\n      drawBackdrop(ctx, opts.backdrop);\n    }\n\n    if (stroke) {\n      if (opts.strokeColor) {\n        ctx.strokeStyle = opts.strokeColor;\n      }\n\n      if (!isNullOrUndef(opts.strokeWidth)) {\n        ctx.lineWidth = opts.strokeWidth;\n      }\n\n      ctx.strokeText(line, x, y, opts.maxWidth);\n    }\n\n    ctx.fillText(line, x, y, opts.maxWidth);\n    decorateText(ctx, x, y, line, opts);\n\n    y += Number(font.lineHeight);\n  }\n\n  ctx.restore();\n}\n\n/**\n * Add a path of a rectangle with rounded corners to the current sub-path\n * @param ctx - Context\n * @param rect - Bounding rect\n */\nexport function addRoundedRectPath(\n  ctx: CanvasRenderingContext2D,\n  rect: RoundedRect & { radius: TRBLCorners }\n) {\n  const {x, y, w, h, radius} = rect;\n\n  // top left arc\n  ctx.arc(x + radius.topLeft, y + radius.topLeft, radius.topLeft, 1.5 * PI, PI, true);\n\n  // line from top left to bottom left\n  ctx.lineTo(x, y + h - radius.bottomLeft);\n\n  // bottom left arc\n  ctx.arc(x + radius.bottomLeft, y + h - radius.bottomLeft, radius.bottomLeft, PI, HALF_PI, true);\n\n  // line from bottom left to bottom right\n  ctx.lineTo(x + w - radius.bottomRight, y + h);\n\n  // bottom right arc\n  ctx.arc(x + w - radius.bottomRight, y + h - radius.bottomRight, radius.bottomRight, HALF_PI, 0, true);\n\n  // line from bottom right to top right\n  ctx.lineTo(x + w, y + radius.topRight);\n\n  // top right arc\n  ctx.arc(x + w - radius.topRight, y + radius.topRight, radius.topRight, 0, -HALF_PI, true);\n\n  // line from top right to top left\n  ctx.lineTo(x + radius.topLeft, y);\n}\n", "import defaults from '../core/core.defaults.js';\nimport {isArray, isObject, toDimension, valueOrDefault} from './helpers.core.js';\nimport {toFontString} from './helpers.canvas.js';\nimport type {ChartArea, FontSpec, Point} from '../types/index.js';\nimport type {TRBL, TRBLCorners} from '../types/geometric.js';\n\nconst LINE_HEIGHT = /^(normal|(\\d+(?:\\.\\d+)?)(px|em|%)?)$/;\nconst FONT_STYLE = /^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;\n\n/**\n * @alias Chart.helpers.options\n * @namespace\n */\n/**\n * Converts the given line height `value` in pixels for a specific font `size`.\n * @param value - The lineHeight to parse (eg. 1.6, '14px', '75%', '1.6em').\n * @param size - The font size (in pixels) used to resolve relative `value`.\n * @returns The effective line height in pixels (size * 1.2 if value is invalid).\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/line-height\n * @since 2.7.0\n */\nexport function toLineHeight(value: number | string, size: number): number {\n  const matches = ('' + value).match(LINE_HEIGHT);\n  if (!matches || matches[1] === 'normal') {\n    return size * 1.2;\n  }\n\n  value = +matches[2];\n\n  switch (matches[3]) {\n    case 'px':\n      return value;\n    case '%':\n      value /= 100;\n      break;\n    default:\n      break;\n  }\n\n  return size * value;\n}\n\nconst numberOrZero = (v: unknown) => +v || 0;\n\n/**\n * @param value\n * @param props\n */\nexport function _readValueToProps<K extends string>(value: number | Record<K, number>, props: K[]): Record<K, number>;\nexport function _readValueToProps<K extends string, T extends string>(value: number | Record<K & T, number>, props: Record<T, K>): Record<T, number>;\nexport function _readValueToProps(value: number | Record<string, number>, props: string[] | Record<string, string>) {\n  const ret = {};\n  const objProps = isObject(props);\n  const keys = objProps ? Object.keys(props) : props;\n  const read = isObject(value)\n    ? objProps\n      ? prop => valueOrDefault(value[prop], value[props[prop]])\n      : prop => value[prop]\n    : () => value;\n\n  for (const prop of keys) {\n    ret[prop] = numberOrZero(read(prop));\n  }\n  return ret;\n}\n\n/**\n * Converts the given value into a TRBL object.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left)\n * @since 3.0.0\n */\nexport function toTRBL(value: number | TRBL | Point) {\n  return _readValueToProps(value, {top: 'y', right: 'x', bottom: 'y', left: 'x'});\n}\n\n/**\n * Converts the given value into a TRBL corners object (similar with css border-radius).\n * @param value - If a number, set the value to all TRBL corner components,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n * @returns The TRBL corner values (topLeft, topRight, bottomLeft, bottomRight)\n * @since 3.0.0\n */\nexport function toTRBLCorners(value: number | TRBLCorners) {\n  return _readValueToProps(value, ['topLeft', 'topRight', 'bottomLeft', 'bottomRight']);\n}\n\n/**\n * Converts the given value into a padding object with pre-computed width/height.\n * @param value - If a number, set the value to all TRBL component,\n *  else, if an object, use defined properties and sets undefined ones to 0.\n *  x / y are shorthands for same value for left/right and top/bottom.\n * @returns The padding values (top, right, bottom, left, width, height)\n * @since 2.7.0\n */\nexport function toPadding(value?: number | TRBL): ChartArea {\n  const obj = toTRBL(value) as ChartArea;\n\n  obj.width = obj.left + obj.right;\n  obj.height = obj.top + obj.bottom;\n\n  return obj;\n}\n\n/**\n * Parses font options and returns the font object.\n * @param options - A object that contains font options to be parsed.\n * @param fallback - A object that contains fallback font options.\n * @return The font object.\n * @private\n */\n\nexport function toFont(options: Partial<FontSpec>, fallback?: Partial<FontSpec>) {\n  options = options || {};\n  fallback = fallback || defaults.font as FontSpec;\n\n  let size = valueOrDefault(options.size, fallback.size);\n\n  if (typeof size === 'string') {\n    size = parseInt(size, 10);\n  }\n  let style = valueOrDefault(options.style, fallback.style);\n  if (style && !('' + style).match(FONT_STYLE)) {\n    console.warn('Invalid font style specified: \"' + style + '\"');\n    style = undefined;\n  }\n\n  const font = {\n    family: valueOrDefault(options.family, fallback.family),\n    lineHeight: toLineHeight(valueOrDefault(options.lineHeight, fallback.lineHeight), size),\n    size,\n    style,\n    weight: valueOrDefault(options.weight, fallback.weight),\n    string: ''\n  };\n\n  font.string = toFontString(font);\n  return font;\n}\n\n/**\n * Evaluates the given `inputs` sequentially and returns the first defined value.\n * @param inputs - An array of values, falling back to the last value.\n * @param context - If defined and the current value is a function, the value\n * is called with `context` as first argument and the result becomes the new input.\n * @param index - If defined and the current value is an array, the value\n * at `index` become the new input.\n * @param info - object to return information about resolution in\n * @param info.cacheable - Will be set to `false` if option is not cacheable.\n * @since 2.7.0\n */\nexport function resolve(inputs: Array<unknown>, context?: object, index?: number, info?: { cacheable: boolean }) {\n  let cacheable = true;\n  let i: number, ilen: number, value: unknown;\n\n  for (i = 0, ilen = inputs.length; i < ilen; ++i) {\n    value = inputs[i];\n    if (value === undefined) {\n      continue;\n    }\n    if (context !== undefined && typeof value === 'function') {\n      value = value(context);\n      cacheable = false;\n    }\n    if (index !== undefined && isArray(value)) {\n      value = value[index % value.length];\n      cacheable = false;\n    }\n    if (value !== undefined) {\n      if (info && !cacheable) {\n        info.cacheable = false;\n      }\n      return value;\n    }\n  }\n}\n\n/**\n * @param minmax\n * @param grace\n * @param beginAtZero\n * @private\n */\nexport function _addGrace(minmax: { min: number; max: number; }, grace: number | string, beginAtZero: boolean) {\n  const {min, max} = minmax;\n  const change = toDimension(grace, (max - min) / 2);\n  const keepZero = (value: number, add: number) => beginAtZero && value === 0 ? 0 : value + add;\n  return {\n    min: keepZero(min, -Math.abs(change)),\n    max: keepZero(max, change)\n  };\n}\n\n/**\n * Create a context inheriting parentContext\n * @param parentContext\n * @param context\n * @returns\n */\nexport function createContext<T extends object>(parentContext: null, context: T): T;\nexport function createContext<T extends object, P extends T>(parentContext: P, context: T): P & T;\nexport function createContext(parentContext: object, context: object) {\n  return Object.assign(Object.create(parentContext), context);\n}\n", "/* eslint-disable @typescript-eslint/no-use-before-define */\nimport type {AnyObject} from '../types/basic.js';\nimport type {ChartMeta} from '../types/index.js';\nimport type {\n  ResolverObjectKey,\n  ResolverCache,\n  ResolverProxy,\n  DescriptorDefaults,\n  Descriptor,\n  ContextCache,\n  ContextProxy\n} from './helpers.config.types.js';\nimport {isArray, isFunction, isObject, resolveObjectKey, _capitalize} from './helpers.core.js';\n\nexport * from './helpers.config.types.js';\n\n/**\n * Creates a Proxy for resolving raw values for options.\n * @param scopes - The option scopes to look for values, in resolution order\n * @param prefixes - The prefixes for values, in resolution order.\n * @param rootScopes - The root option scopes\n * @param fallback - Parent scopes fallback\n * @param getTarget - callback for getting the target for changed values\n * @returns Proxy\n * @private\n */\nexport function _createResolver<\n  T extends AnyObject[] = AnyObject[],\n  R extends AnyObject[] = T\n>(\n  scopes: T,\n  prefixes = [''],\n  rootScopes?: R,\n  fallback?: ResolverObjectKey,\n  getTarget = () => scopes[0]\n) {\n  const finalRootScopes = rootScopes || scopes;\n  if (typeof fallback === 'undefined') {\n    fallback = _resolve('_fallback', scopes);\n  }\n  const cache: ResolverCache<T, R> = {\n    [Symbol.toStringTag]: 'Object',\n    _cacheable: true,\n    _scopes: scopes,\n    _rootScopes: finalRootScopes,\n    _fallback: fallback,\n    _getTarget: getTarget,\n    override: (scope: AnyObject) => _createResolver([scope, ...scopes], prefixes, finalRootScopes, fallback),\n  };\n  return new Proxy(cache, {\n    /**\n     * A trap for the delete operator.\n     */\n    deleteProperty(target, prop: string) {\n      delete target[prop]; // remove from cache\n      delete target._keys; // remove cached keys\n      delete scopes[0][prop]; // remove from top level scope\n      return true;\n    },\n\n    /**\n     * A trap for getting property values.\n     */\n    get(target, prop: string) {\n      return _cached(target, prop,\n        () => _resolveWithPrefixes(prop, prefixes, scopes, target));\n    },\n\n    /**\n     * A trap for Object.getOwnPropertyDescriptor.\n     * Also used by Object.hasOwnProperty.\n     */\n    getOwnPropertyDescriptor(target, prop) {\n      return Reflect.getOwnPropertyDescriptor(target._scopes[0], prop);\n    },\n\n    /**\n     * A trap for Object.getPrototypeOf.\n     */\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(scopes[0]);\n    },\n\n    /**\n     * A trap for the in operator.\n     */\n    has(target, prop: string) {\n      return getKeysFromAllScopes(target).includes(prop);\n    },\n\n    /**\n     * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n     */\n    ownKeys(target) {\n      return getKeysFromAllScopes(target);\n    },\n\n    /**\n     * A trap for setting property values.\n     */\n    set(target, prop: string, value) {\n      const storage = target._storage || (target._storage = getTarget());\n      target[prop] = storage[prop] = value; // set to top level scope + cache\n      delete target._keys; // remove cached keys\n      return true;\n    }\n  }) as ResolverProxy<T, R>;\n}\n\n/**\n * Returns an Proxy for resolving option values with context.\n * @param proxy - The Proxy returned by `_createResolver`\n * @param context - Context object for scriptable/indexable options\n * @param subProxy - The proxy provided for scriptable options\n * @param descriptorDefaults - Defaults for descriptors\n * @private\n */\nexport function _attachContext<\n  T extends AnyObject[] = AnyObject[],\n  R extends AnyObject[] = T\n>(\n  proxy: ResolverProxy<T, R>,\n  context: AnyObject,\n  subProxy?: ResolverProxy<T, R>,\n  descriptorDefaults?: DescriptorDefaults\n) {\n  const cache: ContextCache<T, R> = {\n    _cacheable: false,\n    _proxy: proxy,\n    _context: context,\n    _subProxy: subProxy,\n    _stack: new Set(),\n    _descriptors: _descriptors(proxy, descriptorDefaults),\n    setContext: (ctx: AnyObject) => _attachContext(proxy, ctx, subProxy, descriptorDefaults),\n    override: (scope: AnyObject) => _attachContext(proxy.override(scope), context, subProxy, descriptorDefaults)\n  };\n  return new Proxy(cache, {\n    /**\n     * A trap for the delete operator.\n     */\n    deleteProperty(target, prop) {\n      delete target[prop]; // remove from cache\n      delete proxy[prop]; // remove from proxy\n      return true;\n    },\n\n    /**\n     * A trap for getting property values.\n     */\n    get(target, prop: string, receiver) {\n      return _cached(target, prop,\n        () => _resolveWithContext(target, prop, receiver));\n    },\n\n    /**\n     * A trap for Object.getOwnPropertyDescriptor.\n     * Also used by Object.hasOwnProperty.\n     */\n    getOwnPropertyDescriptor(target, prop) {\n      return target._descriptors.allKeys\n        ? Reflect.has(proxy, prop) ? {enumerable: true, configurable: true} : undefined\n        : Reflect.getOwnPropertyDescriptor(proxy, prop);\n    },\n\n    /**\n     * A trap for Object.getPrototypeOf.\n     */\n    getPrototypeOf() {\n      return Reflect.getPrototypeOf(proxy);\n    },\n\n    /**\n     * A trap for the in operator.\n     */\n    has(target, prop) {\n      return Reflect.has(proxy, prop);\n    },\n\n    /**\n     * A trap for Object.getOwnPropertyNames and Object.getOwnPropertySymbols.\n     */\n    ownKeys() {\n      return Reflect.ownKeys(proxy);\n    },\n\n    /**\n     * A trap for setting property values.\n     */\n    set(target, prop, value) {\n      proxy[prop] = value; // set to proxy\n      delete target[prop]; // remove from cache\n      return true;\n    }\n  }) as ContextProxy<T, R>;\n}\n\n/**\n * @private\n */\nexport function _descriptors(\n  proxy: ResolverCache,\n  defaults: DescriptorDefaults = {scriptable: true, indexable: true}\n): Descriptor {\n  const {_scriptable = defaults.scriptable, _indexable = defaults.indexable, _allKeys = defaults.allKeys} = proxy;\n  return {\n    allKeys: _allKeys,\n    scriptable: _scriptable,\n    indexable: _indexable,\n    isScriptable: isFunction(_scriptable) ? _scriptable : () => _scriptable,\n    isIndexable: isFunction(_indexable) ? _indexable : () => _indexable\n  };\n}\n\nconst readKey = (prefix: string, name: string) => prefix ? prefix + _capitalize(name) : name;\nconst needsSubResolver = (prop: string, value: unknown) => isObject(value) && prop !== 'adapters' &&\n  (Object.getPrototypeOf(value) === null || value.constructor === Object);\n\nfunction _cached(\n  target: AnyObject,\n  prop: string,\n  resolve: () => unknown\n) {\n  if (Object.prototype.hasOwnProperty.call(target, prop) || prop === 'constructor') {\n    return target[prop];\n  }\n\n  const value = resolve();\n  // cache the resolved value\n  target[prop] = value;\n  return value;\n}\n\nfunction _resolveWithContext(\n  target: ContextCache,\n  prop: string,\n  receiver: AnyObject\n) {\n  const {_proxy, _context, _subProxy, _descriptors: descriptors} = target;\n  let value = _proxy[prop]; // resolve from proxy\n\n  // resolve with context\n  if (isFunction(value) && descriptors.isScriptable(prop)) {\n    value = _resolveScriptable(prop, value, target, receiver);\n  }\n  if (isArray(value) && value.length) {\n    value = _resolveArray(prop, value, target, descriptors.isIndexable);\n  }\n  if (needsSubResolver(prop, value)) {\n    // if the resolved value is an object, create a sub resolver for it\n    value = _attachContext(value, _context, _subProxy && _subProxy[prop], descriptors);\n  }\n  return value;\n}\n\nfunction _resolveScriptable(\n  prop: string,\n  getValue: (ctx: AnyObject, sub: AnyObject) => unknown,\n  target: ContextCache,\n  receiver: AnyObject\n) {\n  const {_proxy, _context, _subProxy, _stack} = target;\n  if (_stack.has(prop)) {\n    throw new Error('Recursion detected: ' + Array.from(_stack).join('->') + '->' + prop);\n  }\n  _stack.add(prop);\n  let value = getValue(_context, _subProxy || receiver);\n  _stack.delete(prop);\n  if (needsSubResolver(prop, value)) {\n    // When scriptable option returns an object, create a resolver on that.\n    value = createSubResolver(_proxy._scopes, _proxy, prop, value);\n  }\n  return value;\n}\n\nfunction _resolveArray(\n  prop: string,\n  value: unknown[],\n  target: ContextCache,\n  isIndexable: (key: string) => boolean\n) {\n  const {_proxy, _context, _subProxy, _descriptors: descriptors} = target;\n\n  if (typeof _context.index !== 'undefined' && isIndexable(prop)) {\n    return value[_context.index % value.length];\n  } else if (isObject(value[0])) {\n    // Array of objects, return array or resolvers\n    const arr = value;\n    const scopes = _proxy._scopes.filter(s => s !== arr);\n    value = [];\n    for (const item of arr) {\n      const resolver = createSubResolver(scopes, _proxy, prop, item);\n      value.push(_attachContext(resolver, _context, _subProxy && _subProxy[prop], descriptors));\n    }\n  }\n  return value;\n}\n\nfunction resolveFallback(\n  fallback: ResolverObjectKey | ((prop: ResolverObjectKey, value: unknown) => ResolverObjectKey),\n  prop: ResolverObjectKey,\n  value: unknown\n) {\n  return isFunction(fallback) ? fallback(prop, value) : fallback;\n}\n\nconst getScope = (key: ResolverObjectKey, parent: AnyObject) => key === true ? parent\n  : typeof key === 'string' ? resolveObjectKey(parent, key) : undefined;\n\nfunction addScopes(\n  set: Set<AnyObject>,\n  parentScopes: AnyObject[],\n  key: ResolverObjectKey,\n  parentFallback: ResolverObjectKey,\n  value: unknown\n) {\n  for (const parent of parentScopes) {\n    const scope = getScope(key, parent);\n    if (scope) {\n      set.add(scope);\n      const fallback = resolveFallback(scope._fallback, key, value);\n      if (typeof fallback !== 'undefined' && fallback !== key && fallback !== parentFallback) {\n        // When we reach the descriptor that defines a new _fallback, return that.\n        // The fallback will resume to that new scope.\n        return fallback;\n      }\n    } else if (scope === false && typeof parentFallback !== 'undefined' && key !== parentFallback) {\n      // Fallback to `false` results to `false`, when falling back to different key.\n      // For example `interaction` from `hover` or `plugins.tooltip` and `animation` from `animations`\n      return null;\n    }\n  }\n  return false;\n}\n\nfunction createSubResolver(\n  parentScopes: AnyObject[],\n  resolver: ResolverCache,\n  prop: ResolverObjectKey,\n  value: unknown\n) {\n  const rootScopes = resolver._rootScopes;\n  const fallback = resolveFallback(resolver._fallback, prop, value);\n  const allScopes = [...parentScopes, ...rootScopes];\n  const set = new Set<AnyObject>();\n  set.add(value);\n  let key = addScopesFromKey(set, allScopes, prop, fallback || prop, value);\n  if (key === null) {\n    return false;\n  }\n  if (typeof fallback !== 'undefined' && fallback !== prop) {\n    key = addScopesFromKey(set, allScopes, fallback, key, value);\n    if (key === null) {\n      return false;\n    }\n  }\n  return _createResolver(Array.from(set), [''], rootScopes, fallback,\n    () => subGetTarget(resolver, prop as string, value));\n}\n\nfunction addScopesFromKey(\n  set: Set<AnyObject>,\n  allScopes: AnyObject[],\n  key: ResolverObjectKey,\n  fallback: ResolverObjectKey,\n  item: unknown\n) {\n  while (key) {\n    key = addScopes(set, allScopes, key, fallback, item);\n  }\n  return key;\n}\n\nfunction subGetTarget(\n  resolver: ResolverCache,\n  prop: string,\n  value: unknown\n) {\n  const parent = resolver._getTarget();\n  if (!(prop in parent)) {\n    parent[prop] = {};\n  }\n  const target = parent[prop];\n  if (isArray(target) && isObject(value)) {\n    // For array of objects, the object is used to store updated values\n    return value;\n  }\n  return target || {};\n}\n\nfunction _resolveWithPrefixes(\n  prop: string,\n  prefixes: string[],\n  scopes: AnyObject[],\n  proxy: ResolverProxy\n) {\n  let value: unknown;\n  for (const prefix of prefixes) {\n    value = _resolve(readKey(prefix, prop), scopes);\n    if (typeof value !== 'undefined') {\n      return needsSubResolver(prop, value)\n        ? createSubResolver(scopes, proxy, prop, value)\n        : value;\n    }\n  }\n}\n\nfunction _resolve(key: string, scopes: AnyObject[]) {\n  for (const scope of scopes) {\n    if (!scope) {\n      continue;\n    }\n    const value = scope[key];\n    if (typeof value !== 'undefined') {\n      return value;\n    }\n  }\n}\n\nfunction getKeysFromAllScopes(target: ResolverCache) {\n  let keys = target._keys;\n  if (!keys) {\n    keys = target._keys = resolveKeysFromAllScopes(target._scopes);\n  }\n  return keys;\n}\n\nfunction resolveKeysFromAllScopes(scopes: AnyObject[]) {\n  const set = new Set<string>();\n  for (const scope of scopes) {\n    for (const key of Object.keys(scope).filter(k => !k.startsWith('_'))) {\n      set.add(key);\n    }\n  }\n  return Array.from(set);\n}\n\nexport function _parseObjectDataRadialScale(\n  meta: ChartMeta<'line' | 'scatter'>,\n  data: AnyObject[],\n  start: number,\n  count: number\n) {\n  const {iScale} = meta;\n  const {key = 'r'} = this._parsing;\n  const parsed = new Array<{r: unknown}>(count);\n  let i: number, ilen: number, index: number, item: AnyObject;\n\n  for (i = 0, ilen = count; i < ilen; ++i) {\n    index = i + start;\n    item = data[index];\n    parsed[i] = {\n      r: iScale.parse(resolveObjectKey(item, key), index)\n    };\n  }\n  return parsed;\n}\n", "import {almostEquals, distanceBetweenPoints, sign} from './helpers.math.js';\nimport {_isPointInArea} from './helpers.canvas.js';\nimport type {ChartArea} from '../types/index.js';\nimport type {SplinePoint} from '../types/geometric.js';\n\nconst EPSILON = Number.EPSILON || 1e-14;\n\ntype OptionalSplinePoint = SplinePoint | false\nconst getPoint = (points: SplinePoint[], i: number): OptionalSplinePoint => i < points.length && !points[i].skip && points[i];\nconst getValueAxis = (indexAxis: 'x' | 'y') => indexAxis === 'x' ? 'y' : 'x';\n\nexport function splineCurve(\n  firstPoint: SplinePoint,\n  middlePoint: SplinePoint,\n  afterPoint: SplinePoint,\n  t: number\n): {\n    previous: SplinePoint\n    next: SplinePoint\n  } {\n  // Props to <PERSON> at scaled innovation for his post on splining between points\n  // http://scaledinnovation.com/analytics/splines/aboutSplines.html\n\n  // This function must also respect \"skipped\" points\n\n  const previous = firstPoint.skip ? middlePoint : firstPoint;\n  const current = middlePoint;\n  const next = afterPoint.skip ? middlePoint : afterPoint;\n  const d01 = distanceBetweenPoints(current, previous);\n  const d12 = distanceBetweenPoints(next, current);\n\n  let s01 = d01 / (d01 + d12);\n  let s12 = d12 / (d01 + d12);\n\n  // If all points are the same, s01 & s02 will be inf\n  s01 = isNaN(s01) ? 0 : s01;\n  s12 = isNaN(s12) ? 0 : s12;\n\n  const fa = t * s01; // scaling factor for triangle Ta\n  const fb = t * s12;\n\n  return {\n    previous: {\n      x: current.x - fa * (next.x - previous.x),\n      y: current.y - fa * (next.y - previous.y)\n    },\n    next: {\n      x: current.x + fb * (next.x - previous.x),\n      y: current.y + fb * (next.y - previous.y)\n    }\n  };\n}\n\n/**\n * Adjust tangents to ensure monotonic properties\n */\nfunction monotoneAdjust(points: SplinePoint[], deltaK: number[], mK: number[]) {\n  const pointsLen = points.length;\n\n  let alphaK: number, betaK: number, tauK: number, squaredMagnitude: number, pointCurrent: OptionalSplinePoint;\n  let pointAfter = getPoint(points, 0);\n  for (let i = 0; i < pointsLen - 1; ++i) {\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent || !pointAfter) {\n      continue;\n    }\n\n    if (almostEquals(deltaK[i], 0, EPSILON)) {\n      mK[i] = mK[i + 1] = 0;\n      continue;\n    }\n\n    alphaK = mK[i] / deltaK[i];\n    betaK = mK[i + 1] / deltaK[i];\n    squaredMagnitude = Math.pow(alphaK, 2) + Math.pow(betaK, 2);\n    if (squaredMagnitude <= 9) {\n      continue;\n    }\n\n    tauK = 3 / Math.sqrt(squaredMagnitude);\n    mK[i] = alphaK * tauK * deltaK[i];\n    mK[i + 1] = betaK * tauK * deltaK[i];\n  }\n}\n\nfunction monotoneCompute(points: SplinePoint[], mK: number[], indexAxis: 'x' | 'y' = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  let delta: number, pointBefore: OptionalSplinePoint, pointCurrent: OptionalSplinePoint;\n  let pointAfter = getPoint(points, 0);\n\n  for (let i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n\n    const iPixel = pointCurrent[indexAxis];\n    const vPixel = pointCurrent[valueAxis];\n    if (pointBefore) {\n      delta = (iPixel - pointBefore[indexAxis]) / 3;\n      pointCurrent[`cp1${indexAxis}`] = iPixel - delta;\n      pointCurrent[`cp1${valueAxis}`] = vPixel - delta * mK[i];\n    }\n    if (pointAfter) {\n      delta = (pointAfter[indexAxis] - iPixel) / 3;\n      pointCurrent[`cp2${indexAxis}`] = iPixel + delta;\n      pointCurrent[`cp2${valueAxis}`] = vPixel + delta * mK[i];\n    }\n  }\n}\n\n/**\n * This function calculates Bézier control points in a similar way than |splineCurve|,\n * but preserves monotonicity of the provided data and ensures no local extremums are added\n * between the dataset discrete points due to the interpolation.\n * See : https://en.wikipedia.org/wiki/Monotone_cubic_interpolation\n */\nexport function splineCurveMonotone(points: SplinePoint[], indexAxis: 'x' | 'y' = 'x') {\n  const valueAxis = getValueAxis(indexAxis);\n  const pointsLen = points.length;\n  const deltaK: number[] = Array(pointsLen).fill(0);\n  const mK: number[] = Array(pointsLen);\n\n  // Calculate slopes (deltaK) and initialize tangents (mK)\n  let i, pointBefore: OptionalSplinePoint, pointCurrent: OptionalSplinePoint;\n  let pointAfter = getPoint(points, 0);\n\n  for (i = 0; i < pointsLen; ++i) {\n    pointBefore = pointCurrent;\n    pointCurrent = pointAfter;\n    pointAfter = getPoint(points, i + 1);\n    if (!pointCurrent) {\n      continue;\n    }\n\n    if (pointAfter) {\n      const slopeDelta = pointAfter[indexAxis] - pointCurrent[indexAxis];\n\n      // In the case of two points that appear at the same x pixel, slopeDeltaX is 0\n      deltaK[i] = slopeDelta !== 0 ? (pointAfter[valueAxis] - pointCurrent[valueAxis]) / slopeDelta : 0;\n    }\n    mK[i] = !pointBefore ? deltaK[i]\n      : !pointAfter ? deltaK[i - 1]\n        : (sign(deltaK[i - 1]) !== sign(deltaK[i])) ? 0\n          : (deltaK[i - 1] + deltaK[i]) / 2;\n  }\n\n  monotoneAdjust(points, deltaK, mK);\n\n  monotoneCompute(points, mK, indexAxis);\n}\n\nfunction capControlPoint(pt: number, min: number, max: number) {\n  return Math.max(Math.min(pt, max), min);\n}\n\nfunction capBezierPoints(points: SplinePoint[], area: ChartArea) {\n  let i, ilen, point, inArea, inAreaPrev;\n  let inAreaNext = _isPointInArea(points[0], area);\n  for (i = 0, ilen = points.length; i < ilen; ++i) {\n    inAreaPrev = inArea;\n    inArea = inAreaNext;\n    inAreaNext = i < ilen - 1 && _isPointInArea(points[i + 1], area);\n    if (!inArea) {\n      continue;\n    }\n    point = points[i];\n    if (inAreaPrev) {\n      point.cp1x = capControlPoint(point.cp1x, area.left, area.right);\n      point.cp1y = capControlPoint(point.cp1y, area.top, area.bottom);\n    }\n    if (inAreaNext) {\n      point.cp2x = capControlPoint(point.cp2x, area.left, area.right);\n      point.cp2y = capControlPoint(point.cp2y, area.top, area.bottom);\n    }\n  }\n}\n\n/**\n * @private\n */\nexport function _updateBezierControlPoints(\n  points: SplinePoint[],\n  options,\n  area: ChartArea,\n  loop: boolean,\n  indexAxis: 'x' | 'y'\n) {\n  let i: number, ilen: number, point: SplinePoint, controlPoints: ReturnType<typeof splineCurve>;\n\n  // Only consider points that are drawn in case the spanGaps option is used\n  if (options.spanGaps) {\n    points = points.filter((pt) => !pt.skip);\n  }\n\n  if (options.cubicInterpolationMode === 'monotone') {\n    splineCurveMonotone(points, indexAxis);\n  } else {\n    let prev = loop ? points[points.length - 1] : points[0];\n    for (i = 0, ilen = points.length; i < ilen; ++i) {\n      point = points[i];\n      controlPoints = splineCurve(\n        prev,\n        point,\n        points[Math.min(i + 1, ilen - (loop ? 0 : 1)) % ilen],\n        options.tension\n      );\n      point.cp1x = controlPoints.previous.x;\n      point.cp1y = controlPoints.previous.y;\n      point.cp2x = controlPoints.next.x;\n      point.cp2y = controlPoints.next.y;\n      prev = point;\n    }\n  }\n\n  if (options.capBezierPoints) {\n    capBezierPoints(points, area);\n  }\n}\n", "import type {ChartArea, Scale} from '../types/index.js';\nimport type PrivateChart from '../core/core.controller.js';\nimport type {Chart, ChartEvent} from '../types.js';\nimport {INFINITY} from './helpers.math.js';\n\n/**\n * @private\n */\nexport function _isDomSupported(): boolean {\n  return typeof window !== 'undefined' && typeof document !== 'undefined';\n}\n\n/**\n * @private\n */\nexport function _getParentNode(domNode: HTMLCanvasElement): HTMLCanvasElement {\n  let parent = domNode.parentNode;\n  if (parent && parent.toString() === '[object ShadowRoot]') {\n    parent = (parent as ShadowRoot).host;\n  }\n  return parent as HTMLCanvasElement;\n}\n\n/**\n * convert max-width/max-height values that may be percentages into a number\n * @private\n */\n\nfunction parseMaxStyle(styleValue: string | number, node: HTMLElement, parentProperty: string) {\n  let valueInPixels: number;\n  if (typeof styleValue === 'string') {\n    valueInPixels = parseInt(styleValue, 10);\n\n    if (styleValue.indexOf('%') !== -1) {\n      // percentage * size in dimension\n      valueInPixels = (valueInPixels / 100) * node.parentNode[parentProperty];\n    }\n  } else {\n    valueInPixels = styleValue;\n  }\n\n  return valueInPixels;\n}\n\nconst getComputedStyle = (element: HTMLElement): CSSStyleDeclaration =>\n  element.ownerDocument.defaultView.getComputedStyle(element, null);\n\nexport function getStyle(el: HTMLElement, property: string): string {\n  return getComputedStyle(el).getPropertyValue(property);\n}\n\nconst positions = ['top', 'right', 'bottom', 'left'];\nfunction getPositionedStyle(styles: CSSStyleDeclaration, style: string, suffix?: string): ChartArea {\n  const result = {} as ChartArea;\n  suffix = suffix ? '-' + suffix : '';\n  for (let i = 0; i < 4; i++) {\n    const pos = positions[i];\n    result[pos] = parseFloat(styles[style + '-' + pos + suffix]) || 0;\n  }\n  result.width = result.left + result.right;\n  result.height = result.top + result.bottom;\n  return result;\n}\n\nconst useOffsetPos = (x: number, y: number, target: HTMLElement | EventTarget) =>\n  (x > 0 || y > 0) && (!target || !(target as HTMLElement).shadowRoot);\n\n/**\n * @param e\n * @param canvas\n * @returns Canvas position\n */\nfunction getCanvasPosition(\n  e: Event | TouchEvent | MouseEvent,\n  canvas: HTMLCanvasElement\n): {\n    x: number;\n    y: number;\n    box: boolean;\n  } {\n  const touches = (e as TouchEvent).touches;\n  const source = (touches && touches.length ? touches[0] : e) as MouseEvent;\n  const {offsetX, offsetY} = source as MouseEvent;\n  let box = false;\n  let x, y;\n  if (useOffsetPos(offsetX, offsetY, e.target)) {\n    x = offsetX;\n    y = offsetY;\n  } else {\n    const rect = canvas.getBoundingClientRect();\n    x = source.clientX - rect.left;\n    y = source.clientY - rect.top;\n    box = true;\n  }\n  return {x, y, box};\n}\n\n/**\n * Gets an event's x, y coordinates, relative to the chart area\n * @param event\n * @param chart\n * @returns x and y coordinates of the event\n */\n\nexport function getRelativePosition(\n  event: Event | ChartEvent | TouchEvent | MouseEvent,\n  chart: Chart | PrivateChart\n): { x: number; y: number } {\n  if ('native' in event) {\n    return event;\n  }\n\n  const {canvas, currentDevicePixelRatio} = chart;\n  const style = getComputedStyle(canvas);\n  const borderBox = style.boxSizing === 'border-box';\n  const paddings = getPositionedStyle(style, 'padding');\n  const borders = getPositionedStyle(style, 'border', 'width');\n  const {x, y, box} = getCanvasPosition(event, canvas);\n  const xOffset = paddings.left + (box && borders.left);\n  const yOffset = paddings.top + (box && borders.top);\n\n  let {width, height} = chart;\n  if (borderBox) {\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  return {\n    x: Math.round((x - xOffset) / width * canvas.width / currentDevicePixelRatio),\n    y: Math.round((y - yOffset) / height * canvas.height / currentDevicePixelRatio)\n  };\n}\n\nfunction getContainerSize(canvas: HTMLCanvasElement, width: number, height: number): Partial<Scale> {\n  let maxWidth: number, maxHeight: number;\n\n  if (width === undefined || height === undefined) {\n    const container = canvas && _getParentNode(canvas);\n    if (!container) {\n      width = canvas.clientWidth;\n      height = canvas.clientHeight;\n    } else {\n      const rect = container.getBoundingClientRect(); // this is the border box of the container\n      const containerStyle = getComputedStyle(container);\n      const containerBorder = getPositionedStyle(containerStyle, 'border', 'width');\n      const containerPadding = getPositionedStyle(containerStyle, 'padding');\n      width = rect.width - containerPadding.width - containerBorder.width;\n      height = rect.height - containerPadding.height - containerBorder.height;\n      maxWidth = parseMaxStyle(containerStyle.maxWidth, container, 'clientWidth');\n      maxHeight = parseMaxStyle(containerStyle.maxHeight, container, 'clientHeight');\n    }\n  }\n  return {\n    width,\n    height,\n    maxWidth: maxWidth || INFINITY,\n    maxHeight: maxHeight || INFINITY\n  };\n}\n\nconst round1 = (v: number) => Math.round(v * 10) / 10;\n\n// eslint-disable-next-line complexity\nexport function getMaximumSize(\n  canvas: HTMLCanvasElement,\n  bbWidth?: number,\n  bbHeight?: number,\n  aspectRatio?: number\n): { width: number; height: number } {\n  const style = getComputedStyle(canvas);\n  const margins = getPositionedStyle(style, 'margin');\n  const maxWidth = parseMaxStyle(style.maxWidth, canvas, 'clientWidth') || INFINITY;\n  const maxHeight = parseMaxStyle(style.maxHeight, canvas, 'clientHeight') || INFINITY;\n  const containerSize = getContainerSize(canvas, bbWidth, bbHeight);\n  let {width, height} = containerSize;\n\n  if (style.boxSizing === 'content-box') {\n    const borders = getPositionedStyle(style, 'border', 'width');\n    const paddings = getPositionedStyle(style, 'padding');\n    width -= paddings.width + borders.width;\n    height -= paddings.height + borders.height;\n  }\n  width = Math.max(0, width - margins.width);\n  height = Math.max(0, aspectRatio ? width / aspectRatio : height - margins.height);\n  width = round1(Math.min(width, maxWidth, containerSize.maxWidth));\n  height = round1(Math.min(height, maxHeight, containerSize.maxHeight));\n  if (width && !height) {\n    // https://github.com/chartjs/Chart.js/issues/4659\n    // If the canvas has width, but no height, default to aspectRatio of 2 (canvas default)\n    height = round1(width / 2);\n  }\n\n  const maintainHeight = bbWidth !== undefined || bbHeight !== undefined;\n\n  if (maintainHeight && aspectRatio && containerSize.height && height > containerSize.height) {\n    height = containerSize.height;\n    width = round1(Math.floor(height * aspectRatio));\n  }\n\n  return {width, height};\n}\n\n/**\n * @param chart\n * @param forceRatio\n * @param forceStyle\n * @returns True if the canvas context size or transformation has changed.\n */\nexport function retinaScale(\n  chart: Chart | PrivateChart,\n  forceRatio: number,\n  forceStyle?: boolean\n): boolean | void {\n  const pixelRatio = forceRatio || 1;\n  const deviceHeight = Math.floor(chart.height * pixelRatio);\n  const deviceWidth = Math.floor(chart.width * pixelRatio);\n\n  (chart as PrivateChart).height = Math.floor(chart.height);\n  (chart as PrivateChart).width = Math.floor(chart.width);\n\n  const canvas = chart.canvas;\n\n  // If no style has been set on the canvas, the render size is used as display size,\n  // making the chart visually bigger, so let's enforce it to the \"correct\" values.\n  // See https://github.com/chartjs/Chart.js/issues/3575\n  if (canvas.style && (forceStyle || (!canvas.style.height && !canvas.style.width))) {\n    canvas.style.height = `${chart.height}px`;\n    canvas.style.width = `${chart.width}px`;\n  }\n\n  if (chart.currentDevicePixelRatio !== pixelRatio\n      || canvas.height !== deviceHeight\n      || canvas.width !== deviceWidth) {\n    (chart as PrivateChart).currentDevicePixelRatio = pixelRatio;\n    canvas.height = deviceHeight;\n    canvas.width = deviceWidth;\n    chart.ctx.setTransform(pixelRatio, 0, 0, pixelRatio, 0, 0);\n    return true;\n  }\n  return false;\n}\n\n/**\n * Detects support for options object argument in addEventListener.\n * https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Safely_detecting_option_support\n * @private\n */\nexport const supportsEventListenerOptions = (function() {\n  let passiveSupported = false;\n  try {\n    const options = {\n      get passive() { // This function will be called when the browser attempts to access the passive property.\n        passiveSupported = true;\n        return false;\n      }\n    } as EventListenerOptions;\n\n    if (_isDomSupported()) {\n      window.addEventListener('test', null, options);\n      window.removeEventListener('test', null, options);\n    }\n  } catch (e) {\n    // continue regardless of error\n  }\n  return passiveSupported;\n}());\n\n/**\n * The \"used\" size is the final value of a dimension property after all calculations have\n * been performed. This method uses the computed style of `element` but returns undefined\n * if the computed style is not expressed in pixels. That can happen in some cases where\n * `element` has a size relative to its parent and this last one is not yet displayed,\n * for example because of `display: none` on a parent node.\n * @see https://developer.mozilla.org/en-US/docs/Web/CSS/used_value\n * @returns Size in pixels or undefined if unknown.\n */\n\nexport function readUsedSize(\n  element: HTMLElement,\n  property: 'width' | 'height'\n): number | undefined {\n  const value = getStyle(element, property);\n  const matches = value && value.match(/^(\\d+)(\\.\\d+)?px$/);\n  return matches ? +matches[1] : undefined;\n}\n", "import type {Point, SplinePoint} from '../types/geometric.js';\n\n/**\n * @private\n */\nexport function _pointInLine(p1: Point, p2: Point, t: number, mode?) { // eslint-disable-line @typescript-eslint/no-unused-vars\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: p1.y + t * (p2.y - p1.y)\n  };\n}\n\n/**\n * @private\n */\nexport function _steppedInterpolation(\n  p1: Point,\n  p2: Point,\n  t: number, mode: 'middle' | 'after' | unknown\n) {\n  return {\n    x: p1.x + t * (p2.x - p1.x),\n    y: mode === 'middle' ? t < 0.5 ? p1.y : p2.y\n      : mode === 'after' ? t < 1 ? p1.y : p2.y\n        : t > 0 ? p2.y : p1.y\n  };\n}\n\n/**\n * @private\n */\nexport function _bezierInterpolation(p1: SplinePoint, p2: SplinePoint, t: number, mode?) { // eslint-disable-line @typescript-eslint/no-unused-vars\n  const cp1 = {x: p1.cp2x, y: p1.cp2y};\n  const cp2 = {x: p2.cp1x, y: p2.cp1y};\n  const a = _pointInLine(p1, cp1, t);\n  const b = _pointInLine(cp1, cp2, t);\n  const c = _pointInLine(cp2, p2, t);\n  const d = _pointInLine(a, b, t);\n  const e = _pointInLine(b, c, t);\n  return _pointInLine(d, e, t);\n}\n", "export interface RTLAdapter {\n  x(x: number): number;\n  setWidth(w: number): void;\n  textAlign(align: 'center' | 'left' | 'right'): 'center' | 'left' | 'right';\n  xPlus(x: number, value: number): number;\n  leftForLtr(x: number, itemWidth: number): number;\n}\n\nconst getRightToLeftAdapter = function(rectX: number, width: number): RTLAdapter {\n  return {\n    x(x) {\n      return rectX + rectX + width - x;\n    },\n    setWidth(w) {\n      width = w;\n    },\n    textAlign(align) {\n      if (align === 'center') {\n        return align;\n      }\n      return align === 'right' ? 'left' : 'right';\n    },\n    xPlus(x, value) {\n      return x - value;\n    },\n    leftForLtr(x, itemWidth) {\n      return x - itemWidth;\n    },\n  };\n};\n\nconst getLeftToRightAdapter = function(): RTLAdapter {\n  return {\n    x(x) {\n      return x;\n    },\n    setWidth(w) { // eslint-disable-line no-unused-vars\n    },\n    textAlign(align) {\n      return align;\n    },\n    xPlus(x, value) {\n      return x + value;\n    },\n    leftForLtr(x, _itemWidth) { // eslint-disable-line @typescript-eslint/no-unused-vars\n      return x;\n    },\n  };\n};\n\nexport function getRtlAdapter(rtl: boolean, rectX: number, width: number) {\n  return rtl ? getRightToLeftAdapter(rectX, width) : getLeftToRightAdapter();\n}\n\nexport function overrideTextDirection(ctx: CanvasRenderingContext2D, direction: 'ltr' | 'rtl') {\n  let style: CSSStyleDeclaration, original: [string, string];\n  if (direction === 'ltr' || direction === 'rtl') {\n    style = ctx.canvas.style;\n    original = [\n      style.getPropertyValue('direction'),\n      style.getPropertyPriority('direction'),\n    ];\n\n    style.setProperty('direction', direction, 'important');\n    (ctx as { prevTextDirection?: [string, string] }).prevTextDirection = original;\n  }\n}\n\nexport function restoreTextDirection(ctx: CanvasRenderingContext2D, original?: [string, string]) {\n  if (original !== undefined) {\n    delete (ctx as { prevTextDirection?: [string, string] }).prevTextDirection;\n    ctx.canvas.style.setProperty('direction', original[0], original[1]);\n  }\n}\n", "import {_angleBetween, _angleDiff, _isBetween, _normalizeAngle} from './helpers.math.js';\nimport {createContext} from './helpers.options.js';\nimport {isPatternOrGradient} from './helpers.color.js';\n\n/**\n * @typedef { import('../elements/element.line.js').default } LineElement\n * @typedef { import('../elements/element.point.js').default } PointElement\n * @typedef {{start: number, end: number, loop: boolean, style?: any}} Segment\n */\n\nfunction propertyFn(property) {\n  if (property === 'angle') {\n    return {\n      between: _angleBetween,\n      compare: _angleDiff,\n      normalize: _normalizeAngle,\n    };\n  }\n  return {\n    between: _isBetween,\n    compare: (a, b) => a - b,\n    normalize: x => x\n  };\n}\n\nfunction normalizeSegment({start, end, count, loop, style}) {\n  return {\n    start: start % count,\n    end: end % count,\n    loop: loop && (end - start + 1) % count === 0,\n    style\n  };\n}\n\nfunction getSegment(segment, points, bounds) {\n  const {property, start: startBound, end: endBound} = bounds;\n  const {between, normalize} = propertyFn(property);\n  const count = points.length;\n  // eslint-disable-next-line prefer-const\n  let {start, end, loop} = segment;\n  let i, ilen;\n\n  if (loop) {\n    start += count;\n    end += count;\n    for (i = 0, ilen = count; i < ilen; ++i) {\n      if (!between(normalize(points[start % count][property]), startBound, endBound)) {\n        break;\n      }\n      start--;\n      end--;\n    }\n    start %= count;\n    end %= count;\n  }\n\n  if (end < start) {\n    end += count;\n  }\n  return {start, end, loop, style: segment.style};\n}\n\n/**\n * Returns the sub-segment(s) of a line segment that fall in the given bounds\n * @param {object} segment\n * @param {number} segment.start - start index of the segment, referring the points array\n * @param {number} segment.end - end index of the segment, referring the points array\n * @param {boolean} segment.loop - indicates that the segment is a loop\n * @param {object} [segment.style] - segment style\n * @param {PointElement[]} points - the points that this segment refers to\n * @param {object} [bounds]\n * @param {string} bounds.property - the property of a `PointElement` we are bounding. `x`, `y` or `angle`.\n * @param {number} bounds.start - start value of the property\n * @param {number} bounds.end - end value of the property\n * @private\n **/\nexport function _boundSegment(segment, points, bounds) {\n  if (!bounds) {\n    return [segment];\n  }\n\n  const {property, start: startBound, end: endBound} = bounds;\n  const count = points.length;\n  const {compare, between, normalize} = propertyFn(property);\n  const {start, end, loop, style} = getSegment(segment, points, bounds);\n\n  const result = [];\n  let inside = false;\n  let subStart = null;\n  let value, point, prevValue;\n\n  const startIsBefore = () => between(startBound, prevValue, value) && compare(startBound, prevValue) !== 0;\n  const endIsBefore = () => compare(endBound, value) === 0 || between(endBound, prevValue, value);\n  const shouldStart = () => inside || startIsBefore();\n  const shouldStop = () => !inside || endIsBefore();\n\n  for (let i = start, prev = start; i <= end; ++i) {\n    point = points[i % count];\n\n    if (point.skip) {\n      continue;\n    }\n\n    value = normalize(point[property]);\n\n    if (value === prevValue) {\n      continue;\n    }\n\n    inside = between(value, startBound, endBound);\n\n    if (subStart === null && shouldStart()) {\n      subStart = compare(value, startBound) === 0 ? i : prev;\n    }\n\n    if (subStart !== null && shouldStop()) {\n      result.push(normalizeSegment({start: subStart, end: i, loop, count, style}));\n      subStart = null;\n    }\n    prev = i;\n    prevValue = value;\n  }\n\n  if (subStart !== null) {\n    result.push(normalizeSegment({start: subStart, end, loop, count, style}));\n  }\n\n  return result;\n}\n\n\n/**\n * Returns the segments of the line that are inside given bounds\n * @param {LineElement} line\n * @param {object} [bounds]\n * @param {string} bounds.property - the property we are bounding with. `x`, `y` or `angle`.\n * @param {number} bounds.start - start value of the `property`\n * @param {number} bounds.end - end value of the `property`\n * @private\n */\nexport function _boundSegments(line, bounds) {\n  const result = [];\n  const segments = line.segments;\n\n  for (let i = 0; i < segments.length; i++) {\n    const sub = _boundSegment(segments[i], line.points, bounds);\n    if (sub.length) {\n      result.push(...sub);\n    }\n  }\n  return result;\n}\n\n/**\n * Find start and end index of a line.\n */\nfunction findStartAndEnd(points, count, loop, spanGaps) {\n  let start = 0;\n  let end = count - 1;\n\n  if (loop && !spanGaps) {\n    // loop and not spanning gaps, first find a gap to start from\n    while (start < count && !points[start].skip) {\n      start++;\n    }\n  }\n\n  // find first non skipped point (after the first gap possibly)\n  while (start < count && points[start].skip) {\n    start++;\n  }\n\n  // if we looped to count, start needs to be 0\n  start %= count;\n\n  if (loop) {\n    // loop will go past count, if start > 0\n    end += start;\n  }\n\n  while (end > start && points[end % count].skip) {\n    end--;\n  }\n\n  // end could be more than count, normalize\n  end %= count;\n\n  return {start, end};\n}\n\n/**\n * Compute solid segments from Points, when spanGaps === false\n * @param {PointElement[]} points - the points\n * @param {number} start - start index\n * @param {number} max - max index (can go past count on a loop)\n * @param {boolean} loop - boolean indicating that this would be a loop if no gaps are found\n */\nfunction solidSegments(points, start, max, loop) {\n  const count = points.length;\n  const result = [];\n  let last = start;\n  let prev = points[start];\n  let end;\n\n  for (end = start + 1; end <= max; ++end) {\n    const cur = points[end % count];\n    if (cur.skip || cur.stop) {\n      if (!prev.skip) {\n        loop = false;\n        result.push({start: start % count, end: (end - 1) % count, loop});\n        // @ts-ignore\n        start = last = cur.stop ? end : null;\n      }\n    } else {\n      last = end;\n      if (prev.skip) {\n        start = end;\n      }\n    }\n    prev = cur;\n  }\n\n  if (last !== null) {\n    result.push({start: start % count, end: last % count, loop});\n  }\n\n  return result;\n}\n\n/**\n * Compute the continuous segments that define the whole line\n * There can be skipped points within a segment, if spanGaps is true.\n * @param {LineElement} line\n * @param {object} [segmentOptions]\n * @return {Segment[]}\n * @private\n */\nexport function _computeSegments(line, segmentOptions) {\n  const points = line.points;\n  const spanGaps = line.options.spanGaps;\n  const count = points.length;\n\n  if (!count) {\n    return [];\n  }\n\n  const loop = !!line._loop;\n  const {start, end} = findStartAndEnd(points, count, loop, spanGaps);\n\n  if (spanGaps === true) {\n    return splitByStyles(line, [{start, end, loop}], points, segmentOptions);\n  }\n\n  const max = end < start ? end + count : end;\n  const completeLoop = !!line._fullLoop && start === 0 && end === count - 1;\n  return splitByStyles(line, solidSegments(points, start, max, completeLoop), points, segmentOptions);\n}\n\n/**\n * @param {Segment[]} segments\n * @param {PointElement[]} points\n * @param {object} [segmentOptions]\n * @return {Segment[]}\n */\nfunction splitByStyles(line, segments, points, segmentOptions) {\n  if (!segmentOptions || !segmentOptions.setContext || !points) {\n    return segments;\n  }\n  return doSplitByStyles(line, segments, points, segmentOptions);\n}\n\n/**\n * @param {LineElement} line\n * @param {Segment[]} segments\n * @param {PointElement[]} points\n * @param {object} [segmentOptions]\n * @return {Segment[]}\n */\nfunction doSplitByStyles(line, segments, points, segmentOptions) {\n  const chartContext = line._chart.getContext();\n  const baseStyle = readStyle(line.options);\n  const {_datasetIndex: datasetIndex, options: {spanGaps}} = line;\n  const count = points.length;\n  const result = [];\n  let prevStyle = baseStyle;\n  let start = segments[0].start;\n  let i = start;\n\n  function addStyle(s, e, l, st) {\n    const dir = spanGaps ? -1 : 1;\n    if (s === e) {\n      return;\n    }\n    // Style can not start/end on a skipped point, adjust indices accordingly\n    s += count;\n    while (points[s % count].skip) {\n      s -= dir;\n    }\n    while (points[e % count].skip) {\n      e += dir;\n    }\n    if (s % count !== e % count) {\n      result.push({start: s % count, end: e % count, loop: l, style: st});\n      prevStyle = st;\n      start = e % count;\n    }\n  }\n\n  for (const segment of segments) {\n    start = spanGaps ? start : segment.start;\n    let prev = points[start % count];\n    let style;\n    for (i = start + 1; i <= segment.end; i++) {\n      const pt = points[i % count];\n      style = readStyle(segmentOptions.setContext(createContext(chartContext, {\n        type: 'segment',\n        p0: prev,\n        p1: pt,\n        p0DataIndex: (i - 1) % count,\n        p1DataIndex: i % count,\n        datasetIndex\n      })));\n      if (styleChanged(style, prevStyle)) {\n        addStyle(start, i - 1, segment.loop, prevStyle);\n      }\n      prev = pt;\n      prevStyle = style;\n    }\n    if (start < i - 1) {\n      addStyle(start, i - 1, segment.loop, prevStyle);\n    }\n  }\n\n  return result;\n}\n\nfunction readStyle(options) {\n  return {\n    backgroundColor: options.backgroundColor,\n    borderCapStyle: options.borderCapStyle,\n    borderDash: options.borderDash,\n    borderDashOffset: options.borderDashOffset,\n    borderJoinStyle: options.borderJoinStyle,\n    borderWidth: options.borderWidth,\n    borderColor: options.borderColor\n  };\n}\n\nfunction styleChanged(style, prevStyle) {\n  if (!prevStyle) {\n    return false;\n  }\n  const cache = [];\n  const replacer = function(key, value) {\n    if (!isPatternOrGradient(value)) {\n      return value;\n    }\n    if (!cache.includes(value)) {\n      cache.push(value);\n    }\n    return cache.indexOf(value);\n  };\n  return JSON.stringify(style, replacer) !== JSON.stringify(prevStyle, replacer);\n}\n", "import type {Chart, ChartArea, ChartMeta, Scale, TRBL} from '../types/index.js';\n\nfunction getSizeForArea(scale: Scale, chartArea: ChartArea, field: keyof ChartArea) {\n  return scale.options.clip ? scale[field] : chartArea[field];\n}\n\nfunction getDatasetArea(meta: ChartMeta, chartArea: ChartArea): TRBL {\n  const {xScale, yScale} = meta;\n  if (xScale && yScale) {\n    return {\n      left: getSizeForArea(xScale, chartArea, 'left'),\n      right: getSizeForArea(xScale, chartArea, 'right'),\n      top: getSizeForArea(yScale, chartArea, 'top'),\n      bottom: getSizeForArea(yScale, chartArea, 'bottom')\n    };\n  }\n  return chartArea;\n}\n\nexport function getDatasetClipArea(chart: Chart, meta: ChartMeta): TRBL | false {\n  const clip = meta._clip;\n  if (clip.disabled) {\n    return false;\n  }\n  const area = getDatasetArea(meta, chart.chartArea);\n\n  return {\n    left: clip.left === false ? 0 : area.left - (clip.left === true ? 0 : clip.left),\n    right: clip.right === false ? chart.width : area.right + (clip.right === true ? 0 : clip.right),\n    top: clip.top === false ? 0 : area.top - (clip.top === true ? 0 : clip.top),\n    bottom: clip.bottom === false ? chart.height : area.bottom + (clip.bottom === true ? 0 : clip.bottom)\n  };\n}\n"], "mappings": ";;;;;;;;AAAA;;GAAA,C;;;AAUO,SAASA,IAAOA,CAAA;EACrB;AAGF;;AAEC;AACM,MAAMC,GAAM,GAAC,OAAM;EACxB,IAAIC,EAAK;EACT,OAAO,MAAMA,EAAA;AACf;AAEA;;;;AAIC;AACM,SAASC,aAAcA,CAAAC,KAAc,EAA6B;EACvE,OAAOA,KAAA,KAAU,IAAI,IAAIA,KAAU,KAAAC,SAAA;AACrC;AAEA;;;;AAIC;AACM,SAASC,OAAqBA,CAAAF,KAAc,EAAgB;EACjE,IAAIG,KAAA,CAAMD,OAAO,IAAIC,KAAM,CAAAD,OAAO,CAACF,KAAQ;IACzC,OAAO,IAAI;;EAEb,MAAMI,IAAA,GAAOC,MAAO,CAAAC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACR,KAAA;EAC5C,IAAII,IAAA,CAAKK,KAAK,CAAC,CAAG,SAAO,SAAa,IAAAL,IAAA,CAAKK,KAAK,CAAC,CAAC,OAAO,QAAU;IACjE,OAAO,IAAI;;EAEb,OAAO,KAAK;AACd;AAEA;;;;AAIC;AACM,SAASC,QAASA,CAAAV,KAAc,EAAsB;EAC3D,OAAOA,KAAA,KAAU,IAAI,IAAIK,MAAO,CAAAC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACR,KAAW;AACrE;AAEA;;;;AAIA,SAASW,cAAeA,CAAAX,KAAc,EAAmB;EACvD,OAAQ,QAAOA,KAAA,KAAU,YAAYA,KAAiB,YAAAY,MAAK,KAAMC,QAAA,CAAS,CAACb,KAAA;AAC7E;AAKA;;;;AAIC;AACM,SAASc,gBAAgBd,KAAc,EAAEe,YAAoB,EAAE;EACpE,OAAOJ,cAAA,CAAeX,KAAS,IAAAA,KAAA,GAAQe,YAAY;AACrD;AAEA;;;;AAIC;AACM,SAASC,eAAkBhB,KAAoB,EAAEe,YAAe,EAAE;EACvE,OAAO,OAAOf,KAAA,KAAU,WAAc,GAAAe,YAAA,GAAef,KAAK;AAC5D;MAEaiB,YAAe,GAAAA,CAACjB,KAAA,EAAwBkB,SACnD,YAAOlB,KAAA,KAAU,QAAY,IAAAA,KAAA,CAAMmB,QAAQ,CAAC,OAC1CC,UAAW,CAAApB,KAAA,IAAS,MAClB,CAACA,KAAA,GAAQkB,SAAA;MAEFG,WAAc,GAAAA,CAACrB,KAAA,EAAwBkB,SAClD,YAAOlB,KAAA,KAAU,QAAY,IAAAA,KAAA,CAAMmB,QAAQ,CAAC,OAC1CC,UAAW,CAAApB,KAAA,IAAS,MAAMkB,SACxB,IAAClB,KAAA;AAEP;;;;;;;AAOO,SAASsB,QACdA,CAAAC,EAAiB,EACjBC,IAAe,EACfC,OAAY,EACG;EACf,IAAIF,EAAM,WAAOA,EAAG,CAAAf,IAAI,KAAK,UAAY;IACvC,OAAOe,EAAA,CAAGG,KAAK,CAACD,OAAS,EAAAD,IAAA;;AAE7B;AAuBO,SAASG,KACdC,QAAiC,EACjCL,EAAoC,EACpCE,OAAY,EACZI,OAAiB,EACjB;EACA,IAAIC,CAAA,EAAWC,GAAa,EAAAC,IAAA;EAC5B,IAAI9B,OAAA,CAAQ0B,QAAW;IACrBG,GAAA,GAAMH,QAAA,CAASK,MAAM;IACrB,IAAIJ,OAAS;MACX,KAAKC,CAAI,GAAAC,GAAA,GAAM,CAAG,EAAAD,CAAA,IAAK,GAAGA,CAAK;QAC7BP,EAAA,CAAGf,IAAI,CAACiB,OAAA,EAASG,QAAQ,CAACE,CAAA,CAAE,EAAEA,CAAA;MAChC;KACK;MACL,KAAKA,CAAI,MAAGA,CAAI,GAAAC,GAAA,EAAKD,CAAK;QACxBP,EAAA,CAAGf,IAAI,CAACiB,OAAA,EAASG,QAAQ,CAACE,CAAA,CAAE,EAAEA,CAAA;MAChC;;GAEG,UAAIpB,QAAA,CAASkB,QAAW;IAC7BI,IAAO,GAAA3B,MAAA,CAAO2B,IAAI,CAACJ,QAAA;IACnBG,GAAA,GAAMC,IAAA,CAAKC,MAAM;IACjB,KAAKH,CAAI,MAAGA,CAAI,GAAAC,GAAA,EAAKD,CAAK;MACxBP,EAAA,CAAGf,IAAI,CAACiB,OAAS,EAAAG,QAAQ,CAACI,IAAI,CAACF,CAAA,CAAE,CAAC,EAAEE,IAAI,CAACF,CAAE;IAC7C;;AAEJ;AAEA;;;;;AAKC;AACM,SAASI,eAAeC,EAAqB,EAAEC,EAAqB,EAAE;EAC3E,IAAIN,CAAA,EAAWO,IAAA,EAAcC,EAAqB,EAAAC,EAAA;EAElD,IAAI,CAACJ,EAAA,IAAM,CAACC,EAAA,IAAMD,EAAA,CAAGF,MAAM,KAAKG,EAAG,CAAAH,MAAM,EAAE;IACzC,OAAO,KAAK;;EAGd,KAAKH,CAAA,GAAI,GAAGO,IAAO,GAAAF,EAAA,CAAGF,MAAM,EAAEH,CAAA,GAAIO,IAAM,IAAEP,CAAG;IAC3CQ,EAAK,GAAAH,EAAE,CAACL,CAAE;IACVS,EAAK,GAAAH,EAAE,CAACN,CAAE;IAEV,IAAIQ,EAAA,CAAGE,YAAY,KAAKD,EAAG,CAAAC,YAAY,IAAIF,EAAA,CAAGG,KAAK,KAAKF,EAAG,CAAAE,KAAK,EAAE;MAChE,OAAO,KAAK;;EAEhB;EAEA,OAAO,IAAI;AACb;AAEA;;;AAGC;AACM,SAASC,KAASA,CAAAC,MAAS,EAAK;EACrC,IAAIzC,OAAA,CAAQyC,MAAS;IACnB,OAAOA,MAAA,CAAOC,GAAG,CAACF,KAAA;;EAGpB,IAAIhC,QAAA,CAASiC,MAAS;IACpB,MAAME,MAAS,GAAAxC,MAAA,CAAOyC,MAAM,CAAC,IAAI;IACjC,MAAMd,IAAA,GAAO3B,MAAO,CAAA2B,IAAI,CAACW,MAAA;IACzB,MAAMI,IAAA,GAAOf,IAAA,CAAKC,MAAM;IACxB,IAAIe,CAAI;IAER,OAAOA,CAAA,GAAID,IAAM,IAAEC,CAAG;MACpBH,MAAM,CAACb,IAAI,CAACgB,CAAA,CAAE,CAAC,GAAGN,KAAM,CAAAC,MAAM,CAACX,IAAI,CAACgB,CAAA,CAAE,CAAC;IACzC;IAEA,OAAOH,MAAA;;EAGT,OAAOF,MAAA;AACT;AAEA,SAASM,WAAWC,GAAW,EAAE;EAC/B,OAAO,CAAC,aAAa,aAAa,cAAc,CAACC,OAAO,CAACD,GAAA,MAAS,CAAC;AACrE;AAEA;;;;;AAKO,SAASE,QAAQF,GAAW,EAAEL,MAAiB,EAAEF,MAAiB,EAAEU,OAAkB,EAAE;EAC7F,IAAI,CAACJ,UAAA,CAAWC,GAAM;IACpB;;EAGF,MAAMI,IAAA,GAAOT,MAAM,CAACK,GAAI;EACxB,MAAMK,IAAA,GAAOZ,MAAM,CAACO,GAAI;EAExB,IAAIxC,QAAA,CAAS4C,IAAS,KAAA5C,QAAA,CAAS6C,IAAO;;IAEpCC,KAAA,CAAMF,IAAA,EAAMC,IAAM,EAAAF,OAAA;GACb;IACLR,MAAM,CAACK,GAAI,IAAGR,KAAM,CAAAa,IAAA;;AAExB;AA0BO,SAASC,KAASA,CAAAX,MAAS,EAAEF,MAAmB,EAAEU,OAAsB,EAAa;EAC1F,MAAMI,OAAA,GAAUvD,OAAQ,CAAAyC,MAAA,IAAUA,MAAS,IAACA,MAAA,CAAO;EACnD,MAAMN,IAAA,GAAOoB,OAAA,CAAQxB,MAAM;EAE3B,IAAI,CAACvB,QAAA,CAASmC,MAAS;IACrB,OAAOA,MAAA;;EAGTQ,OAAA,GAAUA,OAAA,IAAW,EAAC;EACtB,MAAMK,MAAA,GAASL,OAAQ,CAAAK,MAAM,IAAIN,OAAA;EACjC,IAAIO,OAAA;EAEJ,KAAK,IAAI7B,CAAI,MAAGA,CAAI,GAAAO,IAAA,EAAM,EAAEP,CAAG;IAC7B6B,OAAU,GAAAF,OAAO,CAAC3B,CAAE;IACpB,IAAI,CAACpB,QAAA,CAASiD,OAAU;MACtB;;IAGF,MAAM3B,IAAA,GAAO3B,MAAO,CAAA2B,IAAI,CAAC2B,OAAA;IACzB,KAAK,IAAIX,CAAI,MAAGD,IAAO,GAAAf,IAAA,CAAKC,MAAM,EAAEe,CAAA,GAAID,IAAM,IAAEC,CAAG;MACjDU,MAAA,CAAO1B,IAAI,CAACgB,CAAE,GAAEH,MAAA,EAAQc,OAAS,EAAAN,OAAA;IACnC;EACF;EAEA,OAAOR,MAAA;AACT;AAgBO,SAASe,QAAWf,MAAS,EAAEF,MAAmB,EAAa;;EAEpE,OAAOa,KAAA,CAASX,MAAA,EAAQF,MAAQ;IAACe,MAAQ,EAAAG;EAAS;AACpD;AAEA;;;;AAIO,SAASA,SAAUA,CAAAX,GAAW,EAAEL,MAAiB,EAAEF,MAAiB,EAAE;EAC3E,IAAI,CAACM,UAAA,CAAWC,GAAM;IACpB;;EAGF,MAAMI,IAAA,GAAOT,MAAM,CAACK,GAAI;EACxB,MAAMK,IAAA,GAAOZ,MAAM,CAACO,GAAI;EAExB,IAAIxC,QAAA,CAAS4C,IAAS,KAAA5C,QAAA,CAAS6C,IAAO;IACpCK,OAAA,CAAQN,IAAM,EAAAC,IAAA;GACT,UAAI,CAAClD,MAAA,CAAOC,SAAS,CAACwD,cAAc,CAACtD,IAAI,CAACqC,MAAA,EAAQK,GAAM;IAC7DL,MAAM,CAACK,GAAI,IAAGR,KAAM,CAAAa,IAAA;;AAExB;AAEA;;;AAGO,SAASQ,YAAYC,KAAa,EAAEhE,KAAc,EAAEiE,QAAgB,EAAEN,OAAe,EAAE;EAC5F,IAAI3D,KAAA,KAAUC,SAAW;IACvBiE,OAAA,CAAQC,IAAI,CAACH,KAAA,GAAQ,KAAQ,GAAAC,QAAA,GAC3B,kCAAkCN,OAAU;;AAElD;AAEA;AACA,MAAMS,YAAe;;EAEnB,IAAIC,CAAK,IAAAA,CAAA;;EAETC,CAAG,EAAAC,CAAK,IAAAA,CAAA,CAAED,CAAC;EACXE,CAAG,EAAAD,CAAK,IAAAA,CAAA,CAAEC;AACZ;AAEA;;AAEC;AACM,SAASC,SAAUA,CAAAvB,GAAW,EAAE;EACrC,MAAMwB,KAAA,GAAQxB,GAAI,CAAAyB,KAAK,CAAC;EACxB,MAAM3C,IAAA,GAAiB,EAAE;EACzB,IAAI4C,GAAM;EACV,KAAK,MAAMC,IAAA,IAAQH,KAAO;IACxBE,GAAO,IAAAC,IAAA;IACP,IAAID,GAAA,CAAIzD,QAAQ,CAAC,IAAO;MACtByD,GAAA,GAAMA,GAAI,CAAAnE,KAAK,CAAC,GAAG,CAAC,CAAK;KACpB;MACLuB,IAAA,CAAK8C,IAAI,CAACF,GAAA;MACVA,GAAM;;EAEV;EACA,OAAO5C,IAAA;AACT;AAEA,SAAS+C,gBAAgB7B,GAAW,EAAE;EACpC,MAAMlB,IAAA,GAAOyC,SAAU,CAAAvB,GAAA;EACvB,OAAO8B,GAAO;IACZ,KAAK,MAAMhC,CAAA,IAAKhB,IAAM;MACpB,IAAIgB,CAAA,KAAM,EAAI;QAGZ;;MAEFgC,GAAM,GAAAA,GAAA,IAAOA,GAAG,CAAChC,CAAE;IACrB;IACA,OAAOgC,GAAA;EACT;AACF;AAEO,SAASC,iBAAiBD,GAAc,EAAE9B,GAAW,EAAO;EACjE,MAAMgC,QAAA,GAAWd,YAAY,CAAClB,GAAI,MAAKkB,YAAY,CAAClB,GAAA,CAAI,GAAG6B,eAAA,CAAgB7B,GAAG;EAC9E,OAAOgC,QAAS,CAAAF,GAAA;AAClB;AAEA;;AAEC;AACM,SAASG,WAAYA,CAAAC,GAAW,EAAE;EACvC,OAAOA,GAAA,CAAIC,MAAM,CAAC,GAAGC,WAAW,EAAK,GAAAF,GAAA,CAAI3E,KAAK,CAAC;AACjD;MAGa8E,OAAU,GAACvF,KAAmB,WAAOA,KAAA,KAAU;MAE/CwF,UAAa,GAACxF,KAAqD,WAAOA,KAAA,KAAU;AAEjG;AACa,MAAAyF,SAAA,GAAYA,CAAIC,CAAA,EAAWC,CAAc;EACpD,IAAID,CAAE,CAAAE,IAAI,KAAKD,CAAA,CAAEC,IAAI,EAAE;IACrB,OAAO,KAAK;;EAGd,KAAK,MAAMC,IAAA,IAAQH,CAAG;IACpB,IAAI,CAACC,CAAA,CAAEG,GAAG,CAACD,IAAO;MAChB,OAAO,KAAK;;EAEhB;EAEA,OAAO,IAAI;AACb;AAEA;;;AAGC;AACM,SAASE,aAAcA,CAAAC,CAAa,EAAE;EAC3C,OAAOA,CAAA,CAAE5F,IAAI,KAAK,SAAa,IAAA4F,CAAA,CAAE5F,IAAI,KAAK,WAAW4F,CAAE,CAAA5F,IAAI,KAAK;AAClE;;AC5ZA;;;AAGC;AAEM,MAAM6F,EAAK,GAAAC,IAAA,CAAKD,EAAA;AAChB,MAAME,GAAM,OAAIF,EAAA;AAChB,MAAMG,KAAQ,GAAAD,GAAA,GAAMF,EAAA;AACd,MAAAI,QAAA,GAAWzF,MAAO,CAAA0F,iBAAA;AACxB,MAAMC,WAAc,GAAAN,EAAA,GAAK;AACzB,MAAMO,OAAU,GAAAP,EAAA,GAAK;AACrB,MAAMQ,UAAa,GAAAR,EAAA,GAAK;AAClB,MAAAS,aAAA,GAAgBT,EAAK,OAAI;AAEzB,MAAAU,KAAA,GAAQT,IAAK,CAAAS,KAAA;AACb,MAAAC,IAAA,GAAOV,IAAK,CAAAU,IAAA;AAElB,SAASC,YAAaA,CAAAvC,CAAS,EAAEE,CAAS,EAAEsC,OAAe,EAAE;EAClE,OAAOZ,IAAK,CAAAa,GAAG,CAACzC,CAAA,GAAIE,CAAK,IAAAsC,OAAA;AAC3B;AAEA;;AAEC;AACM,SAASE,OAAQA,CAAAC,KAAa,EAAE;EACrC,MAAMC,YAAA,GAAehB,IAAK,CAAAiB,KAAK,CAACF,KAAA;EAChCA,KAAA,GAAQJ,YAAA,CAAaI,KAAO,EAAAC,YAAA,EAAcD,KAAQ,WAAQC,YAAA,GAAeD,KAAK;EAC9E,MAAMG,SAAA,GAAYlB,IAAA,CAAKmB,GAAG,CAAC,IAAInB,IAAK,CAAAoB,KAAK,CAACX,KAAM,CAAAM,KAAA;EAChD,MAAMM,QAAA,GAAWN,KAAQ,GAAAG,SAAA;EACzB,MAAMI,YAAA,GAAeD,QAAY,QAAI,CAAI,GAAAA,QAAA,IAAY,IAAI,CAAI,GAAAA,QAAA,IAAY,CAAI,OAAI,EAAE;EACnF,OAAOC,YAAe,GAAAJ,SAAA;AACxB;AAEA;;;AAGC;AACM,SAASK,UAAWA,CAAAzH,KAAa,EAAE;EACxC,MAAM0H,MAAA,GAAmB,EAAE;EAC3B,MAAMC,IAAA,GAAOzB,IAAK,CAAAyB,IAAI,CAAC3H,KAAA;EACvB,IAAI8B,CAAA;EAEJ,KAAKA,CAAI,MAAGA,CAAI,GAAA6F,IAAA,EAAM7F,CAAK;IACzB,IAAI9B,KAAA,GAAQ8B,CAAA,KAAM,CAAG;MACnB4F,MAAA,CAAO5C,IAAI,CAAChD,CAAA;MACZ4F,MAAO,CAAA5C,IAAI,CAAC9E,KAAQ,GAAA8B,CAAA;;EAExB;EACA,IAAI6F,IAAU,MAAAA,IAAO,KAAI;IACvBD,MAAA,CAAO5C,IAAI,CAAC6C,IAAA;;EAGdD,MAAA,CAAOE,IAAI,CAAC,CAAClC,CAAA,EAAGC,CAAM,KAAAD,CAAA,GAAIC,CAAA,EAAGkC,GAAG;EAChC,OAAOH,MAAA;AACT;AAEA;;;AAGA,SAASI,cAAeA,CAAAC,CAAU,EAAE;EAClC,OAAO,OAAOA,CAAM,iBAAa,OAAOA,CAAM,iBAAYA,CAAA,KAAM,IAAI,IAAI,EAAEC,MAAA,CAAOC,WAAW,IAAIF,CAAA,IAAK,UAAc,IAAAA,CAAA,IAAK,aAAaA,CAAA;AACvI;AAEO,SAASG,QAASA,CAAAH,CAAU,EAAe;EAChD,OAAO,CAACD,cAAe,CAAAC,CAAA,KAAM,CAACI,KAAM,CAAA/G,UAAA,CAAW2G,CAAA,MAAiBlH,QAAS,CAAAkH,CAAA;AAC3E;AAEO,SAASK,YAAY9D,CAAS,EAAEwC,OAAe,EAAE;EACtD,MAAMuB,OAAA,GAAUnC,IAAK,CAAAiB,KAAK,CAAC7C,CAAA;EAC3B,OAAO+D,OAAY,GAAAvB,OAAA,IAAYxC,CAAO,IAAC+D,OAAA,GAAUvB,OAAY,IAAAxC,CAAA;AAC/D;AAEA;;;AAGO,SAASgE,kBACdA,CAAAC,KAA+B,EAC/B1F,MAAoC,EACpC2F,QAAgB,EAChB;EACA,IAAI1G,CAAA,EAAWO,IAAc,EAAArC,KAAA;EAE7B,KAAK8B,CAAA,GAAI,GAAGO,IAAO,GAAAkG,KAAA,CAAMtG,MAAM,EAAEH,CAAA,GAAIO,IAAA,EAAMP,CAAK;IAC9C9B,KAAA,GAAQuI,KAAK,CAACzG,CAAE,EAAC0G,QAAS;IAC1B,IAAI,CAACL,KAAA,CAAMnI,KAAQ;MACjB6C,MAAA,CAAO4F,GAAG,GAAGvC,IAAA,CAAKuC,GAAG,CAAC5F,MAAA,CAAO4F,GAAG,EAAEzI,KAAA;MAClC6C,MAAA,CAAO6F,GAAG,GAAGxC,IAAA,CAAKwC,GAAG,CAAC7F,MAAA,CAAO6F,GAAG,EAAE1I,KAAA;;EAEtC;AACF;AAEO,SAAS2I,SAAUA,CAAAC,OAAe,EAAE;EACzC,OAAOA,OAAA,IAAW3C,EAAA,GAAK,GAAE;AAC3B;AAEO,SAAS4C,SAAUA,CAAAC,OAAe,EAAE;EACzC,OAAOA,OAAA,IAAW,MAAM7C,EAAC;AAC3B;AAEA;;;;;;AAMC;AACM,SAAS8C,cAAeA,CAAAzE,CAAS,EAAE;EACxC,IAAI,CAAC3D,cAAA,CAAe2D,CAAI;IACtB;;EAEF,IAAI0B,CAAI;EACR,IAAIgD,CAAI;EACR,OAAO9C,IAAA,CAAKiB,KAAK,CAAC7C,CAAI,GAAA0B,CAAA,IAAKA,CAAA,KAAM1B,CAAG;IAClC0B,CAAK;IACLgD,CAAA;EACF;EACA,OAAOA,CAAA;AACT;AAEA;AACO,SAASC,kBACdC,WAAkB,EAClBC,UAAiB,EACjB;EACA,MAAMC,mBAAsB,GAAAD,UAAA,CAAW7E,CAAC,GAAG4E,WAAA,CAAY5E,CAAC;EACxD,MAAM+E,mBAAsB,GAAAF,UAAA,CAAW3E,CAAC,GAAG0E,WAAA,CAAY1E,CAAC;EACxD,MAAM8E,wBAAA,GAA2BpD,IAAK,CAAAyB,IAAI,CAACyB,mBAAA,GAAsBA,mBAAA,GAAsBC,mBAAsB,GAAAA,mBAAA;EAE7G,IAAIE,KAAQ,GAAArD,IAAA,CAAKsD,KAAK,CAACH,mBAAqB,EAAAD,mBAAA;EAE5C,IAAIG,KAAA,GAAS,CAAC,MAAMtD,EAAK;IACvBsD,KAAA,IAASpD,GAAA;;EAGX,OAAO;IACLoD,KAAA;IACAE,QAAU,EAAAH;EACZ;AACF;AAEO,SAASI,sBAAsBC,GAAU,EAAEC,GAAU,EAAE;EAC5D,OAAO1D,IAAA,CAAKyB,IAAI,CAACzB,IAAA,CAAKmB,GAAG,CAACuC,GAAA,CAAItF,CAAC,GAAGqF,GAAA,CAAIrF,CAAC,EAAE,KAAK4B,IAAA,CAAKmB,GAAG,CAACuC,GAAA,CAAIpF,CAAC,GAAGmF,GAAI,CAAAnF,CAAC,EAAE;AACxE;AAEA;;;AAGC;AACM,SAASqF,WAAWnE,CAAS,EAAEC,CAAS,EAAE;EAC/C,OAAO,CAACD,CAAA,GAAIC,CAAI,GAAAS,KAAI,IAAKD,GAAM,GAAAF,EAAA;AACjC;AAEA;;;AAGC;AACM,SAAS6D,eAAgBA,CAAApE,CAAS,EAAE;EACzC,OAAO,CAACA,CAAI,GAAAS,GAAA,GAAMA,GAAE,IAAKA,GAAA;AAC3B;AAEA;;;AAGO,SAAS4D,cAAcR,KAAa,EAAES,KAAa,EAAEC,GAAW,EAAEC,qBAA+B,EAAE;EACxG,MAAMxE,CAAA,GAAIoE,eAAgB,CAAAP,KAAA;EAC1B,MAAMY,CAAA,GAAIL,eAAgB,CAAAE,KAAA;EAC1B,MAAMhE,CAAA,GAAI8D,eAAgB,CAAAG,GAAA;EAC1B,MAAMG,YAAA,GAAeN,eAAA,CAAgBK,CAAI,GAAAzE,CAAA;EACzC,MAAM2E,UAAA,GAAaP,eAAA,CAAgB9D,CAAI,GAAAN,CAAA;EACvC,MAAM4E,YAAA,GAAeR,eAAA,CAAgBpE,CAAI,GAAAyE,CAAA;EACzC,MAAMI,UAAA,GAAaT,eAAA,CAAgBpE,CAAI,GAAAM,CAAA;EACvC,OAAON,CAAA,KAAMyE,CAAA,IAAKzE,CAAM,KAAAM,CAAA,IAAMkE,qBAAA,IAAyBC,CAAM,KAAAnE,CAAA,IACvDoE,YAAe,GAAAC,UAAA,IAAcC,YAAe,GAAAC,UAAA;AACpD;AAEA;;;;;;;AAOO,SAASC,WAAYA,CAAAxK,KAAa,EAAEyI,GAAW,EAAEC,GAAW,EAAE;EACnE,OAAOxC,IAAA,CAAKwC,GAAG,CAACD,GAAA,EAAKvC,IAAK,CAAAuC,GAAG,CAACC,GAAK,EAAA1I,KAAA;AACrC;AAEA;;;AAGC;AACM,SAASyK,WAAYA,CAAAzK,KAAa,EAAE;EACzC,OAAOwK,WAAA,CAAYxK,KAAO,GAAC,KAAO;AACpC;AAEA;;;;;;;AAOO,SAAS0K,WAAW1K,KAAa,EAAEgK,KAAa,EAAEC,GAAW,EAAEnD,OAAU,OAAI,EAAE;EACpF,OAAO9G,KAAS,IAAAkG,IAAA,CAAKuC,GAAG,CAACuB,KAAO,EAAAC,GAAA,IAAOnD,OAAW,IAAA9G,KAAA,IAASkG,IAAK,CAAAwC,GAAG,CAACsB,KAAA,EAAOC,GAAO,IAAAnD,OAAA;AACpF;AC3LO,SAAS6D,OACdA,CAAAC,KAAgB,EAChB5K,KAAa,EACb6K,GAAgC,EAChC;EACAA,GAAM,GAAAA,GAAA,KAASpI,KAAA,IAAUmI,KAAK,CAACnI,KAAA,CAAM,GAAGzC,KAAI;EAC5C,IAAI8K,EAAA,GAAKF,KAAM,CAAA3I,MAAM,GAAG;EACxB,IAAI8I,EAAK;EACT,IAAIC,GAAA;EAEJ,OAAOF,EAAA,GAAKC,EAAA,GAAK,CAAG;IAClBC,GAAM,GAACD,EAAA,GAAKD,EAAO;IACnB,IAAID,GAAA,CAAIG,GAAM;MACZD,EAAK,GAAAC,GAAA;KACA;MACLF,EAAK,GAAAE,GAAA;;EAET;EAEA,OAAO;IAACD,EAAA;IAAID;EAAE;AAChB;AAEA;;;;;;;AAOC;AACM,MAAMG,YAAe,GAAAA,CAC1BL,KACA,EAAA1H,GAAA,EACAlD,KACA,EAAAkL,IAAA,KAEAP,OAAQ,CAAAC,KAAA,EAAO5K,KAAO,EAAAkL,IAAA,GAClBzI,KAAS;EACT,MAAM0I,EAAK,GAAAP,KAAK,CAACnI,KAAA,CAAM,CAACS,GAAI;EAC5B,OAAOiI,EAAA,GAAKnL,KAAS,IAAAmL,EAAA,KAAOnL,KAAS,IAAA4K,KAAK,CAACnI,KAAQ,KAAE,CAACS,GAAA,CAAI,KAAKlD,KAAA;CAE/D,GAAAyC,KAAA,IAASmI,KAAK,CAACnI,KAAA,CAAM,CAACS,GAAA,CAAI,GAAGlD,KAAK;AAExC;;;;;;AAMC;AACY,MAAAoL,aAAA,GAAgBA,CAC3BR,KACA,EAAA1H,GAAA,EACAlD,KAAA,KAEA2K,OAAQ,CAAAC,KAAA,EAAO5K,KAAO,EAAAyC,KAAA,IAASmI,KAAK,CAACnI,KAAA,CAAM,CAACS,GAAA,CAAI,IAAIlD,KAAO;AAE7D;;;;;;;AAOO,SAASqL,cAAeA,CAAAC,MAAgB,EAAE7C,GAAW,EAAEC,GAAW,EAAE;EACzE,IAAIsB,KAAQ;EACZ,IAAIC,GAAA,GAAMqB,MAAA,CAAOrJ,MAAM;EAEvB,OAAO+H,KAAA,GAAQC,GAAO,IAAAqB,MAAM,CAACtB,KAAA,CAAM,GAAGvB,GAAK;IACzCuB,KAAA;EACF;EACA,OAAOC,GAAA,GAAMD,KAAS,IAAAsB,MAAM,CAACrB,GAAM,KAAE,GAAGvB,GAAK;IAC3CuB,GAAA;EACF;EAEA,OAAOD,KAAA,GAAQ,CAAK,IAAAC,GAAA,GAAMqB,MAAO,CAAArJ,MAAM,GACnCqJ,MAAA,CAAO7K,KAAK,CAACuJ,KAAO,EAAAC,GAAA,IACpBqB,MAAM;AACZ;AAEA,MAAMC,WAAc,IAAC,QAAQ,OAAO,SAAS,UAAU,UAAU;AAgB1D,SAASC,kBAAkBjD,KAAK,EAAEkD,QAAQ,EAAE;EACjD,IAAIlD,KAAA,CAAMmD,QAAQ,EAAE;IAClBnD,KAAA,CAAMmD,QAAQ,CAACC,SAAS,CAAC7G,IAAI,CAAC2G,QAAA;IAC9B;;EAGFpL,MAAO,CAAAuL,cAAc,CAACrD,KAAA,EAAO,UAAY;IACvCsD,YAAA,EAAc,IAAI;IAClBC,UAAA,EAAY,KAAK;IACjB9L,KAAO;MACL2L,SAAW,GAACF,QAAA;IACd;EACF;EAEAF,WAAY,CAAAQ,OAAO,CAAE7I,GAAQ;IAC3B,MAAM8I,MAAA,GAAS,YAAY7G,WAAY,CAAAjC,GAAA;IACvC,MAAM+I,IAAA,GAAO1D,KAAK,CAACrF,GAAI;IAEvB7C,MAAO,CAAAuL,cAAc,CAACrD,KAAA,EAAOrF,GAAK;MAChC2I,YAAA,EAAc,IAAI;MAClBC,UAAA,EAAY,KAAK;MACjB9L,KAAMA,CAAA,GAAGwB,IAAI,EAAE;QACb,MAAM0K,GAAM,GAAAD,IAAA,CAAKvK,KAAK,CAAC,IAAI,EAAEF,IAAA;QAE7B+G,KAAA,CAAMmD,QAAQ,CAACC,SAAS,CAACI,OAAO,CAAEI,MAAW;UAC3C,IAAI,OAAOA,MAAM,CAACH,MAAA,CAAO,KAAK,UAAY;YACxCG,MAAM,CAACH,MAAA,CAAO,CAAI,GAAAxK,IAAA;;QAEtB;QAEA,OAAO0K,GAAA;MACT;IACF;EACF;AACF;AAQO,SAASE,oBAAoB7D,KAAK,EAAEkD,QAAQ,EAAE;EACnD,MAAMY,IAAA,GAAO9D,KAAA,CAAMmD,QAAQ;EAC3B,IAAI,CAACW,IAAM;IACT;;EAGF,MAAMV,SAAA,GAAYU,IAAA,CAAKV,SAAS;EAChC,MAAMlJ,KAAA,GAAQkJ,SAAU,CAAAxI,OAAO,CAACsI,QAAA;EAChC,IAAIhJ,KAAA,KAAU,CAAC,CAAG;IAChBkJ,SAAU,CAAAW,MAAM,CAAC7J,KAAO;;EAG1B,IAAIkJ,SAAA,CAAU1J,MAAM,GAAG,CAAG;IACxB;;EAGFsJ,WAAY,CAAAQ,OAAO,CAAE7I,GAAQ;IAC3B,OAAOqF,KAAK,CAACrF,GAAI;EACnB;EAEA,OAAOqF,KAAA,CAAMmD,QAAQ;AACvB;AAEA;;AAEC;AACM,SAASa,YAAgBA,CAAAC,KAAU,EAAE;EAC1C,MAAMC,GAAA,GAAM,IAAIC,GAAO,CAAAF,KAAA;EAEvB,IAAIC,GAAI,CAAA7G,IAAI,KAAK4G,KAAA,CAAMvK,MAAM,EAAE;IAC7B,OAAOuK,KAAA;;EAGT,OAAOrM,KAAA,CAAMwM,IAAI,CAACF,GAAA;AACpB;ACzLO,SAASG,UAAWA,CAAAC,SAAiB,EAAEC,SAAiB,EAAEC,UAAkB,EAAE;EACnF,OAAOD,SAAA,GAAY,GAAM,GAAAD,SAAA,GAAY,KAAQ,GAAAE,UAAA;AAC/C;AAEA;;AAEA;AACa,MAAAC,gBAAA,GAAoB,YAAW;EAC1C,IAAI,OAAOC,MAAA,KAAW,WAAa;IACjC,OAAO,UAAS3L,QAAQ,EAAE;MACxB,OAAOA,QAAA;IACT;;EAEF,OAAO2L,MAAA,CAAOC,qBAAqB;AACrC,CAAK;AAEL;;;AAGC;AACM,SAASC,UACd5L,EAA4B,EAC5BE,OAAY,EACZ;EACA,IAAI2L,SAAA,GAAY,EAAE;EAClB,IAAIC,OAAA,GAAU,KAAK;EAEnB,OAAO,UAAS,GAAG7L,IAAW,EAAE;;IAE9B4L,SAAY,GAAA5L,IAAA;IACZ,IAAI,CAAC6L,OAAS;MACZA,OAAA,GAAU,IAAI;MACdL,gBAAiB,CAAAxM,IAAI,CAACyM,MAAA,EAAQ,MAAM;QAClCI,OAAA,GAAU,KAAK;QACf9L,EAAG,CAAAG,KAAK,CAACD,OAAS,EAAA2L,SAAA;MACpB;;EAEJ;AACF;AAEA;;AAEC;AACM,SAASE,SAAmC/L,EAA4B,EAAEgM,KAAa,EAAE;EAC9F,IAAIC,OAAA;EACJ,OAAO,UAAS,GAAGhM,IAAW,EAAE;IAC9B,IAAI+L,KAAO;MACTE,YAAa,CAAAD,OAAA;MACbA,OAAU,GAAAE,UAAA,CAAWnM,EAAA,EAAIgM,KAAO,EAAA/L,IAAA;KAC3B;MACLD,EAAG,CAAAG,KAAK,CAAC,IAAI,EAAEF,IAAA;;IAEjB,OAAO+L,KAAA;EACT;AACF;AAEA;;;AAGC;AACM,MAAMI,kBAAqB,GAACC,KAAsC,IAAAA,KAAA,KAAU,OAAU,YAASA,KAAU,aAAQ,OAAU;AAElI;;;AAGC;AACY,MAAAC,cAAA,GAAiBA,CAACD,KAAmC,EAAA5D,KAAA,EAAeC,GAAA,KAAgB2D,KAAU,eAAU5D,KAAA,GAAQ4D,KAAU,aAAQ3D,GAAA,GAAM,CAACD,KAAA,GAAQC,GAAE,IAAK;AAErK;;;AAGC;AACY,MAAA6D,MAAA,GAASA,CAACF,KAAoC,EAAAG,IAAA,EAAcC,KAAA,EAAeC,GAAiB;EACvG,MAAMC,KAAA,GAAQD,GAAM,YAAS,OAAO;EACpC,OAAOL,KAAA,KAAUM,KAAQ,GAAAF,KAAA,GAAQJ,KAAU,gBAAW,CAACG,IAAO,GAAAC,KAAI,IAAK,IAAID,IAAI;AACjF;AAEA;;;;AAIO,SAASI,gCAAiCA,CAAAC,IAAmC,EAAEC,MAAsB,EAAEC,kBAA2B,EAAE;EACzI,MAAMC,UAAA,GAAaF,MAAA,CAAOpM,MAAM;EAEhC,IAAI+H,KAAQ;EACZ,IAAIwE,KAAQ,GAAAD,UAAA;EAEZ,IAAIH,IAAA,CAAKK,OAAO,EAAE;IAChB,MAAM;MAACC,MAAM;MAAEC,MAAA;MAAQC;IAAA,CAAQ,GAAGR,IAAA;IAClC,MAAMS,QAAA,GAAWT,IAAK,CAAAU,OAAO,GAAGV,IAAK,CAAAU,OAAO,CAACzL,OAAO,GAAG+K,IAAK,CAAAU,OAAO,CAACzL,OAAO,CAACwL,QAAQ,GAAG,IAAI,GAAG,IAAI;IAClG,MAAME,IAAA,GAAOL,MAAA,CAAOK,IAAI;IACxB,MAAM;MAACtG,GAAG;MAAEC,GAAG;MAAEsG,UAAU;MAAEC;IAAU,CAAC,GAAGP,MAAA,CAAOQ,aAAa;IAE/D,IAAIF,UAAY;MACdhF,KAAQ,GAAA9D,IAAA,CAAKuC,GAAG;MAAA;MAEdwC,YAAA,CAAa2D,OAAS,EAAAG,IAAA,EAAMtG,GAAK,EAAAsC,EAAE;MAAA;MAEnCuD,kBAAqB,GAAAC,UAAA,GAAatD,YAAA,CAAaoD,MAAQ,EAAAU,IAAA,EAAML,MAAA,CAAOS,gBAAgB,CAAC1G,GAAA,GAAMsC,EAAE;MAC/F,IAAI8D,QAAU;QACZ,MAAMO,mBAAA,GAAuBR,OAC1B,CAAAnO,KAAK,CAAC,CAAG,EAAAuJ,KAAA,GAAQ,GACjBnI,OAAO,GACPwN,SAAS,CACRC,KAAA,IAAS,CAACvP,aAAA,CAAcuP,KAAK,CAACX,MAAA,CAAOI,IAAI,CAAC;QAC9C/E,KAAS,IAAA9D,IAAA,CAAKwC,GAAG,CAAC,CAAG,EAAA0G,mBAAA;;MAEvBpF,KAAQ,GAAAQ,WAAA,CAAYR,KAAO,KAAGuE,UAAa;;IAE7C,IAAIU,UAAY;MACd,IAAIhF,GAAM,GAAA/D,IAAA,CAAKwC,GAAG;MAAA;MAEhBuC,YAAa,CAAA2D,OAAA,EAASF,MAAO,CAAAK,IAAI,EAAErG,GAAA,EAAK,IAAI,CAAE,CAAAoC,EAAE,GAAG;MAAA;MAEnDwD,kBAAA,GAAqB,CAAI,GAAArD,YAAA,CAAaoD,MAAQ,EAAAU,IAAA,EAAML,MAAO,CAAAS,gBAAgB,CAACzG,GAAA,GAAM,IAAI,EAAEoC,EAAE,GAAG,CAAC;MAChG,IAAI+D,QAAU;QACZ,MAAMU,mBAAA,GAAuBX,OAC1B,CAAAnO,KAAK,CAACwJ,GAAA,GAAM,GACZoF,SAAS,CACRC,KAAA,IAAS,CAACvP,aAAc,CAAAuP,KAAK,CAACX,MAAA,CAAOI,IAAI,CAAC;QAC9C9E,GAAO,IAAA/D,IAAA,CAAKwC,GAAG,CAAC,CAAG,EAAA6G,mBAAA;;MAErBf,KAAQ,GAAAhE,WAAA,CAAYP,GAAK,EAAAD,KAAA,EAAOuE,UAAc,IAAAvE,KAAA;KACzC;MACLwE,KAAA,GAAQD,UAAa,GAAAvE,KAAA;;;EAIzB,OAAO;IAACA,KAAA;IAAOwE;EAAK;AACtB;AAEA;;;;;AAKC;AACM,SAASgB,mBAAoBA,CAAApB,IAAI,EAAE;EACxC,MAAM;IAACqB,MAAM;IAAEC,MAAA;IAAQC;EAAA,CAAa,GAAGvB,IAAA;EACvC,MAAMwB,SAAY;IAChBC,IAAA,EAAMJ,MAAA,CAAOhH,GAAG;IAChBqH,IAAA,EAAML,MAAA,CAAO/G,GAAG;IAChBqH,IAAA,EAAML,MAAA,CAAOjH,GAAG;IAChBuH,IAAA,EAAMN,MAAA,CAAOhH;EACf;EACA,IAAI,CAACiH,YAAc;IACjBvB,IAAA,CAAKuB,YAAY,GAAGC,SAAA;IACpB,OAAO,IAAI;;EAEb,MAAMK,OAAA,GAAUN,YAAA,CAAaE,IAAI,KAAKJ,MAAA,CAAOhH,GAAG,IAC7CkH,YAAa,CAAAG,IAAI,KAAKL,MAAA,CAAO/G,GAAG,IAChCiH,YAAA,CAAaI,IAAI,KAAKL,MAAO,CAAAjH,GAAG,IAChCkH,YAAa,CAAAK,IAAI,KAAKN,MAAA,CAAOhH,GAAG;EAEnCrI,MAAO,CAAA6P,MAAM,CAACP,YAAc,EAAAC,SAAA;EAC5B,OAAOK,OAAA;AACT;AChKA,MAAME,MAAS,GAACC,CAAc,IAAAA,CAAA,KAAM,KAAKA,CAAM;AAC/C,MAAMC,SAAA,GAAYA,CAACD,CAAA,EAAWjG,CAAW,EAAAnB,CAAA,KAAc,EAAE9C,IAAA,CAAKmB,GAAG,CAAC,CAAG,QAAM+I,CAAK,MAAM,IAAAlK,IAAA,CAAKoK,GAAG,CAAC,CAACF,CAAI,GAAAjG,CAAA,IAAKhE,GAAA,GAAM6C,CAAC;AAChH,MAAMuH,UAAA,GAAaA,CAACH,CAAW,EAAAjG,CAAA,EAAWnB,CAAA,KAAc9C,IAAK,CAAAmB,GAAG,CAAC,CAAG,GAAC,KAAK+I,CAAK,IAAAlK,IAAA,CAAKoK,GAAG,CAAE,CAAAF,CAAI,GAAAjG,CAAA,IAAKhE,GAAA,GAAM6C,CAAK;AAE7G;;;;AAIC;AAAA,MACKwH,OAAU;EACdC,MAAA,EAASL,CAAc,IAAAA,CAAA;EAEvBM,UAAY,EAACN,CAAA,IAAcA,CAAI,GAAAA,CAAA;EAE/BO,WAAA,EAAcP,CAAc,KAACA,CAAK,IAAAA,CAAA,GAAI;EAEtCQ,aAAe,EAACR,CAAA,IAAgB,CAAAA,CAAK,OAAE,IAAK,IACxC,GAAM,GAAAA,CAAA,GAAIA,CAAA,GACV,CAAC,OAAQ,EAAEA,CAAA,IAAMA,CAAI,KAAK,KAAE;EAEhCS,WAAa,EAACT,CAAc,IAAAA,CAAA,GAAIA,CAAI,GAAAA,CAAA;EAEpCU,YAAc,EAACV,CAAA,IAAc,CAACA,CAAA,IAAK,KAAKA,CAAA,GAAIA,CAAI;EAEhDW,cAAgB,EAACX,CAAA,IAAgB,CAAAA,CAAK,OAAE,IAAK,IACzC,GAAM,GAAAA,CAAA,GAAIA,CAAA,GAAIA,CACd,UAAQ,CAAAA,CAAA,IAAK,KAAKA,CAAA,GAAIA,CAAI,KAAE;EAEhCY,WAAA,EAAcZ,CAAA,IAAcA,CAAI,GAAAA,CAAA,GAAIA,CAAI,GAAAA,CAAA;EAExCa,YAAA,EAAeb,CAAA,IAAc,EAAE,CAACA,CAAK,SAAKA,CAAA,GAAIA,CAAI,GAAAA,CAAA,GAAI;EAEtDc,cAAgB,EAACd,CAAc,IAAC,CAACA,CAAK,OAAE,IAAK,IACzC,GAAM,GAAAA,CAAA,GAAIA,CAAI,GAAAA,CAAA,GAAIA,CAAA,GAClB,CAAC,OAAQ,CAAAA,CAAA,IAAK,KAAKA,CAAI,GAAAA,CAAA,GAAIA,CAAI,KAAE;EAErCe,WAAA,EAAcf,CAAA,IAAcA,CAAI,GAAAA,CAAA,GAAIA,CAAA,GAAIA,CAAI,GAAAA,CAAA;EAE5CgB,YAAc,EAAChB,CAAc,IAAC,CAAAA,CAAA,IAAK,KAAKA,CAAA,GAAIA,CAAI,GAAAA,CAAA,GAAIA,CAAI;EAExDiB,cAAgB,EAACjB,CAAc,IAAC,CAACA,CAAK,OAAE,IAAK,IACzC,GAAM,GAAAA,CAAA,GAAIA,CAAI,GAAAA,CAAA,GAAIA,CAAI,GAAAA,CAAA,GACtB,GAAO,KAACA,CAAK,SAAKA,CAAA,GAAIA,CAAI,GAAAA,CAAA,GAAIA,CAAI,KAAE;EAExCkB,UAAA,EAAalB,CAAc,KAAClK,IAAA,CAAKqL,GAAG,CAACnB,CAAA,GAAI5J,OAAW;EAEpDgL,WAAA,EAAcpB,CAAA,IAAclK,IAAK,CAAAoK,GAAG,CAACF,CAAI,GAAA5J,OAAA;EAEzCiL,aAAe,EAACrB,CAAc,KAAC,GAAO,IAAAlK,IAAA,CAAKqL,GAAG,CAACtL,EAAK,GAAAmK,CAAA,IAAK;EAEzDsB,UAAA,EAAatB,CAAA,IAAcA,CAAC,KAAM,IAAK,CAAI,GAAAlK,IAAA,CAAKmB,GAAG,CAAC,CAAG,QAAM+I,CAAA,GAAI,EAAG;EAEpEuB,WAAA,EAAcvB,CAAA,IAAcA,CAAC,KAAM,IAAK,CAAI,IAAClK,IAAK,CAAAmB,GAAG,CAAC,GAAG,CAAC,KAAK+I,CAAA,IAAK,CAAC;EAErEwB,aAAA,EAAgBxB,CAAA,IAAcD,MAAO,CAAAC,CAAA,IAAKA,CAAA,GAAIA,CAAI,SAC9C,GAAM,GAAAlK,IAAA,CAAKmB,GAAG,CAAC,CAAG,QAAM+I,CAAI,OAAI,MAChC,GAAO,KAAClK,IAAA,CAAKmB,GAAG,CAAC,GAAG,CAAC,MAAM+I,CAAI,OAAI,MAAM,EAAE;EAE/CyB,UAAA,EAAazB,CAAA,IAAcA,CAAC,IAAK,IAAKA,CAAI,KAAElK,IAAA,CAAKyB,IAAI,CAAC,IAAIyI,CAAI,GAAAA,CAAA,IAAK,EAAE;EAErE0B,WAAa,EAAC1B,CAAc,IAAAlK,IAAA,CAAKyB,IAAI,CAAC,IAAI,CAACyI,CAAK,SAAKA,CAAA;EAErD2B,aAAA,EAAgB3B,CAAA,IAAc,CAAEA,CAAK,OAAE,IAAK,IACxC,CAAC,OAAOlK,IAAA,CAAKyB,IAAI,CAAC,IAAIyI,CAAI,GAAAA,CAAA,IAAK,KAC/B,GAAO,IAAAlK,IAAA,CAAKyB,IAAI,CAAC,CAAI,GAAC,CAAAyI,CAAK,SAAKA,CAAA,IAAK,EAAE;EAE3C4B,aAAe,EAAC5B,CAAA,IAAcD,MAAO,CAAAC,CAAA,IAAKA,CAAA,GAAIC,SAAU,CAAAD,CAAA,EAAG,OAAO,GAAI;EAEtE6B,cAAgB,EAAC7B,CAAA,IAAcD,MAAO,CAAAC,CAAA,IAAKA,CAAA,GAAIG,UAAW,CAAAH,CAAA,EAAG,OAAO,GAAI;EAExE8B,iBAAiB9B,CAAS,EAAE;IAC1B,MAAMjG,CAAI;IACV,MAAMnB,CAAI;IACV,OAAOmH,MAAA,CAAOC,CAAK,IAAAA,CAAA,GACjBA,CAAA,GAAI,GACA,SAAMC,SAAA,CAAUD,CAAI,MAAGjG,CAAG,EAAAnB,CAAA,IAC1B,MAAM,GAAM,GAAAuH,UAAA,CAAWH,CAAA,GAAI,CAAI,MAAGjG,CAAA,EAAGnB,CAAE;EAC/C;EAEAmJ,WAAW/B,CAAS,EAAE;IACpB,MAAMjG,CAAI;IACV,OAAOiG,CAAA,GAAIA,CAAA,IAAM,CAAAjG,CAAI,QAAKiG,CAAA,GAAIjG,CAAA;EAChC;EAEAiI,YAAYhC,CAAS,EAAE;IACrB,MAAMjG,CAAI;IACV,OAAO,CAACiG,CAAK,SAAKA,CAAK,KAACjG,CAAI,QAAKiG,CAAA,GAAIjG,CAAA,CAAK;EAC5C;EAEAkI,cAAcjC,CAAS,EAAE;IACvB,IAAIjG,CAAI;IACR,IAAI,CAACiG,CAAK,OAAE,IAAK,CAAG;MAClB,OAAO,OAAOA,CAAA,GAAIA,CAAK,KAAE,CAAAjG,CAAA,IAAM,KAAK,IAAK,KAAKiG,CAAA,GAAIjG,CAAA,CAAC;;IAErD,OAAO,OAAO,CAACiG,CAAA,IAAK,KAAKA,CAAA,IAAM,EAACjG,CAAA,IAAM,KAAK,IAAK,KAAKiG,CAAA,GAAIjG,CAAA,IAAK;EAChE;EAEAmI,YAAA,EAAelC,CAAc,QAAII,OAAQ,CAAA+B,aAAa,CAAC,CAAI,GAAAnC,CAAA;EAE3DmC,cAAcnC,CAAS,EAAE;IACvB,MAAMoC,CAAI;IACV,MAAMC,CAAI;IACV,IAAIrC,CAAA,GAAK,IAAIqC,CAAI;MACf,OAAOD,CAAA,GAAIpC,CAAI,GAAAA,CAAA;;IAEjB,IAAIA,CAAA,GAAK,IAAIqC,CAAI;MACf,OAAOD,CAAA,IAAKpC,CAAA,IAAM,GAAM,GAAAqC,CAAC,IAAKrC,CAAI;;IAEpC,IAAIA,CAAA,GAAK,MAAMqC,CAAI;MACjB,OAAOD,CAAA,IAAKpC,CAAA,IAAM,IAAO,GAAAqC,CAAC,IAAKrC,CAAI;;IAErC,OAAOoC,CAAA,IAAKpC,CAAA,IAAM,KAAQ,GAAAqC,CAAC,IAAKrC,CAAI;EACtC;EAEAsC,eAAA,EAAkBtC,CAAc,IAACA,CAAA,GAAI,GACjC,GAAAI,OAAA,CAAQ8B,YAAY,CAAClC,CAAA,GAAI,CAAK,UAC9BI,OAAA,CAAQ+B,aAAa,CAACnC,CAAA,GAAI,CAAI,QAAK,MAAM;AAC/C;ACrHO,SAASuC,mBAAoBA,CAAA3S,KAAc,EAA2C;EAC3F,IAAIA,KAAA,IAAS,OAAOA,KAAA,KAAU,QAAU;IACtC,MAAMI,IAAA,GAAOJ,KAAA,CAAMO,QAAQ;IAC3B,OAAOH,IAAA,KAAS,4BAA4BA,IAAS;;EAGvD,OAAO,KAAK;AACd;AAWO,SAASwS,KAAMA,CAAA5S,KAAK,EAAE;EAC3B,OAAO2S,mBAAoB,CAAA3S,KAAA,IAASA,KAAQ,OAAI6S,KAAA,CAAM7S,KAAM;AAC9D;AAKO,SAAS8S,aAAcA,CAAA9S,KAAK,EAAE;EACnC,OAAO2S,mBAAoB,CAAA3S,KAAA,IACvBA,KACA,OAAI6S,KAAM,CAAA7S,KAAA,EAAO+S,QAAQ,CAAC,GAAK,EAAAC,MAAM,CAAC,KAAKC,SAAS,EAAE;AAC5D;AC/BA,MAAMC,OAAU,IAAC,KAAK,KAAK,eAAe,UAAU,UAAU;AAC9D,MAAMC,MAAS,IAAC,SAAS,eAAe,kBAAkB;AAEnD,SAASC,uBAAwBA,CAAAC,QAAQ,EAAE;EAChDA,QAAS,CAAA5G,GAAG,CAAC,WAAa;IACxBc,KAAO,EAAAtN,SAAA;IACPqT,QAAU;IACVC,MAAQ;IACRhS,EAAI,EAAAtB,SAAA;IACJ0M,IAAM,EAAA1M,SAAA;IACNuT,IAAM,EAAAvT,SAAA;IACNwT,EAAI,EAAAxT,SAAA;IACJG,IAAM,EAAAH;EACR;EAEAoT,QAAS,CAAAK,QAAQ,CAAC,WAAa;IAC7BC,SAAA,EAAW,KAAK;IAChBC,UAAA,EAAY,KAAK;IACjBC,WAAA,EAAcC,IAAS,IAAAA,IAAA,KAAS,YAAgB,IAAAA,IAAA,KAAS,gBAAgBA,IAAS;EACpF;EAEAT,QAAS,CAAA5G,GAAG,CAAC,YAAc;IACzB0G,MAAQ;MACN/S,IAAM;MACN2T,UAAY,EAAAZ;IACd;IACAD,OAAS;MACP9S,IAAM;MACN2T,UAAY,EAAAb;IACd;EACF;EAEAG,QAAS,CAAAK,QAAQ,CAAC,YAAc;IAC9BC,SAAW;EACb;EAEAN,QAAS,CAAA5G,GAAG,CAAC,aAAe;IAC1BuH,MAAQ;MACNC,SAAW;QACTX,QAAU;MACZ;IACF;IACAY,MAAQ;MACND,SAAW;QACTX,QAAU;MACZ;IACF;IACAa,IAAM;MACJC,UAAY;QACVjB,MAAQ;UACNxG,IAAM;QACR;QACA0H,OAAS;UACPjU,IAAM;UACNkT,QAAA,EAAU;QACZ;MACF;IACF;IACAgB,IAAM;MACJF,UAAY;QACVjB,MAAQ;UACNM,EAAI;QACN;QACAY,OAAS;UACPjU,IAAM;UACNmT,MAAQ;UACRhS,EAAA,EAAI8C,CAAA,IAAKA,CAAI;QACf;MACF;IACF;EACF;AACF;ACvEO,SAASkQ,oBAAqBA,CAAAlB,QAAQ,EAAE;EAC7CA,QAAS,CAAA5G,GAAG,CAAC,QAAU;IACrB+H,WAAA,EAAa,IAAI;IACjBC,OAAS;MACPC,GAAK;MACL1G,KAAO;MACP2G,MAAQ;MACR5G,IAAM;IACR;EACF;AACF;ACTA,MAAM6G,SAAA,GAAY,IAAIC,GAAA;AAEtB,SAASC,eAAgBA,CAAAC,MAAc,EAAE1R,OAAkC,EAAE;EAC3EA,OAAA,GAAUA,OAAA,IAAW,EAAC;EACtB,MAAM2R,QAAW,GAAAD,MAAA,GAASE,IAAK,CAAAC,SAAS,CAAC7R,OAAA;EACzC,IAAI8R,SAAA,GAAYP,SAAU,CAAAQ,GAAG,CAACJ,QAAA;EAC9B,IAAI,CAACG,SAAW;IACdA,SAAA,GAAY,IAAIE,IAAA,CAAKC,YAAY,CAACP,MAAQ,EAAA1R,OAAA;IAC1CuR,SAAU,CAAAnI,GAAG,CAACuI,QAAU,EAAAG,SAAA;;EAE1B,OAAOA,SAAA;AACT;AAEO,SAASI,YAAaA,CAAAC,GAAW,EAAET,MAAc,EAAE1R,OAAkC,EAAE;EAC5F,OAAOyR,eAAgB,CAAAC,MAAA,EAAQ1R,OAAS,EAAAoS,MAAM,CAACD,GAAA;AACjD;ACRA,MAAME,UAAa;EAOjBpK,OAAOtL,KAAK,EAAE;IACZ,OAAOE,OAAA,CAAQF,KAAS,IAAyBA,KAAA,GAAS,KAAKA,KAAK;EACtE;EAUA2V,QAAQC,SAAS,EAAEnT,KAAK,EAAEoT,KAAK,EAAE;IAC/B,IAAID,SAAA,KAAc,CAAG;MACnB,OAAO;;IAGT,MAAMb,MAAA,GAAS,IAAI,CAACe,KAAK,CAACzS,OAAO,CAAC0R,MAAM;IACxC,IAAIgB,QAAA;IACJ,IAAIC,KAAA,GAAQJ,SAAA;IAEZ,IAAIC,KAAA,CAAM5T,MAAM,GAAG,CAAG;MAEpB,MAAMgU,OAAA,GAAU/P,IAAA,CAAKwC,GAAG,CAACxC,IAAA,CAAKa,GAAG,CAAC8O,KAAK,CAAC,CAAE,EAAC7V,KAAK,CAAG,EAAAkG,IAAA,CAAKa,GAAG,CAAC8O,KAAK,CAACA,KAAA,CAAM5T,MAAM,GAAG,CAAE,EAACjC,KAAK;MACzF,IAAIiW,OAAA,GAAU,IAAQ,IAAAA,OAAA,GAAU,KAAO;QACrCF,QAAW;;MAGbC,KAAA,GAAQE,cAAA,CAAeN,SAAW,EAAAC,KAAA;;IAGpC,MAAMM,QAAW,GAAAxP,KAAA,CAAMT,IAAK,CAAAa,GAAG,CAACiP,KAAA;IAOhC,MAAMI,UAAA,GAAajO,KAAM,CAAAgO,QAAA,IAAY,CAAI,GAAAjQ,IAAA,CAAKwC,GAAG,CAACxC,IAAA,CAAKuC,GAAG,CAAC,CAAC,CAAI,GAAAvC,IAAA,CAAKoB,KAAK,CAAC6O,QAAA,GAAW,KAAK,CAAE;IAE7F,MAAM9S,OAAU;MAAC0S,QAAA;MAAUM,qBAAuB,EAAAD,UAAA;MAAYE,qBAAuB,EAAAF;IAAU;IAC/F/V,MAAO,CAAA6P,MAAM,CAAC7M,OAAS,MAAI,CAACA,OAAO,CAACwS,KAAK,CAACJ,MAAM;IAEhD,OAAOF,YAAA,CAAaK,SAAA,EAAWb,MAAQ,EAAA1R,OAAA;EACzC;EAWAkT,YAAYX,SAAS,EAAEnT,KAAK,EAAEoT,KAAK,EAAE;IACnC,IAAID,SAAA,KAAc,CAAG;MACnB,OAAO;;IAET,MAAMY,MAAS,GAAAX,KAAK,CAACpT,KAAA,CAAM,CAACgU,WAAW,IAAKb,SAAa,GAAA1P,IAAA,CAAKmB,GAAG,CAAC,IAAInB,IAAK,CAAAoB,KAAK,CAACX,KAAM,CAAAiP,SAAA;IACvF,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,CAACc,QAAQ,CAACF,MAAA,KAAW/T,KAAA,GAAQ,GAAM,GAAAoT,KAAA,CAAM5T,MAAM,EAAE;MACvE,OAAOyT,UAAA,CAAWC,OAAO,CAACnV,IAAI,CAAC,IAAI,EAAEoV,SAAA,EAAWnT,KAAO,EAAAoT,KAAA;;IAEzD,OAAO;EACT;AAEF;AAGA,SAASK,cAAeA,CAAAN,SAAS,EAAEC,KAAK,EAAE;EAGxC,IAAIG,KAAA,GAAQH,KAAM,CAAA5T,MAAM,GAAG,IAAI4T,KAAK,CAAC,CAAE,EAAC7V,KAAK,GAAG6V,KAAK,CAAC,CAAE,EAAC7V,KAAK,GAAG6V,KAAK,CAAC,CAAE,EAAC7V,KAAK,GAAG6V,KAAK,CAAC,CAAE,EAAC7V,KAAK;EAGhG,IAAIkG,IAAA,CAAKa,GAAG,CAACiP,KAAA,KAAU,KAAKJ,SAAc,KAAA1P,IAAA,CAAKoB,KAAK,CAACsO,SAAY;IAE/DI,KAAQ,GAAAJ,SAAA,GAAY1P,IAAK,CAAAoB,KAAK,CAACsO,SAAA;;EAEjC,OAAOI,KAAA;AACT;AAMA,IAAAW,KAAA,GAAe;EAACjB;AAAU,CAAE;ACnGrB,SAASkB,kBAAmBA,CAAAvD,QAAQ,EAAE;EAC3CA,QAAS,CAAA5G,GAAG,CAAC,OAAS;IACpBoK,OAAA,EAAS,IAAI;IACbC,MAAA,EAAQ,KAAK;IACbjV,OAAA,EAAS,KAAK;IACdkV,WAAA,EAAa,KAAK;IASlBC,MAAQ;IAERC,IAAA,EAAM,IAAI;IAMVC,KAAO;IAGPC,IAAM;MACJN,OAAA,EAAS,IAAI;MACbO,SAAW;MACXC,eAAA,EAAiB,IAAI;MACrBC,SAAA,EAAW,IAAI;MACfC,UAAY;MACZC,SAAA,EAAWA,CAACC,IAAA,EAAMpU,OAAY,KAAAA,OAAA,CAAQ+T,SAAS;MAC/CM,SAAA,EAAWA,CAACD,IAAA,EAAMpU,OAAY,KAAAA,OAAA,CAAQuP,KAAK;MAC3CkE,MAAA,EAAQ;IACV;IAEAa,MAAQ;MACNd,OAAA,EAAS,IAAI;MACbe,IAAA,EAAM,EAAE;MACRC,UAAY;MACZC,KAAO;IACT;IAGAC,KAAO;MAELlB,OAAA,EAAS,KAAK;MAGdmB,IAAM;MAGNvD,OAAS;QACPC,GAAK;QACLC,MAAQ;MACV;IACF;IAGAkB,KAAO;MACLoC,WAAa;MACbC,WAAa;MACbC,MAAA,EAAQ,KAAK;MACbC,eAAiB;MACjBC,eAAiB;MACjB5D,OAAS;MACToC,OAAA,EAAS,IAAI;MACbyB,QAAA,EAAU,IAAI;MACdC,eAAiB;MACjBC,WAAa;MAEblX,QAAU,EAAAqV,KAAA,CAAMjB,UAAU,CAACpK,MAAM;MACjCmN,KAAA,EAAO,EAAC;MACRC,KAAA,EAAO,EAAC;MACR9K,KAAO;MACP+K,UAAY;MAEZC,iBAAA,EAAmB,KAAK;MACxBC,aAAe;MACfC,eAAiB;IACnB;EACF;EAEAzF,QAAA,CAAS0F,KAAK,CAAC,aAAe,WAAS,EAAI;EAC3C1F,QAAA,CAAS0F,KAAK,CAAC,YAAc,WAAS,EAAI;EAC1C1F,QAAA,CAAS0F,KAAK,CAAC,cAAgB,WAAS,EAAI;EAC5C1F,QAAA,CAAS0F,KAAK,CAAC,aAAe,WAAS,EAAI;EAE3C1F,QAAS,CAAAK,QAAQ,CAAC,OAAS;IACzBC,SAAA,EAAW,KAAK;IAChBE,WAAA,EAAcC,IAAA,IAAS,CAACA,IAAA,CAAKkF,UAAU,CAAC,aAAa,CAAClF,IAAA,CAAKkF,UAAU,CAAC,OAAY,KAAAlF,IAAA,KAAS,cAAcA,IAAS;IAClHF,UAAA,EAAaE,IAAS,IAAAA,IAAA,KAAS,YAAgB,IAAAA,IAAA,KAAS,oBAAoBA,IAAS;EACvF;EAEAT,QAAS,CAAAK,QAAQ,CAAC,QAAU;IAC1BC,SAAW;EACb;EAEAN,QAAS,CAAAK,QAAQ,CAAC,aAAe;IAC/BG,WAAA,EAAcC,IAAA,IAASA,IAAS,0BAAqBA,IAAS;IAC9DF,UAAY,EAACE,IAAA,IAASA,IAAS;EACjC;AACF;MClGamF,SAAY,GAAA5Y,MAAA,CAAOyC,MAAM,CAAC,IAAI;MAC9BoW,WAAc,GAAA7Y,MAAA,CAAOyC,MAAM,CAAC,IAAI;AAO7C,SAASqW,WAASC,IAAI,EAAElW,GAAG,EAAE;EAC3B,IAAI,CAACA,GAAK;IACR,OAAOkW,IAAA;;EAET,MAAMpX,IAAA,GAAOkB,GAAI,CAAAyB,KAAK,CAAC;EACvB,KAAK,IAAI7C,CAAI,MAAGiG,CAAI,GAAA/F,IAAA,CAAKC,MAAM,EAAEH,CAAA,GAAIiG,CAAG,IAAEjG,CAAG;IAC3C,MAAMkB,CAAA,GAAIhB,IAAI,CAACF,CAAE;IACjBsX,IAAA,GAAOA,IAAI,CAACpW,CAAE,MAAKoW,IAAI,CAACpW,CAAA,CAAE,GAAG3C,MAAA,CAAOyC,MAAM,CAAC,IAAI;EACjD;EACA,OAAOsW,IAAA;AACT;AAEA,SAAS3M,IAAI4M,IAAI,EAAErV,KAAK,EAAEsH,MAAM,EAAE;EAChC,IAAI,OAAOtH,KAAA,KAAU,QAAU;IAC7B,OAAOR,KAAA,CAAM2V,UAAS,CAAAE,IAAA,EAAMrV,KAAQ,GAAAsH,MAAA;;EAEtC,OAAO9H,KAAA,CAAM2V,UAAS,CAAAE,IAAA,EAAM,EAAK,GAAArV,KAAA;AACnC;AAMO,MAAMsV,QAAA;EACXC,WAAYA,CAAAC,YAAY,EAAEC,SAAS,EAAE;IACnC,IAAI,CAACxF,SAAS,GAAGhU,SAAA;IACjB,IAAI,CAACyZ,eAAe,GAAG;IACvB,IAAI,CAACC,WAAW,GAAG;IACnB,IAAI,CAAC/G,KAAK,GAAG;IACb,IAAI,CAACgH,QAAQ,GAAG,EAAC;IACjB,IAAI,CAACC,gBAAgB,GAAIC,OAAA,IAAYA,OAAA,CAAQhE,KAAK,CAACiE,QAAQ,CAACC,mBAAmB;IAC/E,IAAI,CAACC,QAAQ,GAAG,EAAC;IACjB,IAAI,CAACC,MAAM,GAAG,CACZ,aACA,YACA,SACA,cACA,YACD;IACD,IAAI,CAACC,IAAI,GAAG;MACVC,MAAQ;MACRxU,IAAM;MACNyU,KAAO;MACPC,UAAY;MACZC,MAAA,EAAQ;IACV;IACA,IAAI,CAACC,KAAK,GAAG,EAAC;IACd,IAAI,CAACC,oBAAoB,GAAG,CAACC,GAAA,EAAKrX,OAAY,KAAAyP,aAAA,CAAczP,OAAA,CAAQqW,eAAe;IACnF,IAAI,CAACiB,gBAAgB,GAAG,CAACD,GAAA,EAAKrX,OAAY,KAAAyP,aAAA,CAAczP,OAAA,CAAQsW,WAAW;IAC3E,IAAI,CAACiB,UAAU,GAAG,CAACF,GAAA,EAAKrX,OAAY,KAAAyP,aAAA,CAAczP,OAAA,CAAQuP,KAAK;IAC/D,IAAI,CAACiI,SAAS,GAAG;IACjB,IAAI,CAACC,WAAW,GAAG;MACjBC,IAAM;MACNC,SAAA,EAAW,IAAI;MACfC,gBAAA,EAAkB;IACpB;IACA,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,EAAC;IAChB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,KAAK,GAAGvb,SAAA;IACb,IAAI,CAACwb,MAAM,GAAG,EAAC;IACf,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,uBAAuB,GAAG,IAAI;IAEnC,IAAI,CAACjI,QAAQ,CAAC8F,YAAA;IACd,IAAI,CAAC9X,KAAK,CAAC+X,SAAA;EACb;EAMAhN,GAAIA,CAAAzI,KAAK,EAAEsH,MAAM,EAAE;IACjB,OAAOmB,GAAA,CAAI,IAAI,EAAEzI,KAAO,EAAAsH,MAAA;EAC1B;EAKA8J,IAAIpR,KAAK,EAAE;IACT,OAAOmV,UAAA,CAAS,IAAI,EAAEnV,KAAA;EACxB;EAMA0P,QAASA,CAAA1P,KAAK,EAAEsH,MAAM,EAAE;IACtB,OAAOmB,GAAA,CAAIyM,WAAA,EAAalV,KAAO,EAAAsH,MAAA;EACjC;EAEAsQ,QAASA,CAAA5X,KAAK,EAAEsH,MAAM,EAAE;IACtB,OAAOmB,GAAA,CAAIwM,SAAA,EAAWjV,KAAO,EAAAsH,MAAA;EAC/B;EAmBAyN,MAAM/U,KAAK,EAAE8P,IAAI,EAAE+H,WAAW,EAAEC,UAAU,EAAE;IAC1C,MAAMC,WAAA,GAAc5C,UAAS,KAAI,EAAEnV,KAAA;IACnC,MAAMgY,iBAAA,GAAoB7C,UAAS,KAAI,EAAE0C,WAAA;IACzC,MAAMI,WAAA,GAAc,GAAM,GAAAnI,IAAA;IAE1BzT,MAAO,CAAA6b,gBAAgB,CAACH,WAAa;MAEnC,CAACE,WAAA,GAAc;QACbjc,KAAO,EAAA+b,WAAW,CAACjI,IAAK;QACxBqI,QAAA,EAAU;MACZ;MAEA,CAACrI,IAAA,GAAO;QACNhI,UAAA,EAAY,IAAI;QAChBsJ,GAAMA,CAAA;UACJ,MAAMgH,KAAA,GAAQ,IAAI,CAACH,WAAY;UAC/B,MAAMpZ,MAAA,GAASmZ,iBAAiB,CAACF,UAAW;UAC5C,IAAIpb,QAAA,CAAS0b,KAAQ;YACnB,OAAO/b,MAAO,CAAA6P,MAAM,CAAC,IAAIrN,MAAQ,EAAAuZ,KAAA;;UAEnC,OAAOpb,cAAA,CAAeob,KAAO,EAAAvZ,MAAA;QAC/B;QACA4J,IAAIzM,KAAK,EAAE;UACT,IAAI,CAACic,WAAA,CAAY,GAAGjc,KAAA;QACtB;MACF;IACF;EACF;EAEA0B,MAAM2a,QAAQ,EAAE;IACdA,QAAA,CAAStQ,OAAO,CAAErK,KAAA,IAAUA,KAAA,CAAM,IAAI;EACxC;AACF;AAGA,IAAA2R,QAAA,GAAe,eAAgB,IAAIiG,QAAS;EAC1CzF,WAAA,EAAcC,IAAA,IAAS,CAACA,IAAA,CAAKkF,UAAU,CAAC;EACxCpF,UAAY,EAACE,IAAA,IAASA,IAAS;EAC/B0G,KAAO;IACL7G,SAAW;EACb;EACAmH,WAAa;IACXjH,WAAA,EAAa,KAAK;IAClBD,UAAA,EAAY;EACd;AACF,CAAG,GAACR,uBAAA,EAAyBmB,oBAAA,EAAsBqC,kBAAA,CAAmB,CAAE;;AC5JxE;;;;;AAKC;AACM,SAAS0F,YAAaA,CAAAnC,IAAc,EAAE;EAC3C,IAAI,CAACA,IAAA,IAAQpa,aAAc,CAAAoa,IAAA,CAAKvU,IAAI,CAAK,IAAA7F,aAAA,CAAcoa,IAAK,CAAAC,MAAM,CAAG;IACnE,OAAO,IAAI;;EAGb,OAAO,CAACD,IAAK,CAAAE,KAAK,GAAGF,IAAA,CAAKE,KAAK,GAAG,GAAM,KAAE,KACvCF,IAAA,CAAKI,MAAM,GAAGJ,IAAK,CAAAI,MAAM,GAAG,MAAM,EAAC,CACpC,GAAAJ,IAAA,CAAKvU,IAAI,GAAG,KACZ,GAAAuU,IAAA,CAAKC,MAAM;AACf;AAEA;;AAEC;AACM,SAASmC,YACdA,CAAA7B,GAA6B,EAC7B8B,IAA4B,EAC5BC,EAAY,EACZC,OAAe,EACfC,MAAc,EACd;EACA,IAAIC,SAAA,GAAYJ,IAAI,CAACG,MAAO;EAC5B,IAAI,CAACC,SAAW;IACdA,SAAY,GAAAJ,IAAI,CAACG,MAAO,IAAGjC,GAAA,CAAImC,WAAW,CAACF,MAAA,EAAQ7E,KAAK;IACxD2E,EAAA,CAAG3X,IAAI,CAAC6X,MAAA;;EAEV,IAAIC,SAAA,GAAYF,OAAS;IACvBA,OAAU,GAAAE,SAAA;;EAEZ,OAAOF,OAAA;AACT;AAKA;;AAEC,GAFD,CAEC;AAEM,SAASI,aACdpC,GAA6B,EAC7BP,IAAY,EACZ4C,aAAqB,EACrBC,KAAiF,EACjF;EACAA,KAAA,GAAQA,KAAA,IAAS,EAAC;EAClB,IAAIR,IAAA,GAAOQ,KAAM,CAAAR,IAAI,GAAGQ,KAAM,CAAAR,IAAI,IAAI,EAAC;EACvC,IAAIC,EAAA,GAAKO,KAAM,CAAAC,cAAc,GAAGD,KAAM,CAAAC,cAAc,IAAI,EAAE;EAE1D,IAAID,KAAA,CAAM7C,IAAI,KAAKA,IAAM;IACvBqC,IAAO,GAAAQ,KAAA,CAAMR,IAAI,GAAG,EAAC;IACrBC,EAAK,GAAAO,KAAA,CAAMC,cAAc,GAAG,EAAE;IAC9BD,KAAA,CAAM7C,IAAI,GAAGA,IAAA;;EAGfO,GAAA,CAAIwC,IAAI;EAERxC,GAAA,CAAIP,IAAI,GAAGA,IAAA;EACX,IAAIuC,OAAU;EACd,MAAMra,IAAA,GAAO0a,aAAA,CAAc9a,MAAM;EACjC,IAAIH,CAAA,EAAWqb,CAAW,EAAAC,IAAA,EAAcC,KAAwB,EAAAC,WAAA;EAChE,KAAKxb,CAAI,MAAGA,CAAI,GAAAO,IAAA,EAAMP,CAAK;IACzBub,KAAQ,GAAAN,aAAa,CAACjb,CAAE;;IAGxB,IAAIub,KAAA,KAAUpd,SAAa,IAAAod,KAAA,KAAU,IAAI,IAAI,CAACnd,OAAA,CAAQmd,KAAQ;MAC5DX,OAAA,GAAUH,YAAa,CAAA7B,GAAA,EAAK8B,IAAM,EAAAC,EAAA,EAAIC,OAAS,EAAAW,KAAA;KAC1C,UAAInd,OAAA,CAAQmd,KAAQ;;;MAGzB,KAAKF,CAAA,GAAI,GAAGC,IAAO,GAAAC,KAAA,CAAMpb,MAAM,EAAEkb,CAAA,GAAIC,IAAA,EAAMD,CAAK;QAC9CG,WAAc,GAAAD,KAAK,CAACF,CAAE;;QAEtB,IAAIG,WAAA,KAAgBrd,SAAa,IAAAqd,WAAA,KAAgB,IAAI,IAAI,CAACpd,OAAA,CAAQod,WAAc;UAC9EZ,OAAA,GAAUH,YAAa,CAAA7B,GAAA,EAAK8B,IAAM,EAAAC,EAAA,EAAIC,OAAS,EAAAY,WAAA;;MAEnD;;EAEJ;EAEA5C,GAAA,CAAI6C,OAAO;EAEX,MAAMC,KAAA,GAAQf,EAAG,CAAAxa,MAAM,GAAG;EAC1B,IAAIub,KAAA,GAAQT,aAAc,CAAA9a,MAAM,EAAE;IAChC,KAAKH,CAAI,MAAGA,CAAI,GAAA0b,KAAA,EAAO1b,CAAK;MAC1B,OAAO0a,IAAI,CAACC,EAAE,CAAC3a,CAAA,CAAE,CAAC;IACpB;IACA2a,EAAG,CAAAnQ,MAAM,CAAC,CAAG,EAAAkR,KAAA;;EAEf,OAAOd,OAAA;AACT;AAEA;;;;;;;;AAQO,SAASe,WAAYA,CAAA3H,KAAY,EAAE4H,KAAa,EAAE5F,KAAa,EAAE;EACtE,MAAM+B,gBAAA,GAAmB/D,KAAA,CAAM6H,uBAAuB;EACtD,MAAMC,SAAA,GAAY9F,KAAA,KAAU,CAAI,GAAA5R,IAAA,CAAKwC,GAAG,CAACoP,KAAA,GAAQ,CAAG,SAAO,CAAC;EAC5D,OAAO5R,IAAA,CAAKiB,KAAK,CAAE,CAAAuW,KAAQ,GAAAE,SAAQ,IAAK/D,gBAAA,IAAoBA,gBAAmB,GAAA+D,SAAA;AACjF;AAEA;;AAEC;AACM,SAASC,YAAYC,MAA0B,EAAEpD,GAA8B,EAAE;EACtF,IAAI,CAACA,GAAO,KAACoD,MAAQ;IACnB;;EAGFpD,GAAM,GAAAA,GAAA,IAAOoD,MAAO,CAAAC,UAAU,CAAC;EAE/BrD,GAAA,CAAIwC,IAAI;;;EAGRxC,GAAA,CAAIsD,cAAc;EAClBtD,GAAI,CAAAuD,SAAS,CAAC,CAAG,KAAGH,MAAA,CAAOhG,KAAK,EAAEgG,MAAA,CAAOI,MAAM;EAC/CxD,GAAA,CAAI6C,OAAO;AACb;AASO,SAASY,UACdzD,GAA6B,EAC7BrX,OAAyB,EACzBiB,CAAS,EACTE,CAAS,EACT;;EAEA4Z,eAAA,CAAgB1D,GAAK,EAAArX,OAAA,EAASiB,CAAG,EAAAE,CAAA,EAAG,IAAI;AAC1C;AAEA;AACO,SAAS4Z,eACdA,CAAA1D,GAA6B,EAC7BrX,OAAyB,EACzBiB,CAAS,EACTE,CAAS,EACT6Z,CAAS,EACT;EACA,IAAIje,IAAA,EAAcke,OAAiB,EAAAC,OAAA,EAAiB3Y,IAAc,EAAA4Y,YAAA,EAAsB1G,KAAA,EAAe2G,QAAkB,EAAAC,QAAA;EACzH,MAAMrE,KAAA,GAAQhX,OAAA,CAAQsb,UAAU;EAChC,MAAMC,QAAA,GAAWvb,OAAA,CAAQub,QAAQ;EACjC,MAAMC,MAAA,GAASxb,OAAA,CAAQwb,MAAM;EAC7B,IAAIC,GAAM,GAAC,CAAAF,QAAA,IAAY,KAAKrY,WAAA;EAE5B,IAAI8T,KAAA,IAAS,OAAOA,KAAA,KAAU,QAAU;IACtCja,IAAA,GAAOia,KAAA,CAAM9Z,QAAQ;IACrB,IAAIH,IAAA,KAAS,2BAA+B,IAAAA,IAAA,KAAS,4BAA8B;MACjFsa,GAAA,CAAIwC,IAAI;MACRxC,GAAI,CAAAqE,SAAS,CAACza,CAAG,EAAAE,CAAA;MACjBkW,GAAA,CAAIsE,MAAM,CAACF,GAAA;MACXpE,GAAA,CAAIuE,SAAS,CAAC5E,KAAA,EAAO,CAACA,KAAA,CAAMvC,KAAK,GAAG,GAAG,CAACuC,KAAA,CAAM6D,MAAM,GAAG,GAAG7D,KAAA,CAAMvC,KAAK,EAAEuC,KAAA,CAAM6D,MAAM;MACnFxD,GAAA,CAAI6C,OAAO;MACX;;;EAIJ,IAAIpV,KAAA,CAAM0W,MAAW,KAAAA,MAAA,IAAU,CAAG;IAChC;;EAGFnE,GAAA,CAAIwE,SAAS;EAEb,QAAQ7E,KAAA;;IAEN;MACE,IAAIgE,CAAG;QACL3D,GAAI,CAAAyE,OAAO,CAAC7a,CAAG,EAAAE,CAAA,EAAG6Z,CAAA,GAAI,CAAG,EAAAQ,MAAA,EAAQ,GAAG,CAAG,EAAA1Y,GAAA;OAClC;QACLuU,GAAA,CAAI0E,GAAG,CAAC9a,CAAG,EAAAE,CAAA,EAAGqa,MAAA,EAAQ,CAAG,EAAA1Y,GAAA;;MAE3BuU,GAAA,CAAI2E,SAAS;MACb;IACF,KAAK;MACHvH,KAAQ,GAAAuG,CAAA,GAAIA,CAAI,OAAIQ,MAAM;MAC1BnE,GAAA,CAAI4E,MAAM,CAAChb,CAAI,GAAA4B,IAAA,CAAKoK,GAAG,CAACwO,GAAO,IAAAhH,KAAA,EAAOtT,CAAI,GAAA0B,IAAA,CAAKqL,GAAG,CAACuN,GAAO,IAAAD,MAAA;MAC1DC,GAAO,IAAApY,aAAA;MACPgU,GAAA,CAAI6E,MAAM,CAACjb,CAAI,GAAA4B,IAAA,CAAKoK,GAAG,CAACwO,GAAO,IAAAhH,KAAA,EAAOtT,CAAI,GAAA0B,IAAA,CAAKqL,GAAG,CAACuN,GAAO,IAAAD,MAAA;MAC1DC,GAAO,IAAApY,aAAA;MACPgU,GAAA,CAAI6E,MAAM,CAACjb,CAAI,GAAA4B,IAAA,CAAKoK,GAAG,CAACwO,GAAO,IAAAhH,KAAA,EAAOtT,CAAI,GAAA0B,IAAA,CAAKqL,GAAG,CAACuN,GAAO,IAAAD,MAAA;MAC1DnE,GAAA,CAAI2E,SAAS;MACb;IACF,KAAK;;;;;;;;MAQHb,YAAA,GAAeK,MAAS;MACxBjZ,IAAA,GAAOiZ,MAAS,GAAAL,YAAA;MAChBF,OAAA,GAAUpY,IAAK,CAAAqL,GAAG,CAACuN,GAAA,GAAMrY,UAAc,IAAAb,IAAA;MACvC6Y,QAAW,GAAAvY,IAAA,CAAKqL,GAAG,CAACuN,GAAM,GAAArY,UAAA,KAAe4X,CAAA,GAAIA,CAAI,OAAIG,YAAe,GAAA5Y,IAAI,CAAD;MACvE2Y,OAAA,GAAUrY,IAAK,CAAAoK,GAAG,CAACwO,GAAA,GAAMrY,UAAc,IAAAb,IAAA;MACvC8Y,QAAW,GAAAxY,IAAA,CAAKoK,GAAG,CAACwO,GAAM,GAAArY,UAAA,KAAe4X,CAAA,GAAIA,CAAI,OAAIG,YAAe,GAAA5Y,IAAI,CAAD;MACvE8U,GAAI,CAAA0E,GAAG,CAAC9a,CAAI,GAAAma,QAAA,EAAUja,CAAA,GAAI+Z,OAAS,EAAAC,YAAA,EAAcM,GAAM,GAAA7Y,EAAA,EAAI6Y,GAAM,GAAAtY,OAAA;MACjEkU,GAAI,CAAA0E,GAAG,CAAC9a,CAAI,GAAAoa,QAAA,EAAUla,CAAA,GAAI8Z,OAAS,EAAAE,YAAA,EAAcM,GAAA,GAAMtY,OAAS,EAAAsY,GAAA;MAChEpE,GAAI,CAAA0E,GAAG,CAAC9a,CAAI,GAAAma,QAAA,EAAUja,CAAA,GAAI+Z,OAAS,EAAAC,YAAA,EAAcM,GAAA,EAAKA,GAAM,GAAAtY,OAAA;MAC5DkU,GAAI,CAAA0E,GAAG,CAAC9a,CAAI,GAAAoa,QAAA,EAAUla,CAAA,GAAI8Z,OAAS,EAAAE,YAAA,EAAcM,GAAM,GAAAtY,OAAA,EAASsY,GAAM,GAAA7Y,EAAA;MACtEyU,GAAA,CAAI2E,SAAS;MACb;IACF,KAAK;MACH,IAAI,CAACT,QAAU;QACbhZ,IAAO,GAAAM,IAAA,CAAKsZ,OAAO,GAAGX,MAAA;QACtB/G,KAAQ,GAAAuG,CAAA,GAAIA,CAAI,OAAIzY,IAAI;QACxB8U,GAAI,CAAA+E,IAAI,CAACnb,CAAI,GAAAwT,KAAA,EAAOtT,CAAA,GAAIoB,IAAM,MAAIkS,KAAA,EAAO,CAAI,GAAAlS,IAAA;QAC7C;;MAEFkZ,GAAO,IAAArY,UAAA;IACT;IACA,KAAK;MACHgY,QAAW,GAAAvY,IAAA,CAAKqL,GAAG,CAACuN,GAAA,KAAQT,CAAI,GAAAA,CAAA,GAAI,CAAI,GAAAQ,MAAM,CAAD;MAC7CP,OAAU,GAAApY,IAAA,CAAKqL,GAAG,CAACuN,GAAO,IAAAD,MAAA;MAC1BN,OAAU,GAAArY,IAAA,CAAKoK,GAAG,CAACwO,GAAO,IAAAD,MAAA;MAC1BH,QAAW,GAAAxY,IAAA,CAAKoK,GAAG,CAACwO,GAAA,KAAQT,CAAI,GAAAA,CAAA,GAAI,CAAI,GAAAQ,MAAM,CAAD;MAC7CnE,GAAA,CAAI4E,MAAM,CAAChb,CAAI,GAAAma,QAAA,EAAUja,CAAI,GAAA+Z,OAAA;MAC7B7D,GAAA,CAAI6E,MAAM,CAACjb,CAAI,GAAAoa,QAAA,EAAUla,CAAI,GAAA8Z,OAAA;MAC7B5D,GAAA,CAAI6E,MAAM,CAACjb,CAAI,GAAAma,QAAA,EAAUja,CAAI,GAAA+Z,OAAA;MAC7B7D,GAAA,CAAI6E,MAAM,CAACjb,CAAI,GAAAoa,QAAA,EAAUla,CAAI,GAAA8Z,OAAA;MAC7B5D,GAAA,CAAI2E,SAAS;MACb;IACF,KAAK;MACHP,GAAO,IAAArY,UAAA;IACT;IACA,KAAK;MACHgY,QAAW,GAAAvY,IAAA,CAAKqL,GAAG,CAACuN,GAAA,KAAQT,CAAI,GAAAA,CAAA,GAAI,CAAI,GAAAQ,MAAM,CAAD;MAC7CP,OAAU,GAAApY,IAAA,CAAKqL,GAAG,CAACuN,GAAO,IAAAD,MAAA;MAC1BN,OAAU,GAAArY,IAAA,CAAKoK,GAAG,CAACwO,GAAO,IAAAD,MAAA;MAC1BH,QAAW,GAAAxY,IAAA,CAAKoK,GAAG,CAACwO,GAAA,KAAQT,CAAI,GAAAA,CAAA,GAAI,CAAI,GAAAQ,MAAM,CAAD;MAC7CnE,GAAA,CAAI4E,MAAM,CAAChb,CAAI,GAAAma,QAAA,EAAUja,CAAI,GAAA+Z,OAAA;MAC7B7D,GAAA,CAAI6E,MAAM,CAACjb,CAAI,GAAAma,QAAA,EAAUja,CAAI,GAAA+Z,OAAA;MAC7B7D,GAAA,CAAI4E,MAAM,CAAChb,CAAI,GAAAoa,QAAA,EAAUla,CAAI,GAAA8Z,OAAA;MAC7B5D,GAAA,CAAI6E,MAAM,CAACjb,CAAI,GAAAoa,QAAA,EAAUla,CAAI,GAAA8Z,OAAA;MAC7B;IACF,KAAK;MACHG,QAAW,GAAAvY,IAAA,CAAKqL,GAAG,CAACuN,GAAA,KAAQT,CAAI,GAAAA,CAAA,GAAI,CAAI,GAAAQ,MAAM,CAAD;MAC7CP,OAAU,GAAApY,IAAA,CAAKqL,GAAG,CAACuN,GAAO,IAAAD,MAAA;MAC1BN,OAAU,GAAArY,IAAA,CAAKoK,GAAG,CAACwO,GAAO,IAAAD,MAAA;MAC1BH,QAAW,GAAAxY,IAAA,CAAKoK,GAAG,CAACwO,GAAA,KAAQT,CAAI,GAAAA,CAAA,GAAI,CAAI,GAAAQ,MAAM,CAAD;MAC7CnE,GAAA,CAAI4E,MAAM,CAAChb,CAAI,GAAAma,QAAA,EAAUja,CAAI,GAAA+Z,OAAA;MAC7B7D,GAAA,CAAI6E,MAAM,CAACjb,CAAI,GAAAma,QAAA,EAAUja,CAAI,GAAA+Z,OAAA;MAC7B7D,GAAA,CAAI4E,MAAM,CAAChb,CAAI,GAAAoa,QAAA,EAAUla,CAAI,GAAA8Z,OAAA;MAC7B5D,GAAA,CAAI6E,MAAM,CAACjb,CAAI,GAAAoa,QAAA,EAAUla,CAAI,GAAA8Z,OAAA;MAC7BQ,GAAO,IAAArY,UAAA;MACPgY,QAAW,GAAAvY,IAAA,CAAKqL,GAAG,CAACuN,GAAA,KAAQT,CAAI,GAAAA,CAAA,GAAI,CAAI,GAAAQ,MAAM,CAAD;MAC7CP,OAAU,GAAApY,IAAA,CAAKqL,GAAG,CAACuN,GAAO,IAAAD,MAAA;MAC1BN,OAAU,GAAArY,IAAA,CAAKoK,GAAG,CAACwO,GAAO,IAAAD,MAAA;MAC1BH,QAAW,GAAAxY,IAAA,CAAKoK,GAAG,CAACwO,GAAA,KAAQT,CAAI,GAAAA,CAAA,GAAI,CAAI,GAAAQ,MAAM,CAAD;MAC7CnE,GAAA,CAAI4E,MAAM,CAAChb,CAAI,GAAAma,QAAA,EAAUja,CAAI,GAAA+Z,OAAA;MAC7B7D,GAAA,CAAI6E,MAAM,CAACjb,CAAI,GAAAma,QAAA,EAAUja,CAAI,GAAA+Z,OAAA;MAC7B7D,GAAA,CAAI4E,MAAM,CAAChb,CAAI,GAAAoa,QAAA,EAAUla,CAAI,GAAA8Z,OAAA;MAC7B5D,GAAA,CAAI6E,MAAM,CAACjb,CAAI,GAAAoa,QAAA,EAAUla,CAAI,GAAA8Z,OAAA;MAC7B;IACF,KAAK;MACHA,OAAA,GAAUD,CAAA,GAAIA,CAAI,OAAInY,IAAA,CAAKqL,GAAG,CAACuN,GAAA,IAAOD,MAAM;MAC5CN,OAAU,GAAArY,IAAA,CAAKoK,GAAG,CAACwO,GAAO,IAAAD,MAAA;MAC1BnE,GAAA,CAAI4E,MAAM,CAAChb,CAAI,GAAAga,OAAA,EAAS9Z,CAAI,GAAA+Z,OAAA;MAC5B7D,GAAA,CAAI6E,MAAM,CAACjb,CAAI,GAAAga,OAAA,EAAS9Z,CAAI,GAAA+Z,OAAA;MAC5B;IACF,KAAK;MACH7D,GAAI,CAAA4E,MAAM,CAAChb,CAAG,EAAAE,CAAA;MACdkW,GAAA,CAAI6E,MAAM,CAACjb,CAAA,GAAI4B,IAAA,CAAKqL,GAAG,CAACuN,GAAA,KAAQT,CAAA,GAAIA,CAAI,OAAIQ,MAAM,CAAD,EAAIra,CAAA,GAAI0B,IAAK,CAAAoK,GAAG,CAACwO,GAAO,IAAAD,MAAA;MACzE;IACF,KAAK,KAAK;MACRnE,GAAA,CAAI2E,SAAS;MACb;EACJ;EAEA3E,GAAA,CAAIgF,IAAI;EACR,IAAIrc,OAAA,CAAQsc,WAAW,GAAG,CAAG;IAC3BjF,GAAA,CAAIkF,MAAM;;AAEd;AAEA;;;;;;;AAOO,SAASC,cACdA,CAAAvQ,KAAY,EACZwQ,IAAU,EACVC,MAAe,EACf;EACAA,MAAS,GAAAA,MAAA,IAAU;EAEnB,OAAO,CAACD,IAAA,IAASxQ,KAAS,IAAAA,KAAA,CAAMhL,CAAC,GAAGwb,IAAK,CAAA/R,IAAI,GAAGgS,MAAA,IAAUzQ,KAAM,CAAAhL,CAAC,GAAGwb,IAAA,CAAK9R,KAAK,GAAG+R,MACjF,IAAAzQ,KAAA,CAAM9K,CAAC,GAAGsb,IAAK,CAAApL,GAAG,GAAGqL,MAAA,IAAUzQ,KAAM,CAAA9K,CAAC,GAAGsb,IAAA,CAAKnL,MAAM,GAAGoL,MAAA;AACzD;AAEO,SAASC,SAAStF,GAA6B,EAAEoF,IAAU,EAAE;EAClEpF,GAAA,CAAIwC,IAAI;EACRxC,GAAA,CAAIwE,SAAS;EACbxE,GAAA,CAAI+E,IAAI,CAACK,IAAA,CAAK/R,IAAI,EAAE+R,IAAA,CAAKpL,GAAG,EAAEoL,IAAA,CAAK9R,KAAK,GAAG8R,IAAA,CAAK/R,IAAI,EAAE+R,IAAA,CAAKnL,MAAM,GAAGmL,IAAA,CAAKpL,GAAG;EAC5EgG,GAAA,CAAIzD,IAAI;AACV;AAEO,SAASgJ,UAAWA,CAAAvF,GAA6B,EAAE;EACxDA,GAAA,CAAI6C,OAAO;AACb;AAEA;;AAEC;AACM,SAAS2C,cACdA,CAAAxF,GAA6B,EAC7BzW,QAAe,EACfpB,MAAa,EACbsd,IAAc,EACdpF,IAAa,EACb;EACA,IAAI,CAAC9W,QAAU;IACb,OAAOyW,GAAA,CAAI6E,MAAM,CAAC1c,MAAA,CAAOyB,CAAC,EAAEzB,MAAA,CAAO2B,CAAC;;EAEtC,IAAIuW,IAAA,KAAS,QAAU;IACrB,MAAMqF,QAAA,GAAW,CAACnc,QAAA,CAASK,CAAC,GAAGzB,MAAA,CAAOyB,CAAA,IAAK;IAC3CoW,GAAA,CAAI6E,MAAM,CAACa,QAAU,EAAAnc,QAAA,CAASO,CAAC;IAC/BkW,GAAA,CAAI6E,MAAM,CAACa,QAAU,EAAAvd,MAAA,CAAO2B,CAAC;EAC/B,OAAO,IAAIuW,IAAA,KAAS,OAAY,MAAC,CAACoF,IAAM;IACtCzF,GAAA,CAAI6E,MAAM,CAACtb,QAAA,CAASK,CAAC,EAAEzB,MAAA,CAAO2B,CAAC;GAC1B;IACLkW,GAAA,CAAI6E,MAAM,CAAC1c,MAAA,CAAOyB,CAAC,EAAEL,QAAA,CAASO,CAAC;;EAEjCkW,GAAA,CAAI6E,MAAM,CAAC1c,MAAA,CAAOyB,CAAC,EAAEzB,MAAA,CAAO2B,CAAC;AAC/B;AAEA;;;AAGO,SAAS6b,eACd3F,GAA6B,EAC7BzW,QAAqB,EACrBpB,MAAmB,EACnBsd,IAAc,EACd;EACA,IAAI,CAAClc,QAAU;IACb,OAAOyW,GAAA,CAAI6E,MAAM,CAAC1c,MAAA,CAAOyB,CAAC,EAAEzB,MAAA,CAAO2B,CAAC;;EAEtCkW,GAAA,CAAI4F,aAAa,CACfH,IAAO,GAAAlc,QAAA,CAASsc,IAAI,GAAGtc,QAAA,CAASuc,IAAI,EACpCL,IAAA,GAAOlc,QAAS,CAAAwc,IAAI,GAAGxc,QAAA,CAASyc,IAAI,EACpCP,IAAA,GAAOtd,MAAO,CAAA2d,IAAI,GAAG3d,MAAA,CAAO0d,IAAI,EAChCJ,IAAA,GAAOtd,MAAO,CAAA6d,IAAI,GAAG7d,MAAA,CAAO4d,IAAI,EAChC5d,MAAA,CAAOyB,CAAC,EACRzB,MAAA,CAAO2B,CAAC;AACZ;AAEA,SAASmc,aAAcA,CAAAjG,GAA6B,EAAEkG,IAAoB,EAAE;EAC1E,IAAIA,IAAA,CAAKC,WAAW,EAAE;IACpBnG,GAAI,CAAAqE,SAAS,CAAC6B,IAAA,CAAKC,WAAW,CAAC,EAAE,EAAED,IAAA,CAAKC,WAAW,CAAC,CAAE;;EAGxD,IAAI,CAAC9gB,aAAA,CAAc6gB,IAAK,CAAAhC,QAAQ,CAAG;IACjClE,GAAI,CAAAsE,MAAM,CAAC4B,IAAA,CAAKhC,QAAQ;;EAG1B,IAAIgC,IAAA,CAAKhO,KAAK,EAAE;IACd8H,GAAI,CAAAoG,SAAS,GAAGF,IAAA,CAAKhO,KAAK;;EAG5B,IAAIgO,IAAA,CAAKG,SAAS,EAAE;IAClBrG,GAAI,CAAAqG,SAAS,GAAGH,IAAA,CAAKG,SAAS;;EAGhC,IAAIH,IAAA,CAAKI,YAAY,EAAE;IACrBtG,GAAI,CAAAsG,YAAY,GAAGJ,IAAA,CAAKI,YAAY;;AAExC;AAEA,SAASC,aACPvG,GAA6B,EAC7BpW,CAAS,EACTE,CAAS,EACT0c,IAAY,EACZN,IAAoB,EACpB;EACA,IAAIA,IAAK,CAAAO,aAAa,IAAIP,IAAA,CAAKQ,SAAS,EAAE;IACxC;;;;;;AAMC;IACD,MAAMC,OAAA,GAAU3G,GAAI,CAAAmC,WAAW,CAACqE,IAAA;IAChC,MAAMnT,IAAA,GAAOzJ,CAAI,GAAA+c,OAAA,CAAQC,qBAAqB;IAC9C,MAAMtT,KAAA,GAAQ1J,CAAI,GAAA+c,OAAA,CAAQE,sBAAsB;IAChD,MAAM7M,GAAA,GAAMlQ,CAAI,GAAA6c,OAAA,CAAQG,uBAAuB;IAC/C,MAAM7M,MAAA,GAASnQ,CAAI,GAAA6c,OAAA,CAAQI,wBAAwB;IACnD,MAAMC,WAAA,GAAcd,IAAK,CAAAO,aAAa,GAAI,CAAAzM,GAAM,GAAAC,MAAK,IAAK,IAAIA,MAAM;IAEpE+F,GAAI,CAAAiH,WAAW,GAAGjH,GAAA,CAAIoG,SAAS;IAC/BpG,GAAA,CAAIwE,SAAS;IACbxE,GAAA,CAAItD,SAAS,GAAGwJ,IAAK,CAAAgB,eAAe,IAAI;IACxClH,GAAI,CAAA4E,MAAM,CAACvR,IAAM,EAAA2T,WAAA;IACjBhH,GAAI,CAAA6E,MAAM,CAACvR,KAAO,EAAA0T,WAAA;IAClBhH,GAAA,CAAIkF,MAAM;;AAEd;AAEA,SAASiC,YAAaA,CAAAnH,GAA6B,EAAEkG,IAAqB,EAAE;EAC1E,MAAMkB,QAAA,GAAWpH,GAAA,CAAIoG,SAAS;EAE9BpG,GAAI,CAAAoG,SAAS,GAAGF,IAAA,CAAKhO,KAAK;EAC1B8H,GAAA,CAAIqH,QAAQ,CAACnB,IAAK,CAAA7S,IAAI,EAAE6S,IAAA,CAAKlM,GAAG,EAAEkM,IAAK,CAAA9I,KAAK,EAAE8I,IAAA,CAAK1C,MAAM;EACzDxD,GAAA,CAAIoG,SAAS,GAAGgB,QAAA;AAClB;AAEA;;AAEC;AACM,SAASE,WACdtH,GAA6B,EAC7B1C,IAAuB,EACvB1T,CAAS,EACTE,CAAS,EACT2V,IAAoB,EACpByG,IAAuB,KAAE,EACzB;EACA,MAAMqB,KAAA,GAAQ/hB,OAAQ,CAAA8X,IAAA,IAAQA,IAAO,IAACA,IAAA,CAAK;EAC3C,MAAM4H,MAAA,GAASgB,IAAK,CAAAsB,WAAW,GAAG,CAAK,IAAAtB,IAAA,CAAKuB,WAAW,KAAK;EAC5D,IAAIrgB,CAAW,EAAAof,IAAA;EAEfxG,GAAA,CAAIwC,IAAI;EACRxC,GAAI,CAAAP,IAAI,GAAGA,IAAA,CAAKwC,MAAM;EACtBgE,aAAA,CAAcjG,GAAK,EAAAkG,IAAA;EAEnB,KAAK9e,CAAA,GAAI,CAAG,EAAAA,CAAA,GAAImgB,KAAA,CAAMhgB,MAAM,EAAE,EAAEH,CAAG;IACjCof,IAAO,GAAAe,KAAK,CAACngB,CAAE;IAEf,IAAI8e,IAAA,CAAKwB,QAAQ,EAAE;MACjBP,YAAa,CAAAnH,GAAA,EAAKkG,IAAA,CAAKwB,QAAQ;;IAGjC,IAAIxC,MAAQ;MACV,IAAIgB,IAAA,CAAKuB,WAAW,EAAE;QACpBzH,GAAI,CAAAiH,WAAW,GAAGf,IAAA,CAAKuB,WAAW;;MAGpC,IAAI,CAACpiB,aAAA,CAAc6gB,IAAK,CAAAsB,WAAW,CAAG;QACpCxH,GAAI,CAAAtD,SAAS,GAAGwJ,IAAA,CAAKsB,WAAW;;MAGlCxH,GAAA,CAAI2H,UAAU,CAACnB,IAAA,EAAM5c,CAAG,EAAAE,CAAA,EAAGoc,IAAA,CAAK0B,QAAQ;;IAG1C5H,GAAA,CAAI6H,QAAQ,CAACrB,IAAA,EAAM5c,CAAG,EAAAE,CAAA,EAAGoc,IAAA,CAAK0B,QAAQ;IACtCrB,YAAa,CAAAvG,GAAA,EAAKpW,CAAG,EAAAE,CAAA,EAAG0c,IAAM,EAAAN,IAAA;IAE9Bpc,CAAK,IAAA5D,MAAA,CAAOuZ,IAAA,CAAKG,UAAU;EAC7B;EAEAI,GAAA,CAAI6C,OAAO;AACb;AAEA;;;;AAIC;AACM,SAASiF,mBACd9H,GAA6B,EAC7B+E,IAA2C,EAC3C;EACA,MAAM;IAACnb,CAAC;IAAEE,CAAC;IAAE6Z,CAAC;IAAEoE,CAAC;IAAE5D;EAAM,CAAC,GAAGY,IAAA;;EAG7B/E,GAAA,CAAI0E,GAAG,CAAC9a,CAAA,GAAIua,MAAO,CAAA6D,OAAO,EAAEle,CAAI,GAAAqa,MAAA,CAAO6D,OAAO,EAAE7D,MAAA,CAAO6D,OAAO,EAAE,GAAM,GAAAzc,EAAA,EAAIA,EAAA,EAAI,IAAI;;EAGlFyU,GAAA,CAAI6E,MAAM,CAACjb,CAAA,EAAGE,CAAI,GAAAie,CAAA,GAAI5D,MAAA,CAAO8D,UAAU;;EAGvCjI,GAAA,CAAI0E,GAAG,CAAC9a,CAAA,GAAIua,MAAO,CAAA8D,UAAU,EAAEne,CAAI,GAAAie,CAAA,GAAI5D,MAAO,CAAA8D,UAAU,EAAE9D,MAAO,CAAA8D,UAAU,EAAE1c,EAAA,EAAIO,OAAA,EAAS,IAAI;;EAG9FkU,GAAA,CAAI6E,MAAM,CAACjb,CAAA,GAAI+Z,CAAA,GAAIQ,MAAO,CAAA+D,WAAW,EAAEpe,CAAI,GAAAie,CAAA;;EAG3C/H,GAAA,CAAI0E,GAAG,CAAC9a,CAAA,GAAI+Z,CAAI,GAAAQ,MAAA,CAAO+D,WAAW,EAAEpe,CAAA,GAAIie,CAAI,GAAA5D,MAAA,CAAO+D,WAAW,EAAE/D,MAAA,CAAO+D,WAAW,EAAEpc,OAAA,EAAS,GAAG,IAAI;;EAGpGkU,GAAA,CAAI6E,MAAM,CAACjb,CAAA,GAAI+Z,CAAG,EAAA7Z,CAAA,GAAIqa,MAAA,CAAOgE,QAAQ;;EAGrCnI,GAAA,CAAI0E,GAAG,CAAC9a,CAAA,GAAI+Z,CAAI,GAAAQ,MAAA,CAAOgE,QAAQ,EAAEre,CAAA,GAAIqa,MAAO,CAAAgE,QAAQ,EAAEhE,MAAO,CAAAgE,QAAQ,EAAE,CAAG,GAACrc,OAAA,EAAS,IAAI;;EAGxFkU,GAAA,CAAI6E,MAAM,CAACjb,CAAI,GAAAua,MAAA,CAAO6D,OAAO,EAAEle,CAAA;AACjC;ACxgBA,MAAMse,WAAc;AACpB,MAAMC,UAAa;AAEnB;;;GAAA,C;;;;;;;AAWC;AACM,SAASC,aAAahjB,KAAsB,EAAE4F,IAAY,EAAU;EACzE,MAAMqd,OAAA,GAAU,CAAC,KAAKjjB,KAAI,EAAGkjB,KAAK,CAACJ,WAAA;EACnC,IAAI,CAACG,OAAW,IAAAA,OAAO,CAAC,EAAE,KAAK,QAAU;IACvC,OAAOrd,IAAO;;EAGhB5F,KAAQ,IAACijB,OAAO,CAAC,CAAE;EAEnB,QAAQA,OAAO,CAAC,CAAE;IAChB,KAAK;MACH,OAAOjjB,KAAA;IACT,KAAK;MACHA,KAAS;MACT;EAGJ;EAEA,OAAO4F,IAAO,GAAA5F,KAAA;AAChB;AAEA,MAAMmjB,YAAe,GAAC9e,CAAe,KAACA,CAAK;AAQpC,SAAS+e,kBAAkBpjB,KAAsC,EAAEqjB,KAAwC,EAAE;EAClH,MAAMC,GAAA,GAAM,EAAC;EACb,MAAMC,QAAA,GAAW7iB,QAAS,CAAA2iB,KAAA;EAC1B,MAAMrhB,IAAA,GAAOuhB,QAAW,GAAAljB,MAAA,CAAO2B,IAAI,CAACqhB,KAAA,IAASA,KAAK;EAClD,MAAMG,IAAA,GAAO9iB,QAAS,CAAAV,KAAA,IAClBujB,QACE,GAAAE,IAAA,IAAQziB,cAAe,CAAAhB,KAAK,CAACyjB,IAAA,CAAK,EAAEzjB,KAAK,CAACqjB,KAAK,CAACI,IAAK,EAAC,CACtD,GAAAA,IAAQ,IAAAzjB,KAAK,CAACyjB,IAAA,CAAK,GACrB,MAAMzjB,KAAK;EAEf,KAAK,MAAMyjB,IAAA,IAAQzhB,IAAM;IACvBshB,GAAG,CAACG,IAAA,CAAK,GAAGN,YAAA,CAAaK,IAAK,CAAAC,IAAA;EAChC;EACA,OAAOH,GAAA;AACT;AAEA;;;;;;;AAOC;AACM,SAASI,MAAOA,CAAA1jB,KAA4B,EAAE;EACnD,OAAOojB,iBAAA,CAAkBpjB,KAAO;IAAC0U,GAAK;IAAK1G,KAAO;IAAK2G,MAAQ;IAAK5G,IAAM;EAAG;AAC/E;AAEA;;;;;;AAMC;AACM,SAAS4V,aAAcA,CAAA3jB,KAA2B,EAAE;EACzD,OAAOojB,iBAAA,CAAkBpjB,KAAO,GAAC,WAAW,YAAY,cAAc,cAAc;AACtF;AAEA;;;;;;;AAOC;AACM,SAAS4jB,SAAUA,CAAA5jB,KAAqB,EAAa;EAC1D,MAAMgF,GAAA,GAAM0e,MAAO,CAAA1jB,KAAA;EAEnBgF,GAAA,CAAI8S,KAAK,GAAG9S,GAAA,CAAI+I,IAAI,GAAG/I,GAAA,CAAIgJ,KAAK;EAChChJ,GAAA,CAAIkZ,MAAM,GAAGlZ,GAAA,CAAI0P,GAAG,GAAG1P,GAAA,CAAI2P,MAAM;EAEjC,OAAO3P,GAAA;AACT;AAEA;;;;;;AAMC;AAEM,SAAS6e,OAAOxgB,OAA0B,EAAEygB,QAA4B,EAAE;EAC/EzgB,OAAA,GAAUA,OAAA,IAAW,EAAC;EACtBygB,QAAW,GAAAA,QAAA,IAAYzQ,QAAA,CAAS8G,IAAI;EAEpC,IAAIvU,IAAA,GAAO5E,cAAe,CAAAqC,OAAA,CAAQuC,IAAI,EAAEke,QAAA,CAASle,IAAI;EAErD,IAAI,OAAOA,IAAA,KAAS,QAAU;IAC5BA,IAAA,GAAOme,QAAA,CAASne,IAAM;;EAExB,IAAIyU,KAAA,GAAQrZ,cAAe,CAAAqC,OAAA,CAAQgX,KAAK,EAAEyJ,QAAA,CAASzJ,KAAK;EACxD,IAAIA,KAAA,IAAS,CAAC,CAAC,KAAKA,KAAI,EAAG6I,KAAK,CAACH,UAAa;IAC5C7e,OAAQ,CAAAC,IAAI,CAAC,oCAAoCkW,KAAQ;IACzDA,KAAQ,GAAApa,SAAA;;EAGV,MAAMka,IAAO;IACXC,MAAA,EAAQpZ,cAAe,CAAAqC,OAAA,CAAQ+W,MAAM,EAAE0J,QAAA,CAAS1J,MAAM;IACtDE,UAAA,EAAY0I,YAAA,CAAahiB,cAAe,CAAAqC,OAAA,CAAQiX,UAAU,EAAEwJ,QAAA,CAASxJ,UAAU,CAAG,EAAA1U,IAAA;IAClFA,IAAA;IACAyU,KAAA;IACAE,MAAA,EAAQvZ,cAAe,CAAAqC,OAAA,CAAQkX,MAAM,EAAEuJ,QAAA,CAASvJ,MAAM;IACtDoC,MAAQ;EACV;EAEAxC,IAAK,CAAAwC,MAAM,GAAGL,YAAa,CAAAnC,IAAA;EAC3B,OAAOA,IAAA;AACT;AAEA;;;;;;;;;;;AAWO,SAAS6J,QAAQC,MAAsB,EAAEnK,OAAgB,EAAErX,KAAc,EAAEyhB,IAA6B,EAAE;EAC/G,IAAIC,SAAA,GAAY,IAAI;EACpB,IAAIriB,CAAA,EAAWO,IAAc,EAAArC,KAAA;EAE7B,KAAK8B,CAAA,GAAI,GAAGO,IAAO,GAAA4hB,MAAA,CAAOhiB,MAAM,EAAEH,CAAA,GAAIO,IAAM,IAAEP,CAAG;IAC/C9B,KAAQ,GAAAikB,MAAM,CAACniB,CAAE;IACjB,IAAI9B,KAAA,KAAUC,SAAW;MACvB;;IAEF,IAAI6Z,OAAY,KAAA7Z,SAAA,IAAa,OAAOD,KAAA,KAAU,UAAY;MACxDA,KAAA,GAAQA,KAAM,CAAA8Z,OAAA;MACdqK,SAAA,GAAY,KAAK;;IAEnB,IAAI1hB,KAAA,KAAUxC,SAAa,IAAAC,OAAA,CAAQF,KAAQ;MACzCA,KAAA,GAAQA,KAAK,CAACyC,KAAQ,GAAAzC,KAAA,CAAMiC,MAAM,CAAC;MACnCkiB,SAAA,GAAY,KAAK;;IAEnB,IAAInkB,KAAA,KAAUC,SAAW;MACvB,IAAIikB,IAAA,IAAQ,CAACC,SAAW;QACtBD,IAAK,CAAAC,SAAS,GAAG,KAAK;;MAExB,OAAOnkB,KAAA;;EAEX;AACF;AAEA;;;;;;AAMO,SAASokB,SAAUA,CAAAC,MAAqC,EAAEnN,KAAsB,EAAEH,WAAoB,EAAE;EAC7G,MAAM;IAACtO,GAAA;IAAKC;EAAA,CAAI,GAAG2b,MAAA;EACnB,MAAMC,MAAA,GAASjjB,WAAY,CAAA6V,KAAA,EAAO,CAACxO,GAAA,GAAMD,GAAE,IAAK;EAChD,MAAM8b,QAAA,GAAWA,CAACvkB,KAAe,EAAAwkB,GAAA,KAAgBzN,WAAA,IAAe/W,KAAU,SAAI,CAAI,GAAAA,KAAA,GAAQwkB,GAAG;EAC7F,OAAO;IACL/b,GAAA,EAAK8b,QAAS,CAAA9b,GAAA,EAAK,CAACvC,IAAA,CAAKa,GAAG,CAACud,MAAA;IAC7B5b,GAAA,EAAK6b,QAAA,CAAS7b,GAAK,EAAA4b,MAAA;EACrB;AACF;AAUO,SAASG,cAAcC,aAAqB,EAAE5K,OAAe,EAAE;EACpE,OAAOzZ,MAAA,CAAO6P,MAAM,CAAC7P,MAAO,CAAAyC,MAAM,CAAC4hB,aAAgB,GAAA5K,OAAA;AACrD;;AC7LA;;;;;;;;;AASC;AACM,SAAS6K,eAIdA,CAAAC,MAAS,EACTC,QAAW,IAAC,GAAG,EACfC,UAAc,EACdhB,QAA4B,EAC5BiB,SAAA,GAAYA,CAAA,KAAMH,MAAM,CAAC,EAAE,EAC3B;EACA,MAAMI,eAAA,GAAkBF,UAAc,IAAAF,MAAA;EACtC,IAAI,OAAOd,QAAA,KAAa,WAAa;IACnCA,QAAA,GAAWmB,QAAA,CAAS,WAAa,EAAAL,MAAA;;EAEnC,MAAM5H,KAA6B;IACjC,CAAChV,MAAA,CAAOkd,WAAW,GAAG;IACtBC,UAAA,EAAY,IAAI;IAChBC,OAAS,EAAAR,MAAA;IACTS,WAAa,EAAAL,eAAA;IACbrR,SAAW,EAAAmQ,QAAA;IACXwB,UAAY,EAAAP,SAAA;IACZnJ,QAAU,EAAC5X,KAAA,IAAqB2gB,eAAgB,EAAC3gB,KAAA,EAAU,GAAA4gB,MAAA,CAAO,EAAEC,QAAA,EAAUG,eAAiB,EAAAlB,QAAA;EACjG;EACA,OAAO,IAAIyB,KAAA,CAAMvI,KAAO;IACtB;;AAEC;IACDwI,cAAeA,CAAA3iB,MAAM,EAAE4gB,IAAY,EAAE;MACnC,OAAO5gB,MAAM,CAAC4gB,IAAK;MACnB,OAAO5gB,MAAA,CAAO4iB,KAAK;MACnB,OAAOb,MAAM,CAAC,EAAE,CAACnB,IAAA,CAAK;MACtB,OAAO,IAAI;IACb;IAEA;;AAEC;IACDrO,GAAIA,CAAAvS,MAAM,EAAE4gB,IAAY,EAAE;MACxB,OAAOiC,OAAA,CAAQ7iB,MAAQ,EAAA4gB,IAAA,EACrB,MAAMkC,oBAAqB,CAAAlC,IAAA,EAAMoB,QAAA,EAAUD,MAAQ,EAAA/hB,MAAA;IACvD;IAEA;;;AAGC;IACD+iB,wBAAyBA,CAAA/iB,MAAM,EAAE4gB,IAAI,EAAE;MACrC,OAAOoC,OAAA,CAAQD,wBAAwB,CAAC/iB,MAAA,CAAOuiB,OAAO,CAAC,EAAE,EAAE3B,IAAA;IAC7D;IAEA;;AAEC;IACDqC,cAAiBA,CAAA;MACf,OAAOD,OAAQ,CAAAC,cAAc,CAAClB,MAAM,CAAC,CAAE;IACzC;IAEA;;AAEC;IACD9e,GAAIA,CAAAjD,MAAM,EAAE4gB,IAAY,EAAE;MACxB,OAAOsC,oBAAA,CAAqBljB,MAAQ,EAAA6T,QAAQ,CAAC+M,IAAA;IAC/C;IAEA;;;IAGAuC,QAAQnjB,MAAM,EAAE;MACd,OAAOkjB,oBAAqB,CAAAljB,MAAA;IAC9B;IAEA;;AAEC;IACD4J,IAAI5J,MAAM,EAAE4gB,IAAY,EAAEzjB,KAAK,EAAE;MAC/B,MAAMimB,OAAA,GAAUpjB,MAAA,CAAOqjB,QAAQ,KAAKrjB,MAAO,CAAAqjB,QAAQ,GAAGnB,SAAU;MAChEliB,MAAM,CAAC4gB,IAAA,CAAK,GAAGwC,OAAO,CAACxC,IAAK,IAAGzjB,KAAA;MAC/B,OAAO6C,MAAA,CAAO4iB,KAAK;MACnB,OAAO,IAAI;IACb;EACF;AACF;AAEA;;;;;;;;AAQO,SAASU,eAIdC,KAA0B,EAC1BtM,OAAkB,EAClBuM,QAA8B,EAC9BC,kBAAuC,EACvC;EACA,MAAMtJ,KAA4B;IAChCmI,UAAA,EAAY,KAAK;IACjBoB,MAAQ,EAAAH,KAAA;IACRI,QAAU,EAAA1M,OAAA;IACV2M,SAAW,EAAAJ,QAAA;IACXK,MAAA,EAAQ,IAAIha,GAAA;IACZ8M,YAAA,EAAcA,YAAA,CAAa4M,KAAO,EAAAE,kBAAA;IAClCK,UAAA,EAAajM,GAAA,IAAmByL,cAAe,CAAAC,KAAA,EAAO1L,GAAA,EAAK2L,QAAU,EAAAC,kBAAA;IACrE1K,QAAU,EAAC5X,KAAA,IAAqBmiB,cAAe,CAAAC,KAAA,CAAMxK,QAAQ,CAAC5X,KAAA,GAAQ8V,OAAA,EAASuM,QAAU,EAAAC,kBAAA;EAC3F;EACA,OAAO,IAAIf,KAAA,CAAMvI,KAAO;IACtB;;AAEC;IACDwI,cAAeA,CAAA3iB,MAAM,EAAE4gB,IAAI,EAAE;MAC3B,OAAO5gB,MAAM,CAAC4gB,IAAK;MACnB,OAAO2C,KAAK,CAAC3C,IAAK;MAClB,OAAO,IAAI;IACb;IAEA;;AAEC;IACDrO,IAAIvS,MAAM,EAAE4gB,IAAY,EAAEmD,QAAQ,EAAE;MAClC,OAAOlB,OAAA,CAAQ7iB,MAAQ,EAAA4gB,IAAA,EACrB,MAAMoD,mBAAA,CAAoBhkB,MAAA,EAAQ4gB,IAAM,EAAAmD,QAAA;IAC5C;IAEA;;;AAGC;IACDhB,wBAAyBA,CAAA/iB,MAAM,EAAE4gB,IAAI,EAAE;MACrC,OAAO5gB,MAAA,CAAO2W,YAAY,CAACsN,OAAO,GAC9BjB,OAAQ,CAAA/f,GAAG,CAACsgB,KAAA,EAAO3C,IAAQ;QAAC3X,UAAA,EAAY,IAAI;QAAED,YAAA,EAAc;MAAI,IAAI5L,SAAS,GAC7E4lB,OAAA,CAAQD,wBAAwB,CAACQ,KAAA,EAAO3C,IAAK;IACnD;IAEA;;AAEC;IACDqC,cAAiBA,CAAA;MACf,OAAOD,OAAA,CAAQC,cAAc,CAACM,KAAA;IAChC;IAEA;;AAEC;IACDtgB,GAAIA,CAAAjD,MAAM,EAAE4gB,IAAI,EAAE;MAChB,OAAOoC,OAAA,CAAQ/f,GAAG,CAACsgB,KAAO,EAAA3C,IAAA;IAC5B;IAEA;;AAEC;IACDuC,OAAUA,CAAA;MACR,OAAOH,OAAA,CAAQG,OAAO,CAACI,KAAA;IACzB;IAEA;;AAEC;IACD3Z,IAAI5J,MAAM,EAAE4gB,IAAI,EAAEzjB,KAAK,EAAE;MACvBomB,KAAK,CAAC3C,IAAA,CAAK,GAAGzjB,KAAA;MACd,OAAO6C,MAAM,CAAC4gB,IAAK;MACnB,OAAO,IAAI;IACb;EACF;AACF;AAEA;;AAEC;AACM,SAASjK,YACdA,CAAA4M,KAAoB,EACpB/S,QAA+B;EAAC0T,UAAA,EAAY,IAAI;EAAEC,SAAA,EAAW;AAAI,CAAC,EACtD;EACZ,MAAM;IAACnT,WAAc,GAAAR,QAAA,CAAS0T,UAAU;IAAEnT,UAAa,GAAAP,QAAA,CAAS2T,SAAS;IAAEC,QAAW,GAAA5T,QAAA,CAASyT;EAAO,CAAC,GAAGV,KAAA;EAC1G,OAAO;IACLU,OAAS,EAAAG,QAAA;IACTF,UAAY,EAAAlT,WAAA;IACZmT,SAAW,EAAApT,UAAA;IACXsT,YAAA,EAAc1hB,UAAW,CAAAqO,WAAA,IAAeA,WAAc,SAAMA,WAAW;IACvEsT,WAAA,EAAa3hB,UAAW,CAAAoO,UAAA,IAAcA,UAAa,SAAMA;EAC3D;AACF;AAEA,MAAMwT,OAAA,GAAUA,CAACC,MAAgB,EAAAvT,IAAA,KAAiBuT,MAAA,GAASA,MAAS,GAAAliB,WAAA,CAAY2O,IAAA,IAAQA,IAAI;AAC5F,MAAMwT,gBAAA,GAAmBA,CAAC7D,IAAA,EAAczjB,KAAA,KAAmBU,QAAS,CAAAV,KAAA,KAAUyjB,IAAA,KAAS,UACpF,KAAApjB,MAAO,CAAAylB,cAAc,CAAC9lB,KAAW,UAAI,IAAIA,KAAM,CAAAuZ,WAAW,KAAKlZ,MAAK;AAEvE,SAASqlB,QACP7iB,MAAiB,EACjB4gB,IAAY,EACZO,OAAsB,EACtB;EACA,IAAI3jB,MAAA,CAAOC,SAAS,CAACwD,cAAc,CAACtD,IAAI,CAACqC,MAAA,EAAQ4gB,IAAS,KAAAA,IAAA,KAAS,aAAe;IAChF,OAAO5gB,MAAM,CAAC4gB,IAAK;;EAGrB,MAAMzjB,KAAQ,GAAAgkB,OAAA;;EAEdnhB,MAAM,CAAC4gB,IAAA,CAAK,GAAGzjB,KAAA;EACf,OAAOA,KAAA;AACT;AAEA,SAAS6mB,oBACPhkB,MAAoB,EACpB4gB,IAAY,EACZmD,QAAmB,EACnB;EACA,MAAM;IAACL,MAAM;IAAEC,QAAQ;IAAEC,SAAA;IAAWjN,YAAA,EAAcN;EAAW,CAAC,GAAGrW,MAAA;EACjE,IAAI7C,KAAQ,GAAAumB,MAAM,CAAC9C,IAAA,CAAK;;EAGxB,IAAIje,UAAW,CAAAxF,KAAA,KAAUkZ,WAAY,CAAAgO,YAAY,CAACzD,IAAO;IACvDzjB,KAAQ,GAAAunB,kBAAA,CAAmB9D,IAAM,EAAAzjB,KAAA,EAAO6C,MAAQ,EAAA+jB,QAAA;;EAElD,IAAI1mB,OAAQ,CAAAF,KAAA,KAAUA,KAAM,CAAAiC,MAAM,EAAE;IAClCjC,KAAA,GAAQwnB,aAAc,CAAA/D,IAAA,EAAMzjB,KAAO,EAAA6C,MAAA,EAAQqW,WAAA,CAAYiO,WAAW;;EAEpE,IAAIG,gBAAA,CAAiB7D,IAAA,EAAMzjB,KAAQ;;IAEjCA,KAAA,GAAQmmB,cAAA,CAAenmB,KAAO,EAAAwmB,QAAA,EAAUC,SAAA,IAAaA,SAAS,CAAChD,IAAA,CAAK,EAAEvK,WAAA;;EAExE,OAAOlZ,KAAA;AACT;AAEA,SAASunB,mBACP9D,IAAY,EACZgE,QAAqD,EACrD5kB,MAAoB,EACpB+jB,QAAmB,EACnB;EACA,MAAM;IAACL,MAAA;IAAQC,QAAA;IAAUC,SAAS;IAAEC;EAAM,CAAC,GAAG7jB,MAAA;EAC9C,IAAI6jB,MAAA,CAAO5gB,GAAG,CAAC2d,IAAO;IACpB,MAAM,IAAIiE,KAAM,0BAAyBvnB,KAAM,CAAAwM,IAAI,CAAC+Z,MAAA,EAAQiB,IAAI,CAAC,IAAQ,WAAOlE,IAAM;;EAExFiD,MAAA,CAAOlC,GAAG,CAACf,IAAA;EACX,IAAIzjB,KAAA,GAAQynB,QAAS,CAAAjB,QAAA,EAAUC,SAAa,IAAAG,QAAA;EAC5CF,MAAA,CAAOkB,MAAM,CAACnE,IAAA;EACd,IAAI6D,gBAAA,CAAiB7D,IAAA,EAAMzjB,KAAQ;;IAEjCA,KAAA,GAAQ6nB,iBAAkB,CAAAtB,MAAA,CAAOnB,OAAO,EAAEmB,MAAA,EAAQ9C,IAAM,EAAAzjB,KAAA;;EAE1D,OAAOA,KAAA;AACT;AAEA,SAASwnB,cACP/D,IAAY,EACZzjB,KAAgB,EAChB6C,MAAoB,EACpBskB,WAAqC,EACrC;EACA,MAAM;IAACZ,MAAM;IAAEC,QAAQ;IAAEC,SAAA;IAAWjN,YAAA,EAAcN;EAAW,CAAC,GAAGrW,MAAA;EAEjE,IAAI,OAAO2jB,QAAS,CAAA/jB,KAAK,KAAK,eAAe0kB,WAAA,CAAY1D,IAAO;IAC9D,OAAOzjB,KAAK,CAACwmB,QAAA,CAAS/jB,KAAK,GAAGzC,KAAA,CAAMiC,MAAM,CAAC;EAC7C,OAAO,IAAIvB,QAAA,CAASV,KAAK,CAAC,EAAE,CAAG;;IAE7B,MAAM8nB,GAAM,GAAA9nB,KAAA;IACZ,MAAM4kB,MAAA,GAAS2B,MAAA,CAAOnB,OAAO,CAAC2C,MAAM,CAAC5d,CAAA,IAAKA,CAAM,KAAA2d,GAAA;IAChD9nB,KAAA,GAAQ,EAAE;IACV,KAAK,MAAM6F,IAAA,IAAQiiB,GAAK;MACtB,MAAM5iB,QAAW,GAAA2iB,iBAAA,CAAkBjD,MAAQ,EAAA2B,MAAA,EAAQ9C,IAAM,EAAA5d,IAAA;MACzD7F,KAAM,CAAA8E,IAAI,CAACqhB,cAAe,CAAAjhB,QAAA,EAAUshB,QAAA,EAAUC,SAAa,IAAAA,SAAS,CAAChD,IAAA,CAAK,EAAEvK,WAAA;IAC9E;;EAEF,OAAOlZ,KAAA;AACT;AAEA,SAASgoB,gBACPlE,QAA8F,EAC9FL,IAAuB,EACvBzjB,KAAc,EACd;EACA,OAAOwF,UAAW,CAAAse,QAAA,IAAYA,QAAS,CAAAL,IAAA,EAAMzjB,KAAA,IAAS8jB,QAAQ;AAChE;AAEA,MAAMmE,QAAW,GAAAA,CAAC/kB,GAAwB,EAAAglB,MAAA,KAAsBhlB,GAAA,KAAQ,IAAI,GAAGglB,MAC3E,UAAOhlB,GAAQ,gBAAW+B,gBAAiB,CAAAijB,MAAA,EAAQhlB,GAAA,IAAOjD,SAAS;AAEvE,SAASkoB,UACP1b,GAAmB,EACnB2b,YAAyB,EACzBllB,GAAsB,EACtBmlB,cAAiC,EACjCroB,KAAc,EACd;EACA,KAAK,MAAMkoB,MAAA,IAAUE,YAAc;IACjC,MAAMpkB,KAAA,GAAQikB,QAAA,CAAS/kB,GAAK,EAAAglB,MAAA;IAC5B,IAAIlkB,KAAO;MACTyI,GAAA,CAAI+X,GAAG,CAACxgB,KAAA;MACR,MAAM8f,QAAW,GAAAkE,eAAA,CAAgBhkB,KAAM,CAAA2P,SAAS,EAAEzQ,GAAK,EAAAlD,KAAA;MACvD,IAAI,OAAO8jB,QAAa,oBAAeA,QAAa,KAAA5gB,GAAA,IAAO4gB,QAAA,KAAauE,cAAgB;;;QAGtF,OAAOvE,QAAA;;KAEJ,UAAI9f,KAAA,KAAU,KAAK,IAAI,OAAOqkB,cAAmB,oBAAenlB,GAAA,KAAQmlB,cAAgB;;;MAG7F,OAAO,IAAI;;EAEf;EACA,OAAO,KAAK;AACd;AAEA,SAASR,kBACPO,YAAyB,EACzBljB,QAAuB,EACvBue,IAAuB,EACvBzjB,KAAc,EACd;EACA,MAAM8kB,UAAA,GAAa5f,QAAA,CAASmgB,WAAW;EACvC,MAAMvB,QAAW,GAAAkE,eAAA,CAAgB9iB,QAAS,CAAAyO,SAAS,EAAE8P,IAAM,EAAAzjB,KAAA;EAC3D,MAAMsoB,SAAY,IAAI,GAAAF,YAAA,EAAiB,GAAAtD,UAAA,CAAW;EAClD,MAAMrY,GAAA,GAAM,IAAIC,GAAA;EAChBD,GAAA,CAAI+X,GAAG,CAACxkB,KAAA;EACR,IAAIkD,GAAA,GAAMqlB,gBAAiB,CAAA9b,GAAA,EAAK6b,SAAW,EAAA7E,IAAA,EAAMK,QAAA,IAAYL,IAAM,EAAAzjB,KAAA;EACnE,IAAIkD,GAAA,KAAQ,IAAI,EAAE;IAChB,OAAO,KAAK;;EAEd,IAAI,OAAO4gB,QAAA,KAAa,WAAe,IAAAA,QAAA,KAAaL,IAAM;IACxDvgB,GAAA,GAAMqlB,gBAAiB,CAAA9b,GAAA,EAAK6b,SAAW,EAAAxE,QAAA,EAAU5gB,GAAK,EAAAlD,KAAA;IACtD,IAAIkD,GAAA,KAAQ,IAAI,EAAE;MAChB,OAAO,KAAK;;;EAGhB,OAAOyhB,eAAgB,CAAAxkB,KAAA,CAAMwM,IAAI,CAACF,GAAM,IAAC,GAAG,EAAEqY,UAAY,EAAAhB,QAAA,EACxD,MAAM0E,YAAA,CAAatjB,QAAA,EAAUue,IAAgB,EAAAzjB,KAAA;AACjD;AAEA,SAASuoB,iBACP9b,GAAmB,EACnB6b,SAAsB,EACtBplB,GAAsB,EACtB4gB,QAA2B,EAC3Bje,IAAa,EACb;EACA,OAAO3C,GAAK;IACVA,GAAA,GAAMilB,SAAU,CAAA1b,GAAA,EAAK6b,SAAW,EAAAplB,GAAA,EAAK4gB,QAAU,EAAAje,IAAA;EACjD;EACA,OAAO3C,GAAA;AACT;AAEA,SAASslB,aACPtjB,QAAuB,EACvBue,IAAY,EACZzjB,KAAc,EACd;EACA,MAAMkoB,MAAA,GAAShjB,QAAA,CAASogB,UAAU;EAClC,IAAI,EAAE7B,IAAQ,IAAAyE,MAAK,CAAI;IACrBA,MAAM,CAACzE,IAAK,IAAG,EAAC;;EAElB,MAAM5gB,MAAA,GAASqlB,MAAM,CAACzE,IAAK;EAC3B,IAAIvjB,OAAA,CAAQ2C,MAAW,KAAAnC,QAAA,CAASV,KAAQ;;IAEtC,OAAOA,KAAA;;EAET,OAAO6C,MAAA,IAAU,EAAC;AACpB;AAEA,SAAS8iB,qBACPlC,IAAY,EACZoB,QAAkB,EAClBD,MAAmB,EACnBwB,KAAoB,EACpB;EACA,IAAIpmB,KAAA;EACJ,KAAK,MAAMqnB,MAAA,IAAUxC,QAAU;IAC7B7kB,KAAQ,GAAAilB,QAAA,CAASmC,OAAQ,CAAAC,MAAA,EAAQ5D,IAAO,GAAAmB,MAAA;IACxC,IAAI,OAAO5kB,KAAA,KAAU,WAAa;MAChC,OAAOsnB,gBAAA,CAAiB7D,IAAA,EAAMzjB,KAC1B,IAAA6nB,iBAAA,CAAkBjD,MAAA,EAAQwB,KAAO,EAAA3C,IAAA,EAAMzjB,KAAA,IACvCA,KAAK;;EAEb;AACF;AAEA,SAASilB,QAASA,CAAA/hB,GAAW,EAAE0hB,MAAmB,EAAE;EAClD,KAAK,MAAM5gB,KAAA,IAAS4gB,MAAQ;IAC1B,IAAI,CAAC5gB,KAAO;MACV;;IAEF,MAAMhE,KAAA,GAAQgE,KAAK,CAACd,GAAI;IACxB,IAAI,OAAOlD,KAAA,KAAU,WAAa;MAChC,OAAOA,KAAA;;EAEX;AACF;AAEA,SAAS+lB,qBAAqBljB,MAAqB,EAAE;EACnD,IAAIb,IAAA,GAAOa,MAAA,CAAO4iB,KAAK;EACvB,IAAI,CAACzjB,IAAM;IACTA,IAAA,GAAOa,MAAO,CAAA4iB,KAAK,GAAGgD,wBAAA,CAAyB5lB,MAAA,CAAOuiB,OAAO;;EAE/D,OAAOpjB,IAAA;AACT;AAEA,SAASymB,yBAAyB7D,MAAmB,EAAE;EACrD,MAAMnY,GAAA,GAAM,IAAIC,GAAA;EAChB,KAAK,MAAM1I,KAAA,IAAS4gB,MAAQ;IAC1B,KAAK,MAAM1hB,GAAA,IAAO7C,MAAO,CAAA2B,IAAI,CAACgC,KAAO,EAAA+jB,MAAM,CAAC/kB,CAAK,KAACA,CAAE,CAAAgW,UAAU,CAAC,GAAO;MACpEvM,GAAA,CAAI+X,GAAG,CAACthB,GAAA;IACV;EACF;EACA,OAAO/C,KAAA,CAAMwM,IAAI,CAACF,GAAA;AACpB;AAEO,SAASic,4BACdta,IAAmC,EACnCoO,IAAiB,EACjBxS,KAAa,EACbwE,KAAa,EACb;EACA,MAAM;IAACE;EAAM,CAAC,GAAGN,IAAA;EACjB,MAAM;IAAClL,GAAM;EAAA,CAAI,GAAG,IAAI,CAACylB,QAAQ;EACjC,MAAMC,MAAA,GAAS,IAAIzoB,KAAoB,CAAAqO,KAAA;EACvC,IAAI1M,CAAA,EAAWO,IAAA,EAAcI,KAAe,EAAAoD,IAAA;EAE5C,KAAK/D,CAAA,GAAI,GAAGO,IAAO,GAAAmM,KAAK,EAAE1M,CAAI,GAAAO,IAAA,EAAM,EAAEP,CAAG;IACvCW,KAAA,GAAQX,CAAI,GAAAkI,KAAA;IACZnE,IAAO,GAAA2W,IAAI,CAAC/Z,KAAM;IAClBmmB,MAAM,CAAC9mB,CAAA,CAAE,GAAG;MACV+mB,CAAA,EAAGna,MAAO,CAAAoa,KAAK,CAAC7jB,gBAAA,CAAiBY,IAAA,EAAM3C,GAAM,GAAAT,KAAA;IAC/C;EACF;EACA,OAAOmmB,MAAA;AACT;AClcA,MAAMG,OAAA,GAAUnoB,MAAO,CAAAmoB,OAAO,IAAI;AAGlC,MAAMC,QAAA,GAAWA,CAAC3a,MAAA,EAAuBvM,CAAmC,KAAAA,CAAA,GAAIuM,MAAA,CAAOpM,MAAM,IAAI,CAACoM,MAAM,CAACvM,CAAE,EAACmnB,IAAI,IAAI5a,MAAM,CAACvM,CAAE;AAC7H,MAAMonB,YAAA,GAAgBrO,SAAA,IAAyBA,SAAc,WAAM,MAAM,GAAG;AAErE,SAASsO,YACdC,UAAuB,EACvBC,WAAwB,EACxBC,UAAuB,EACvBlZ,CAAS,EAIP;;;;EAMF,MAAMnM,QAAW,GAAAmlB,UAAA,CAAWH,IAAI,GAAGI,WAAA,GAAcD,UAAU;EAC3D,MAAMzlB,OAAU,GAAA0lB,WAAA;EAChB,MAAME,IAAO,GAAAD,UAAA,CAAWL,IAAI,GAAGI,WAAA,GAAcC,UAAU;EACvD,MAAME,GAAA,GAAM9f,qBAAA,CAAsB/F,OAAS,EAAAM,QAAA;EAC3C,MAAMwlB,GAAA,GAAM/f,qBAAA,CAAsB6f,IAAM,EAAA5lB,OAAA;EAExC,IAAI+lB,GAAM,GAAAF,GAAA,IAAOA,GAAA,GAAMC,GAAE;EACzB,IAAIE,GAAM,GAAAF,GAAA,IAAOD,GAAA,GAAMC,GAAE;;EAGzBC,GAAM,GAAAvhB,KAAA,CAAMuhB,GAAO,QAAIA,GAAG;EAC1BC,GAAM,GAAAxhB,KAAA,CAAMwhB,GAAO,QAAIA,GAAG;EAE1B,MAAMC,EAAA,GAAKxZ,CAAI,GAAAsZ,GAAA;EACf,MAAMG,EAAA,GAAKzZ,CAAI,GAAAuZ,GAAA;EAEf,OAAO;IACL1lB,QAAU;MACRK,CAAG,EAAAX,OAAA,CAAQW,CAAC,GAAGslB,EAAM,IAAAL,IAAA,CAAKjlB,CAAC,GAAGL,QAAS,CAAAK,CAAC,CAAD;MACvCE,CAAG,EAAAb,OAAA,CAAQa,CAAC,GAAGolB,EAAM,IAAAL,IAAA,CAAK/kB,CAAC,GAAGP,QAAS,CAAAO,CAAC;IAC1C;IACA+kB,IAAM;MACJjlB,CAAG,EAAAX,OAAA,CAAQW,CAAC,GAAGulB,EAAM,IAAAN,IAAA,CAAKjlB,CAAC,GAAGL,QAAS,CAAAK,CAAC,CAAD;MACvCE,CAAG,EAAAb,OAAA,CAAQa,CAAC,GAAGqlB,EAAM,IAAAN,IAAA,CAAK/kB,CAAC,GAAGP,QAAS,CAAAO,CAAC;IAC1C;EACF;AACF;AAEA;;AAEC;AACD,SAASslB,cAAeA,CAAAzb,MAAqB,EAAE0b,MAAgB,EAAEC,EAAY,EAAE;EAC7E,MAAMC,SAAA,GAAY5b,MAAA,CAAOpM,MAAM;EAE/B,IAAIioB,MAAA,EAAgBC,KAAe,EAAAC,IAAA,EAAcC,gBAA0B,EAAAC,YAAA;EAC3E,IAAIC,UAAA,GAAavB,QAAA,CAAS3a,MAAQ;EAClC,KAAK,IAAIvM,CAAI,MAAGA,CAAA,GAAImoB,SAAY,MAAG,EAAEnoB,CAAG;IACtCwoB,YAAe,GAAAC,UAAA;IACfA,UAAa,GAAAvB,QAAA,CAAS3a,MAAA,EAAQvM,CAAI;IAClC,IAAI,CAACwoB,YAAgB,KAACC,UAAY;MAChC;;IAGF,IAAI1jB,YAAA,CAAakjB,MAAM,CAACjoB,CAAE,GAAE,GAAGinB,OAAU;MACvCiB,EAAE,CAACloB,CAAE,IAAGkoB,EAAE,CAACloB,CAAA,GAAI,EAAE,GAAG;MACpB;;IAGFooB,MAAA,GAASF,EAAE,CAACloB,CAAA,CAAE,GAAGioB,MAAM,CAACjoB,CAAE;IAC1BqoB,KAAA,GAAQH,EAAE,CAACloB,CAAA,GAAI,EAAE,GAAGioB,MAAM,CAACjoB,CAAE;IAC7BuoB,gBAAmB,GAAAnkB,IAAA,CAAKmB,GAAG,CAAC6iB,MAAA,EAAQ,KAAKhkB,IAAK,CAAAmB,GAAG,CAAC8iB,KAAO;IACzD,IAAIE,gBAAA,IAAoB,CAAG;MACzB;;IAGFD,IAAO,OAAIlkB,IAAK,CAAAyB,IAAI,CAAC0iB,gBAAA;IACrBL,EAAE,CAACloB,CAAE,IAAGooB,MAAA,GAASE,IAAO,GAAAL,MAAM,CAACjoB,CAAE;IACjCkoB,EAAE,CAACloB,CAAA,GAAI,CAAE,IAAGqoB,KAAA,GAAQC,IAAO,GAAAL,MAAM,CAACjoB,CAAE;EACtC;AACF;AAEA,SAAS0oB,gBAAgBnc,MAAqB,EAAE2b,EAAY,EAAEnP,SAAA,GAAuB,GAAG,EAAE;EACxF,MAAM4P,SAAA,GAAYvB,YAAa,CAAArO,SAAA;EAC/B,MAAMoP,SAAA,GAAY5b,MAAA,CAAOpM,MAAM;EAC/B,IAAI+T,KAAA,EAAe0U,WAAkC,EAAAJ,YAAA;EACrD,IAAIC,UAAA,GAAavB,QAAA,CAAS3a,MAAQ;EAElC,KAAK,IAAIvM,CAAI,MAAGA,CAAI,GAAAmoB,SAAA,EAAW,EAAEnoB,CAAG;IAClC4oB,WAAc,GAAAJ,YAAA;IACdA,YAAe,GAAAC,UAAA;IACfA,UAAa,GAAAvB,QAAA,CAAS3a,MAAA,EAAQvM,CAAI;IAClC,IAAI,CAACwoB,YAAc;MACjB;;IAGF,MAAMK,MAAA,GAASL,YAAY,CAACzP,SAAU;IACtC,MAAM+P,MAAA,GAASN,YAAY,CAACG,SAAU;IACtC,IAAIC,WAAa;MACf1U,KAAA,GAAQ,CAAC2U,MAAA,GAASD,WAAW,CAAC7P,SAAA,CAAU,IAAI;MAC5CyP,YAAY,CAAC,MAAMzP,SAAA,EAAW,CAAC,GAAG8P,MAAS,GAAA3U,KAAA;MAC3CsU,YAAY,CAAC,MAAMG,SAAU,EAAC,CAAC,GAAGG,MAAS,GAAA5U,KAAA,GAAQgU,EAAE,CAACloB,CAAE;;IAE1D,IAAIyoB,UAAY;MACdvU,KAAA,GAAQ,CAACuU,UAAU,CAAC1P,SAAU,IAAG8P,MAAK,IAAK;MAC3CL,YAAY,CAAC,MAAMzP,SAAA,EAAW,CAAC,GAAG8P,MAAS,GAAA3U,KAAA;MAC3CsU,YAAY,CAAC,MAAMG,SAAU,EAAC,CAAC,GAAGG,MAAS,GAAA5U,KAAA,GAAQgU,EAAE,CAACloB,CAAE;;EAE5D;AACF;AAEA;;;;;AAKC;AACM,SAAS+oB,oBAAoBxc,MAAqB,EAAEwM,SAAA,GAAuB,GAAG,EAAE;EACrF,MAAM4P,SAAA,GAAYvB,YAAa,CAAArO,SAAA;EAC/B,MAAMoP,SAAA,GAAY5b,MAAA,CAAOpM,MAAM;EAC/B,MAAM8nB,MAAmB,GAAA5pB,KAAA,CAAM8pB,SAAW,EAAAvK,IAAI,CAAC;EAC/C,MAAMsK,EAAA,GAAe7pB,KAAM,CAAA8pB,SAAA;;EAG3B,IAAInoB,CAAA,EAAG4oB,WAAkC,EAAAJ,YAAA;EACzC,IAAIC,UAAA,GAAavB,QAAA,CAAS3a,MAAQ;EAElC,KAAKvM,CAAI,MAAGA,CAAI,GAAAmoB,SAAA,EAAW,EAAEnoB,CAAG;IAC9B4oB,WAAc,GAAAJ,YAAA;IACdA,YAAe,GAAAC,UAAA;IACfA,UAAa,GAAAvB,QAAA,CAAS3a,MAAA,EAAQvM,CAAI;IAClC,IAAI,CAACwoB,YAAc;MACjB;;IAGF,IAAIC,UAAY;MACd,MAAMO,UAAA,GAAaP,UAAU,CAAC1P,SAAA,CAAU,GAAGyP,YAAY,CAACzP,SAAU;;MAGlEkP,MAAM,CAACjoB,CAAE,IAAGgpB,UAAe,SAAI,CAACP,UAAU,CAACE,SAAA,CAAU,GAAGH,YAAY,CAACG,SAAA,CAAU,IAAIK,UAAA,GAAa,CAAC;;IAEnGd,EAAE,CAACloB,CAAE,IAAG,CAAC4oB,WAAc,GAAAX,MAAM,CAACjoB,CAAE,IAC5B,CAACyoB,UAAA,GAAaR,MAAM,CAACjoB,CAAA,GAAI,EAAE,GACxB8E,IAAA,CAAKmjB,MAAM,CAACjoB,CAAI,KAAE,MAAM8E,IAAK,CAAAmjB,MAAM,CAACjoB,CAAE,KAAK,IAC1C,CAACioB,MAAM,CAACjoB,CAAA,GAAI,EAAE,GAAGioB,MAAM,CAACjoB,CAAE,CAAD,IAAK,CAAC;EACzC;EAEAgoB,cAAA,CAAezb,MAAA,EAAQ0b,MAAQ,EAAAC,EAAA;EAE/BQ,eAAA,CAAgBnc,MAAA,EAAQ2b,EAAI,EAAAnP,SAAA;AAC9B;AAEA,SAASkQ,gBAAgBC,EAAU,EAAEviB,GAAW,EAAEC,GAAW,EAAE;EAC7D,OAAOxC,IAAA,CAAKwC,GAAG,CAACxC,IAAA,CAAKuC,GAAG,CAACuiB,EAAA,EAAItiB,GAAM,GAAAD,GAAA;AACrC;AAEA,SAASwiB,eAAgBA,CAAA5c,MAAqB,EAAEyR,IAAe,EAAE;EAC/D,IAAIhe,CAAA,EAAGO,IAAM,EAAAiN,KAAA,EAAO4b,MAAQ,EAAAC,UAAA;EAC5B,IAAIC,UAAa,GAAAvL,cAAA,CAAexR,MAAM,CAAC,EAAE,EAAEyR,IAAA;EAC3C,KAAKhe,CAAA,GAAI,GAAGO,IAAO,GAAAgM,MAAA,CAAOpM,MAAM,EAAEH,CAAA,GAAIO,IAAM,IAAEP,CAAG;IAC/CqpB,UAAa,GAAAD,MAAA;IACbA,MAAS,GAAAE,UAAA;IACTA,UAAa,GAAAtpB,CAAA,GAAIO,IAAA,GAAO,CAAK,IAAAwd,cAAA,CAAexR,MAAM,CAACvM,CAAA,GAAI,EAAE,EAAEge,IAAA;IAC3D,IAAI,CAACoL,MAAQ;MACX;;IAEF5b,KAAQ,GAAAjB,MAAM,CAACvM,CAAE;IACjB,IAAIqpB,UAAY;MACd7b,KAAM,CAAAiR,IAAI,GAAGwK,eAAA,CAAgBzb,KAAM,CAAAiR,IAAI,EAAET,IAAK,CAAA/R,IAAI,EAAE+R,IAAA,CAAK9R,KAAK;MAC9DsB,KAAM,CAAAmR,IAAI,GAAGsK,eAAA,CAAgBzb,KAAM,CAAAmR,IAAI,EAAEX,IAAK,CAAApL,GAAG,EAAEoL,IAAA,CAAKnL,MAAM;;IAEhE,IAAIyW,UAAY;MACd9b,KAAM,CAAAkR,IAAI,GAAGuK,eAAA,CAAgBzb,KAAM,CAAAkR,IAAI,EAAEV,IAAK,CAAA/R,IAAI,EAAE+R,IAAA,CAAK9R,KAAK;MAC9DsB,KAAM,CAAAoR,IAAI,GAAGqK,eAAA,CAAgBzb,KAAM,CAAAoR,IAAI,EAAEZ,IAAK,CAAApL,GAAG,EAAEoL,IAAA,CAAKnL,MAAM;;EAElE;AACF;AAEA;;AAEC;AACM,SAAS0W,0BACdA,CAAAhd,MAAqB,EACrBhL,OAAO,EACPyc,IAAe,EACftM,IAAa,EACbqH,SAAoB,EACpB;EACA,IAAI/Y,CAAA,EAAWO,IAAA,EAAciN,KAAoB,EAAAgc,aAAA;;EAGjD,IAAIjoB,OAAA,CAAQwL,QAAQ,EAAE;IACpBR,MAAA,GAASA,MAAA,CAAO0Z,MAAM,CAAEiD,EAAO,KAACA,EAAA,CAAG/B,IAAI;;EAGzC,IAAI5lB,OAAA,CAAQkoB,sBAAsB,KAAK,UAAY;IACjDV,mBAAA,CAAoBxc,MAAQ,EAAAwM,SAAA;GACvB;IACL,IAAI2Q,IAAA,GAAOhY,IAAO,GAAAnF,MAAM,CAACA,MAAA,CAAOpM,MAAM,GAAG,CAAE,IAAGoM,MAAM,CAAC,CAAE;IACvD,KAAKvM,CAAA,GAAI,GAAGO,IAAO,GAAAgM,MAAA,CAAOpM,MAAM,EAAEH,CAAA,GAAIO,IAAM,IAAEP,CAAG;MAC/CwN,KAAQ,GAAAjB,MAAM,CAACvM,CAAE;MACjBwpB,aAAgB,GAAAnC,WAAA,CACdqC,IAAA,EACAlc,KACA,EAAAjB,MAAM,CAACnI,IAAK,CAAAuC,GAAG,CAAC3G,CAAI,MAAGO,IAAA,IAAQmR,IAAA,GAAO,IAAI,MAAMnR,IAAK,GACrDgB,OAAA,CAAQooB,OAAO;MAEjBnc,KAAA,CAAMiR,IAAI,GAAG+K,aAAc,CAAArnB,QAAQ,CAACK,CAAC;MACrCgL,KAAA,CAAMmR,IAAI,GAAG6K,aAAc,CAAArnB,QAAQ,CAACO,CAAC;MACrC8K,KAAA,CAAMkR,IAAI,GAAG8K,aAAc,CAAA/B,IAAI,CAACjlB,CAAC;MACjCgL,KAAA,CAAMoR,IAAI,GAAG4K,aAAc,CAAA/B,IAAI,CAAC/kB,CAAC;MACjCgnB,IAAO,GAAAlc,KAAA;IACT;;EAGF,IAAIjM,OAAA,CAAQ4nB,eAAe,EAAE;IAC3BA,eAAA,CAAgB5c,MAAQ,EAAAyR,IAAA;;AAE5B;;ACzNA;;;AAGO,SAAS4L,eAA2BA,CAAA;EACzC,OAAO,OAAOze,MAAA,KAAW,WAAe,WAAO0e,QAAa;AAC9D;AAEA;;AAEC;AACM,SAASC,cAAeA,CAAAC,OAA0B,EAAqB;EAC5E,IAAI3D,MAAA,GAAS2D,OAAA,CAAQC,UAAU;EAC/B,IAAI5D,MAAU,IAAAA,MAAA,CAAO3nB,QAAQ,OAAO,qBAAuB;IACzD2nB,MAAS,GAACA,MAAA,CAAsB6D,IAAI;;EAEtC,OAAO7D,MAAA;AACT;AAEA;;;AAGC;AAED,SAAS8D,aAAcA,CAAAC,UAA2B,EAAE7S,IAAiB,EAAE8S,cAAsB,EAAE;EAC7F,IAAIC,aAAA;EACJ,IAAI,OAAOF,UAAA,KAAe,QAAU;IAClCE,aAAA,GAAgBpI,QAAA,CAASkI,UAAY;IAErC,IAAIA,UAAW,CAAA9oB,OAAO,CAAC,SAAS,CAAC,CAAG;;MAElCgpB,aAAA,GAAgBA,aAAiB,SAAO/S,IAAK,CAAA0S,UAAU,CAACI,cAAe;;GAEpE;IACLC,aAAgB,GAAAF,UAAA;;EAGlB,OAAOE,aAAA;AACT;AAEA,MAAMC,gBAAA,GAAoBC,OAAA,IACxBA,OAAQ,CAAAC,aAAa,CAACC,WAAW,CAACH,gBAAgB,CAACC,OAAA,EAAS,IAAI;AAE3D,SAASG,SAASC,EAAe,EAAEjkB,QAAgB,EAAU;EAClE,OAAO4jB,gBAAA,CAAiBK,EAAI,EAAAC,gBAAgB,CAAClkB,QAAA;AAC/C;AAEA,MAAMmkB,SAAY,IAAC,OAAO,SAAS,UAAU,OAAO;AACpD,SAASC,mBAAmBC,MAA2B,EAAExS,KAAa,EAAEyS,MAAe,EAAa;EAClG,MAAMplB,MAAA,GAAS,EAAC;EAChBolB,MAAS,GAAAA,MAAA,GAAS,GAAM,GAAAA,MAAA,GAAS,EAAE;EACnC,KAAK,IAAIhrB,CAAA,GAAI,CAAG,EAAAA,CAAA,GAAI,GAAGA,CAAK;IAC1B,MAAMirB,GAAA,GAAMJ,SAAS,CAAC7qB,CAAE;IACxB4F,MAAM,CAACqlB,GAAI,IAAG3rB,UAAW,CAAAyrB,MAAM,CAACxS,KAAQ,SAAM0S,GAAM,GAAAD,MAAA,CAAO,CAAK;EAClE;EACAplB,MAAA,CAAOoQ,KAAK,GAAGpQ,MAAA,CAAOqG,IAAI,GAAGrG,MAAA,CAAOsG,KAAK;EACzCtG,MAAA,CAAOwW,MAAM,GAAGxW,MAAA,CAAOgN,GAAG,GAAGhN,MAAA,CAAOiN,MAAM;EAC1C,OAAOjN,MAAA;AACT;AAEA,MAAMslB,YAAA,GAAeA,CAAC1oB,CAAA,EAAWE,CAAA,EAAW3B,MAC1C,KAAC,CAAAyB,CAAI,QAAKE,CAAA,GAAI,OAAO,CAAC3B,MAAA,IAAU,CAACA,MAAC,CAAuBoqB,UAAU,CAAD;AAEpE;;;;AAIC;AACD,SAASC,kBACPlnB,CAAkC,EAClC8X,MAAyB,EAKvB;EACF,MAAMqP,OAAA,GAAUnnB,CAAC,CAAiBmnB,OAAO;EACzC,MAAMxqB,MAAA,GAAUwqB,OAAA,IAAWA,OAAQ,CAAAlrB,MAAM,GAAGkrB,OAAO,CAAC,CAAE,IAAGnnB,CAAC;EAC1D,MAAM;IAAConB,OAAA;IAASC;EAAA,CAAQ,GAAG1qB,MAAA;EAC3B,IAAI2qB,GAAA,GAAM,KAAK;EACf,IAAIhpB,CAAG,EAAAE,CAAA;EACP,IAAIwoB,YAAa,CAAAI,OAAA,EAASC,OAAS,EAAArnB,CAAA,CAAEnD,MAAM,CAAG;IAC5CyB,CAAI,GAAA8oB,OAAA;IACJ5oB,CAAI,GAAA6oB,OAAA;GACC;IACL,MAAM5N,IAAA,GAAO3B,MAAA,CAAOyP,qBAAqB;IACzCjpB,CAAA,GAAI3B,MAAO,CAAA6qB,OAAO,GAAG/N,IAAA,CAAK1R,IAAI;IAC9BvJ,CAAA,GAAI7B,MAAO,CAAA8qB,OAAO,GAAGhO,IAAA,CAAK/K,GAAG;IAC7B4Y,GAAA,GAAM,IAAI;;EAEZ,OAAO;IAAChpB,CAAA;IAAGE,CAAA;IAAG8oB;EAAG;AACnB;AAEA;;;;;AAKC;AAEM,SAASI,oBACdC,KAAmD,EACnD7X,KAA2B,EACD;EAC1B,IAAI,YAAY6X,KAAO;IACrB,OAAOA,KAAA;;EAGT,MAAM;IAAC7P,MAAA;IAAQH;EAAA,CAAwB,GAAG7H,KAAA;EAC1C,MAAMuE,KAAA,GAAQ+R,gBAAiB,CAAAtO,MAAA;EAC/B,MAAM8P,SAAA,GAAYvT,KAAM,CAAAwT,SAAS,KAAK;EACtC,MAAMC,QAAA,GAAWlB,kBAAA,CAAmBvS,KAAO;EAC3C,MAAM0T,OAAA,GAAUnB,kBAAmB,CAAAvS,KAAA,EAAO,QAAU;EACpD,MAAM;IAAC/V,CAAA;IAAGE,CAAA;IAAG8oB;EAAG,CAAC,GAAGJ,iBAAA,CAAkBS,KAAO,EAAA7P,MAAA;EAC7C,MAAMQ,OAAA,GAAUwP,QAAA,CAAS/f,IAAI,IAAIuf,GAAO,IAAAS,OAAA,CAAQhgB,IAAI,CAAD;EACnD,MAAMwQ,OAAA,GAAUuP,QAAA,CAASpZ,GAAG,IAAI4Y,GAAO,IAAAS,OAAA,CAAQrZ,GAAG,CAAD;EAEjD,IAAI;IAACoD,KAAA;IAAOoG;EAAA,CAAO,GAAGpI,KAAA;EACtB,IAAI8X,SAAW;IACb9V,KAAA,IAASgW,QAAS,CAAAhW,KAAK,GAAGiW,OAAA,CAAQjW,KAAK;IACvCoG,MAAA,IAAU4P,QAAS,CAAA5P,MAAM,GAAG6P,OAAA,CAAQ7P,MAAM;;EAE5C,OAAO;IACL5Z,CAAG,EAAA4B,IAAA,CAAKiB,KAAK,CAAC,CAAC7C,CAAI,GAAAga,OAAM,IAAKxG,KAAA,GAAQgG,MAAO,CAAAhG,KAAK,GAAG6F,uBAAA;IACrDnZ,CAAG,EAAA0B,IAAA,CAAKiB,KAAK,CAAC,CAAC3C,CAAI,GAAA+Z,OAAM,IAAKL,MAAA,GAASJ,MAAO,CAAAI,MAAM,GAAGP,uBAAA;EACzD;AACF;AAEA,SAASqQ,iBAAiBlQ,MAAyB,EAAEhG,KAAa,EAAEoG,MAAc,EAAkB;EAClG,IAAIoE,QAAkB,EAAA2L,SAAA;EAEtB,IAAInW,KAAA,KAAU7X,SAAa,IAAAie,MAAA,KAAWje,SAAW;IAC/C,MAAMiuB,SAAA,GAAYpQ,MAAA,IAAU8N,cAAe,CAAA9N,MAAA;IAC3C,IAAI,CAACoQ,SAAW;MACdpW,KAAA,GAAQgG,MAAA,CAAOqQ,WAAW;MAC1BjQ,MAAA,GAASJ,MAAA,CAAOsQ,YAAY;KACvB;MACL,MAAM3O,IAAO,GAAAyO,SAAA,CAAUX,qBAAqB;MAC5C,MAAMc,cAAA,GAAiBjC,gBAAiB,CAAA8B,SAAA;MACxC,MAAMI,eAAA,GAAkB1B,kBAAmB,CAAAyB,cAAA,EAAgB,QAAU;MACrE,MAAME,gBAAA,GAAmB3B,kBAAA,CAAmByB,cAAgB;MAC5DvW,KAAA,GAAQ2H,IAAA,CAAK3H,KAAK,GAAGyW,gBAAA,CAAiBzW,KAAK,GAAGwW,eAAA,CAAgBxW,KAAK;MACnEoG,MAAA,GAASuB,IAAA,CAAKvB,MAAM,GAAGqQ,gBAAA,CAAiBrQ,MAAM,GAAGoQ,eAAA,CAAgBpQ,MAAM;MACvEoE,QAAA,GAAW0J,aAAc,CAAAqC,cAAA,CAAe/L,QAAQ,EAAE4L,SAAW;MAC7DD,SAAA,GAAYjC,aAAc,CAAAqC,cAAA,CAAeJ,SAAS,EAAEC,SAAW;;;EAGnE,OAAO;IACLpW,KAAA;IACAoG,MAAA;IACAoE,QAAA,EAAUA,QAAY,IAAAjc,QAAA;IACtB4nB,SAAA,EAAWA,SAAa,IAAA5nB;EAC1B;AACF;AAEA,MAAMmoB,MAAA,GAAUnqB,CAAA,IAAc6B,IAAA,CAAKiB,KAAK,CAAC9C,CAAA,GAAI,EAAM;AAEnD;AACO,SAASoqB,eACd3Q,MAAyB,EACzB4Q,OAAgB,EAChBC,QAAiB,EACjBC,WAAoB,EACe;EACnC,MAAMvU,KAAA,GAAQ+R,gBAAiB,CAAAtO,MAAA;EAC/B,MAAM+Q,OAAA,GAAUjC,kBAAA,CAAmBvS,KAAO;EAC1C,MAAMiI,QAAA,GAAW0J,aAAc,CAAA3R,KAAA,CAAMiI,QAAQ,EAAExE,MAAA,EAAQ,aAAkB,KAAAzX,QAAA;EACzE,MAAM4nB,SAAA,GAAYjC,aAAc,CAAA3R,KAAA,CAAM4T,SAAS,EAAEnQ,MAAA,EAAQ,cAAmB,KAAAzX,QAAA;EAC5E,MAAMyoB,aAAA,GAAgBd,gBAAiB,CAAAlQ,MAAA,EAAQ4Q,OAAS,EAAAC,QAAA;EACxD,IAAI;IAAC7W,KAAA;IAAOoG;EAAA,CAAO,GAAG4Q,aAAA;EAEtB,IAAIzU,KAAA,CAAMwT,SAAS,KAAK,aAAe;IACrC,MAAME,OAAA,GAAUnB,kBAAmB,CAAAvS,KAAA,EAAO,QAAU;IACpD,MAAMyT,QAAA,GAAWlB,kBAAA,CAAmBvS,KAAO;IAC3CvC,KAAA,IAASgW,QAAS,CAAAhW,KAAK,GAAGiW,OAAA,CAAQjW,KAAK;IACvCoG,MAAA,IAAU4P,QAAS,CAAA5P,MAAM,GAAG6P,OAAA,CAAQ7P,MAAM;;EAE5CpG,KAAA,GAAQ5R,IAAA,CAAKwC,GAAG,CAAC,CAAG,EAAAoP,KAAA,GAAQ+W,OAAA,CAAQ/W,KAAK;EACzCoG,MAAS,GAAAhY,IAAA,CAAKwC,GAAG,CAAC,GAAGkmB,WAAA,GAAc9W,KAAQ,GAAA8W,WAAA,GAAc1Q,MAAS,GAAA2Q,OAAA,CAAQ3Q,MAAM;EAChFpG,KAAA,GAAQ0W,MAAA,CAAOtoB,IAAK,CAAAuC,GAAG,CAACqP,KAAO,EAAAwK,QAAA,EAAUwM,aAAA,CAAcxM,QAAQ;EAC/DpE,MAAA,GAASsQ,MAAA,CAAOtoB,IAAK,CAAAuC,GAAG,CAACyV,MAAQ,EAAA+P,SAAA,EAAWa,aAAA,CAAcb,SAAS;EACnE,IAAInW,KAAA,IAAS,CAACoG,MAAQ;;;IAGpBA,MAAA,GAASsQ,MAAA,CAAO1W,KAAQ;;EAG1B,MAAMiX,cAAA,GAAiBL,OAAY,KAAAzuB,SAAA,IAAa0uB,QAAa,KAAA1uB,SAAA;EAE7D,IAAI8uB,cAAA,IAAkBH,WAAA,IAAeE,aAAc,CAAA5Q,MAAM,IAAIA,MAAS,GAAA4Q,aAAA,CAAc5Q,MAAM,EAAE;IAC1FA,MAAA,GAAS4Q,aAAA,CAAc5Q,MAAM;IAC7BpG,KAAA,GAAQ0W,MAAO,CAAAtoB,IAAA,CAAKoB,KAAK,CAAC4W,MAAS,GAAA0Q,WAAA;;EAGrC,OAAO;IAAC9W,KAAA;IAAOoG;EAAM;AACvB;AAEA;;;;;;AAMO,SAAS8Q,WACdA,CAAAlZ,KAA2B,EAC3BmZ,UAAkB,EAClBC,UAAoB,EACJ;EAChB,MAAMC,UAAA,GAAaF,UAAc;EACjC,MAAMG,YAAA,GAAelpB,IAAK,CAAAoB,KAAK,CAACwO,KAAA,CAAMoI,MAAM,GAAGiR,UAAA;EAC/C,MAAME,WAAA,GAAcnpB,IAAK,CAAAoB,KAAK,CAACwO,KAAA,CAAMgC,KAAK,GAAGqX,UAAA;EAE5CrZ,KAAA,CAAuBoI,MAAM,GAAGhY,IAAA,CAAKoB,KAAK,CAACwO,KAAA,CAAMoI,MAAM;EACvDpI,KAAA,CAAuBgC,KAAK,GAAG5R,IAAA,CAAKoB,KAAK,CAACwO,KAAA,CAAMgC,KAAK;EAEtD,MAAMgG,MAAA,GAAShI,KAAA,CAAMgI,MAAM;;;;EAK3B,IAAIA,MAAA,CAAOzD,KAAK,KAAK6U,UAAA,IAAe,CAACpR,MAAO,CAAAzD,KAAK,CAAC6D,MAAM,IAAI,CAACJ,MAAA,CAAOzD,KAAK,CAACvC,KAAK,CAAI;IACjFgG,MAAO,CAAAzD,KAAK,CAAC6D,MAAM,GAAG,GAAGpI,KAAM,CAAAoI,MAAM,IAAI;IACzCJ,MAAO,CAAAzD,KAAK,CAACvC,KAAK,GAAG,GAAGhC,KAAM,CAAAgC,KAAK,IAAI;;EAGzC,IAAIhC,KAAA,CAAM6H,uBAAuB,KAAKwR,UAC/B,IAAArR,MAAA,CAAOI,MAAM,KAAKkR,YAClB,IAAAtR,MAAA,CAAOhG,KAAK,KAAKuX,WAAa;IAClCvZ,KAAA,CAAuB6H,uBAAuB,GAAGwR,UAAA;IAClDrR,MAAA,CAAOI,MAAM,GAAGkR,YAAA;IAChBtR,MAAA,CAAOhG,KAAK,GAAGuX,WAAA;IACfvZ,KAAM,CAAA4E,GAAG,CAAC4U,YAAY,CAACH,UAAA,EAAY,CAAG,KAAGA,UAAA,EAAY,CAAG;IACxD,OAAO,IAAI;;EAEb,OAAO,KAAK;AACd;AAEA;;;;;AAKa,MAAAI,4BAAA,GAAgC,YAAW;EACtD,IAAIC,gBAAA,GAAmB,KAAK;EAC5B,IAAI;IACF,MAAMnsB,OAAU;MACd,IAAIosB,OAAUA,CAAA;QACZD,gBAAA,GAAmB,IAAI;QACvB,OAAO,KAAK;MACd;IACF;IAEA,IAAI9D,eAAmB;MACrBze,MAAA,CAAOyiB,gBAAgB,CAAC,MAAQ,MAAI,EAAErsB,OAAA;MACtC4J,MAAA,CAAO0iB,mBAAmB,CAAC,MAAQ,MAAI,EAAEtsB,OAAA;;EAE7C,EAAE,OAAO2C,CAAG;;;EAGZ,OAAOwpB,gBAAA;AACT,CAAK;AAEL;;;;;;;;AAQC;AAEM,SAASI,aACdvD,OAAoB,EACpB7jB,QAA4B,EACR;EACpB,MAAMxI,KAAA,GAAQwsB,QAAA,CAASH,OAAS,EAAA7jB,QAAA;EAChC,MAAMya,OAAU,GAAAjjB,KAAA,IAASA,KAAM,CAAAkjB,KAAK,CAAC;EACrC,OAAOD,OAAA,GAAU,CAACA,OAAO,CAAC,EAAE,GAAGhjB,SAAS;AAC1C;;ACzRA;;;AAGO,SAAS4vB,aAAaC,EAAS,EAAEC,EAAS,EAAE3f,CAAS,EAAE2K,IAAK,EAAE;EACnE,OAAO;IACLzW,CAAG,EAAAwrB,EAAA,CAAGxrB,CAAC,GAAG8L,CAAK,IAAA2f,EAAA,CAAGzrB,CAAC,GAAGwrB,EAAG,CAAAxrB,CAAC,CAAD;IACzBE,CAAG,EAAAsrB,EAAA,CAAGtrB,CAAC,GAAG4L,CAAK,IAAA2f,EAAA,CAAGvrB,CAAC,GAAGsrB,EAAG,CAAAtrB,CAAC;EAC5B;AACF;AAEA;;;AAGO,SAASwrB,sBACdF,EAAS,EACTC,EAAS,EACT3f,CAAS,EAAE2K,IAAkC,EAC7C;EACA,OAAO;IACLzW,CAAG,EAAAwrB,EAAA,CAAGxrB,CAAC,GAAG8L,CAAK,IAAA2f,EAAA,CAAGzrB,CAAC,GAAGwrB,EAAG,CAAAxrB,CAAC,CAAD;IACzBE,CAAG,EAAAuW,IAAA,KAAS,QAAW,GAAA3K,CAAA,GAAI,GAAM,GAAA0f,EAAA,CAAGtrB,CAAC,GAAGurB,EAAG,CAAAvrB,CAAC,GACxCuW,IAAA,KAAS,OAAU,GAAA3K,CAAA,GAAI,IAAI0f,EAAG,CAAAtrB,CAAC,GAAGurB,EAAA,CAAGvrB,CAAC,GACpC4L,CAAI,OAAI2f,EAAG,CAAAvrB,CAAC,GAAGsrB,EAAA,CAAGtrB;EAC1B;AACF;AAEA;;;AAGO,SAASyrB,qBAAqBH,EAAe,EAAEC,EAAe,EAAE3f,CAAS,EAAE2K,IAAK,EAAE;EACvF,MAAMmV,GAAM;IAAC5rB,CAAA,EAAGwrB,EAAA,CAAGtP,IAAI;IAAEhc,CAAA,EAAGsrB,EAAA,CAAGpP;EAAI;EACnC,MAAMyP,GAAM;IAAC7rB,CAAA,EAAGyrB,EAAA,CAAGxP,IAAI;IAAE/b,CAAA,EAAGurB,EAAA,CAAGtP;EAAI;EACnC,MAAM/a,CAAA,GAAImqB,YAAa,CAAAC,EAAA,EAAII,GAAK,EAAA9f,CAAA;EAChC,MAAMzK,CAAA,GAAIkqB,YAAa,CAAAK,GAAA,EAAKC,GAAK,EAAA/f,CAAA;EACjC,MAAMggB,CAAA,GAAIP,YAAa,CAAAM,GAAA,EAAKJ,EAAI,EAAA3f,CAAA;EAChC,MAAMqC,CAAA,GAAIod,YAAa,CAAAnqB,CAAA,EAAGC,CAAG,EAAAyK,CAAA;EAC7B,MAAMpK,CAAA,GAAI6pB,YAAa,CAAAlqB,CAAA,EAAGyqB,CAAG,EAAAhgB,CAAA;EAC7B,OAAOyf,YAAA,CAAapd,CAAA,EAAGzM,CAAG,EAAAoK,CAAA;AAC5B;AChCA,MAAMigB,qBAAwB,YAAAA,CAASC,KAAa,EAAExY,KAAa,EAAc;EAC/E,OAAO;IACLxT,EAAEA,CAAC,EAAE;MACH,OAAOgsB,KAAA,GAAQA,KAAA,GAAQxY,KAAQ,GAAAxT,CAAA;IACjC;IACAisB,SAASlS,CAAC,EAAE;MACVvG,KAAQ,GAAAuG,CAAA;IACV;IACA0C,UAAUnT,KAAK,EAAE;MACf,IAAIA,KAAA,KAAU,QAAU;QACtB,OAAOA,KAAA;;MAET,OAAOA,KAAA,KAAU,OAAU,YAAS,OAAO;IAC7C;IACA4iB,KAAMA,CAAAlsB,CAAC,EAAEtE,KAAK,EAAE;MACd,OAAOsE,CAAI,GAAAtE,KAAA;IACb;IACAywB,UAAWA,CAAAnsB,CAAC,EAAEosB,SAAS,EAAE;MACvB,OAAOpsB,CAAI,GAAAosB,SAAA;IACb;EACF;AACF;AAEA,MAAMC,qBAAA,GAAwB,SAAAA,CAAA,EAAuB;EACnD,OAAO;IACLrsB,EAAEA,CAAC,EAAE;MACH,OAAOA,CAAA;IACT;IACAisB,QAASA,CAAAlS,CAAC,EAAE,EACZ;IACA0C,UAAUnT,KAAK,EAAE;MACf,OAAOA,KAAA;IACT;IACA4iB,KAAMA,CAAAlsB,CAAC,EAAEtE,KAAK,EAAE;MACd,OAAOsE,CAAI,GAAAtE,KAAA;IACb;IACAywB,UAAWA,CAAAnsB,CAAC,EAAEssB,UAAU,EAAE;MACxB,OAAOtsB,CAAA;IACT;EACF;AACF;AAEO,SAASusB,aAAcA,CAAA5iB,GAAY,EAAEqiB,KAAa,EAAExY,KAAa,EAAE;EACxE,OAAO7J,GAAM,GAAAoiB,qBAAA,CAAsBC,KAAO,EAAAxY,KAAA,IAAS6Y,qBAAuB;AAC5E;AAEO,SAASG,sBAAsBpW,GAA6B,EAAEqW,SAAwB,EAAE;EAC7F,IAAI1W,KAA4B,EAAA2W,QAAA;EAChC,IAAID,SAAA,KAAc,KAAS,IAAAA,SAAA,KAAc,KAAO;IAC9C1W,KAAQ,GAAAK,GAAA,CAAIoD,MAAM,CAACzD,KAAK;IACxB2W,QAAW,IACT3W,KAAA,CAAMqS,gBAAgB,CAAC,cACvBrS,KAAA,CAAM4W,mBAAmB,CAAC,aAC3B;IAED5W,KAAM,CAAA6W,WAAW,CAAC,aAAaH,SAAW;IACzCrW,GAAA,CAAiDyW,iBAAiB,GAAGH,QAAA;;AAE1E;AAEO,SAASI,qBAAqB1W,GAA6B,EAAEsW,QAA2B,EAAE;EAC/F,IAAIA,QAAA,KAAa/wB,SAAW;IAC1B,OAAQya,GAAA,CAAiDyW,iBAAiB;IAC1EzW,GAAA,CAAIoD,MAAM,CAACzD,KAAK,CAAC6W,WAAW,CAAC,WAAa,EAAAF,QAAQ,CAAC,EAAE,EAAEA,QAAQ,CAAC,CAAE;;AAEtE;AC/DA,SAASK,UAAWA,CAAA7oB,QAAQ,EAAE;EAC5B,IAAIA,QAAA,KAAa,OAAS;IACxB,OAAO;MACL8oB,OAAS,EAAAvnB,aAAA;MACTwnB,OAAS,EAAA1nB,UAAA;MACT2nB,SAAW,EAAA1nB;IACb;;EAEF,OAAO;IACLwnB,OAAS,EAAA5mB,UAAA;IACT6mB,OAAS,EAAAA,CAAC7rB,CAAG,EAAAC,CAAA,KAAMD,CAAI,GAAAC,CAAA;IACvB6rB,SAAA,EAAWltB,CAAK,IAAAA;EAClB;AACF;AAEA,SAASmtB,gBAAiBA,CAAA;EAACznB,KAAK;EAAEC,GAAG;EAAEuE,KAAK;EAAEgF,IAAI;EAAE6G;AAAK,CAAC,EAAE;EAC1D,OAAO;IACLrQ,KAAA,EAAOA,KAAQ,GAAAwE,KAAA;IACfvE,GAAA,EAAKA,GAAM,GAAAuE,KAAA;IACXgF,IAAA,EAAMA,IAAA,IAAQ,CAACvJ,GAAA,GAAMD,KAAQ,QAAKwE,KAAU;IAC5C6L;EACF;AACF;AAEA,SAASqX,WAAWC,OAAO,EAAEtjB,MAAM,EAAE2I,MAAM,EAAE;EAC3C,MAAM;IAACxO,QAAA;IAAUwB,KAAA,EAAO4nB,UAAA;IAAY3nB,GAAA,EAAK4nB;EAAQ,CAAC,GAAG7a,MAAA;EACrD,MAAM;IAACsa,OAAO;IAAEE;EAAS,CAAC,GAAGH,UAAW,CAAA7oB,QAAA;EACxC,MAAMgG,KAAA,GAAQH,MAAA,CAAOpM,MAAM;EAE3B,IAAI;IAAC+H,KAAK;IAAEC,GAAA;IAAKuJ;EAAA,CAAK,GAAGme,OAAA;EACzB,IAAI7vB,CAAG,EAAAO,IAAA;EAEP,IAAImR,IAAM;IACRxJ,KAAS,IAAAwE,KAAA;IACTvE,GAAO,IAAAuE,KAAA;IACP,KAAK1M,CAAA,GAAI,GAAGO,IAAO,GAAAmM,KAAK,EAAE1M,CAAI,GAAAO,IAAA,EAAM,EAAEP,CAAG;MACvC,IAAI,CAACwvB,OAAQ,CAAAE,SAAA,CAAUnjB,MAAM,CAACrE,KAAQ,GAAAwE,KAAA,CAAM,CAAChG,QAAA,CAAS,CAAG,EAAAopB,UAAA,EAAYC,QAAW;QAC9E;;MAEF7nB,KAAA;MACAC,GAAA;IACF;IACAD,KAAS,IAAAwE,KAAA;IACTvE,GAAO,IAAAuE,KAAA;;EAGT,IAAIvE,GAAA,GAAMD,KAAO;IACfC,GAAO,IAAAuE,KAAA;;EAET,OAAO;IAACxE,KAAA;IAAOC,GAAA;IAAKuJ,IAAA;IAAM6G,KAAA,EAAOsX,OAAA,CAAQtX;EAAK;AAChD;AAgBO,SAASyX,aAAcA,CAAAH,OAAO,EAAEtjB,MAAM,EAAE2I,MAAM,EAAE;EACrD,IAAI,CAACA,MAAQ;IACX,OAAO,CAAC2a,OAAA,CAAQ;;EAGlB,MAAM;IAACnpB,QAAA;IAAUwB,KAAA,EAAO4nB,UAAA;IAAY3nB,GAAA,EAAK4nB;EAAQ,CAAC,GAAG7a,MAAA;EACrD,MAAMxI,KAAA,GAAQH,MAAA,CAAOpM,MAAM;EAC3B,MAAM;IAACsvB,OAAA;IAASD,OAAA;IAASE;EAAS,CAAC,GAAGH,UAAW,CAAA7oB,QAAA;EACjD,MAAM;IAACwB,KAAA;IAAOC,GAAA;IAAKuJ,IAAA;IAAM6G;EAAA,CAAM,GAAGqX,UAAW,CAAAC,OAAA,EAAStjB,MAAQ,EAAA2I,MAAA;EAE9D,MAAMtP,MAAA,GAAS,EAAE;EACjB,IAAIqqB,MAAA,GAAS,KAAK;EAClB,IAAIC,QAAA,GAAW,IAAI;EACnB,IAAIhyB,KAAA,EAAOsP,KAAO,EAAA2iB,SAAA;EAElB,MAAMC,aAAA,GAAgBA,CAAA,KAAMZ,OAAQ,CAAAM,UAAA,EAAYK,SAAA,EAAWjyB,KAAU,KAAAuxB,OAAA,CAAQK,UAAA,EAAYK,SAAe;EACxG,MAAME,WAAA,GAAcA,CAAA,KAAMZ,OAAQ,CAAAM,QAAA,EAAU7xB,KAAA,MAAW,CAAK,IAAAsxB,OAAA,CAAQO,QAAA,EAAUI,SAAW,EAAAjyB,KAAA;EACzF,MAAMoyB,WAAA,GAAcA,CAAA,KAAML,MAAU,IAAAG,aAAA;EACpC,MAAMG,UAAA,GAAaA,CAAA,KAAM,CAACN,MAAU,IAAAI,WAAA;EAEpC,KAAK,IAAIrwB,CAAA,GAAIkI,KAAO,EAAAwhB,IAAA,GAAOxhB,KAAA,EAAOlI,CAAK,IAAAmI,GAAA,EAAK,EAAEnI,CAAG;IAC/CwN,KAAQ,GAAAjB,MAAM,CAACvM,CAAA,GAAI0M,KAAM;IAEzB,IAAIc,KAAA,CAAM2Z,IAAI,EAAE;MACd;;IAGFjpB,KAAQ,GAAAwxB,SAAA,CAAUliB,KAAK,CAAC9G,QAAS;IAEjC,IAAIxI,KAAA,KAAUiyB,SAAW;MACvB;;IAGFF,MAAS,GAAAT,OAAA,CAAQtxB,KAAA,EAAO4xB,UAAY,EAAAC,QAAA;IAEpC,IAAIG,QAAA,KAAa,IAAI,IAAII,WAAe;MACtCJ,QAAA,GAAWT,OAAQ,CAAAvxB,KAAA,EAAO4xB,UAAgB,UAAI9vB,CAAA,GAAI0pB,IAAI;;IAGxD,IAAIwG,QAAA,KAAa,IAAI,IAAIK,UAAc;MACrC3qB,MAAO,CAAA5C,IAAI,CAAC2sB,gBAAiB;QAACznB,KAAO,EAAAgoB,QAAA;QAAU/nB,GAAK,EAAAnI,CAAA;QAAG0R,IAAA;QAAMhF,KAAA;QAAO6L;MAAK;MACzE2X,QAAA,GAAW,IAAI;;IAEjBxG,IAAO,GAAA1pB,CAAA;IACPmwB,SAAY,GAAAjyB,KAAA;EACd;EAEA,IAAIgyB,QAAA,KAAa,IAAI,EAAE;IACrBtqB,MAAO,CAAA5C,IAAI,CAAC2sB,gBAAiB;MAACznB,KAAO,EAAAgoB,QAAA;MAAU/nB,GAAA;MAAKuJ,IAAA;MAAMhF,KAAA;MAAO6L;IAAK;;EAGxE,OAAO3S,MAAA;AACT;AAYO,SAAS4qB,eAAepR,IAAI,EAAElK,MAAM,EAAE;EAC3C,MAAMtP,MAAA,GAAS,EAAE;EACjB,MAAM6qB,QAAA,GAAWrR,IAAA,CAAKqR,QAAQ;EAE9B,KAAK,IAAIzwB,CAAI,MAAGA,CAAA,GAAIywB,QAAS,CAAAtwB,MAAM,EAAEH,CAAK;IACxC,MAAM0wB,GAAA,GAAMV,aAAA,CAAcS,QAAQ,CAACzwB,CAAA,CAAE,EAAEof,IAAA,CAAK7S,MAAM,EAAE2I,MAAA;IACpD,IAAIwb,GAAA,CAAIvwB,MAAM,EAAE;MACdyF,MAAA,CAAO5C,IAAI,CAAI,GAAA0tB,GAAA;;EAEnB;EACA,OAAO9qB,MAAA;AACT;AAKA,SAAS+qB,gBAAgBpkB,MAAM,EAAEG,KAAK,EAAEgF,IAAI,EAAE3E,QAAQ,EAAE;EACtD,IAAI7E,KAAQ;EACZ,IAAIC,GAAA,GAAMuE,KAAQ;EAElB,IAAIgF,IAAA,IAAQ,CAAC3E,QAAU;IAErB,OAAO7E,KAAA,GAAQwE,KAAA,IAAS,CAACH,MAAM,CAACrE,KAAM,EAACif,IAAI,EAAE;MAC3Cjf,KAAA;IACF;;EAIF,OAAOA,KAAA,GAAQwE,KAAS,IAAAH,MAAM,CAACrE,KAAM,EAACif,IAAI,EAAE;IAC1Cjf,KAAA;EACF;EAGAA,KAAS,IAAAwE,KAAA;EAET,IAAIgF,IAAM;IAERvJ,GAAO,IAAAD,KAAA;;EAGT,OAAOC,GAAA,GAAMD,KAAA,IAASqE,MAAM,CAACpE,GAAA,GAAMuE,KAAM,EAACya,IAAI,EAAE;IAC9Chf,GAAA;EACF;EAGAA,GAAO,IAAAuE,KAAA;EAEP,OAAO;IAACxE,KAAA;IAAOC;EAAG;AACpB;AASA,SAASyoB,cAAcrkB,MAAM,EAAErE,KAAK,EAAEtB,GAAG,EAAE8K,IAAI,EAAE;EAC/C,MAAMhF,KAAA,GAAQH,MAAA,CAAOpM,MAAM;EAC3B,MAAMyF,MAAA,GAAS,EAAE;EACjB,IAAIwD,IAAO,GAAAlB,KAAA;EACX,IAAIwhB,IAAA,GAAOnd,MAAM,CAACrE,KAAM;EACxB,IAAIC,GAAA;EAEJ,KAAKA,GAAA,GAAMD,KAAQ,MAAGC,GAAO,IAAAvB,GAAA,EAAK,EAAEuB,GAAK;IACvC,MAAM0oB,GAAM,GAAAtkB,MAAM,CAACpE,GAAA,GAAMuE,KAAM;IAC/B,IAAImkB,GAAI,CAAA1J,IAAI,IAAI0J,GAAA,CAAIC,IAAI,EAAE;MACxB,IAAI,CAACpH,IAAK,CAAAvC,IAAI,EAAE;QACdzV,IAAA,GAAO,KAAK;QACZ9L,MAAA,CAAO5C,IAAI,CAAC;UAACkF,KAAA,EAAOA,KAAQ,GAAAwE,KAAA;UAAOvE,GAAA,EAAK,CAACA,GAAM,QAAKuE,KAAA;UAAOgF;QAAI;QAE/DxJ,KAAA,GAAQkB,IAAO,GAAAynB,GAAA,CAAIC,IAAI,GAAG3oB,GAAA,GAAM,IAAI;;KAEjC;MACLiB,IAAO,GAAAjB,GAAA;MACP,IAAIuhB,IAAA,CAAKvC,IAAI,EAAE;QACbjf,KAAQ,GAAAC,GAAA;;;IAGZuhB,IAAO,GAAAmH,GAAA;EACT;EAEA,IAAIznB,IAAA,KAAS,IAAI,EAAE;IACjBxD,MAAA,CAAO5C,IAAI,CAAC;MAACkF,KAAA,EAAOA,KAAQ,GAAAwE,KAAA;MAAOvE,GAAA,EAAKiB,IAAO,GAAAsD,KAAA;MAAOgF;IAAI;;EAG5D,OAAO9L,MAAA;AACT;AAUO,SAASmrB,iBAAiB3R,IAAI,EAAE4R,cAAc,EAAE;EACrD,MAAMzkB,MAAA,GAAS6S,IAAA,CAAK7S,MAAM;EAC1B,MAAMQ,QAAW,GAAAqS,IAAA,CAAK7d,OAAO,CAACwL,QAAQ;EACtC,MAAML,KAAA,GAAQH,MAAA,CAAOpM,MAAM;EAE3B,IAAI,CAACuM,KAAO;IACV,OAAO,EAAE;;EAGX,MAAMgF,IAAO,IAAC,CAAC0N,IAAA,CAAK6R,KAAK;EACzB,MAAM;IAAC/oB,KAAA;IAAOC;EAAA,CAAI,GAAGwoB,eAAA,CAAgBpkB,MAAQ,EAAAG,KAAA,EAAOgF,IAAM,EAAA3E,QAAA;EAE1D,IAAIA,QAAA,KAAa,IAAI,EAAE;IACrB,OAAOmkB,aAAA,CAAc9R,IAAM,GAAC;MAAClX,KAAA;MAAOC,GAAA;MAAKuJ;IAAI,EAAE,EAAEnF,MAAQ,EAAAykB,cAAA;;EAG3D,MAAMpqB,GAAM,GAAAuB,GAAA,GAAMD,KAAQ,GAAAC,GAAA,GAAMuE,KAAA,GAAQvE,GAAG;EAC3C,MAAMgpB,YAAA,GAAe,CAAC,CAAC/R,IAAA,CAAKgS,SAAS,IAAIlpB,KAAA,KAAU,CAAK,IAAAC,GAAA,KAAQuE,KAAQ;EACxE,OAAOwkB,aAAA,CAAc9R,IAAM,EAAAwR,aAAA,CAAcrkB,MAAA,EAAQrE,KAAO,EAAAtB,GAAA,EAAKuqB,YAAA,GAAe5kB,MAAQ,EAAAykB,cAAA;AACtF;AAQA,SAASE,cAAc9R,IAAI,EAAEqR,QAAQ,EAAElkB,MAAM,EAAEykB,cAAc,EAAE;EAC7D,IAAI,CAACA,cAAkB,KAACA,cAAA,CAAenM,UAAU,IAAI,CAACtY,MAAQ;IAC5D,OAAOkkB,QAAA;;EAET,OAAOY,eAAA,CAAgBjS,IAAM,EAAAqR,QAAA,EAAUlkB,MAAQ,EAAAykB,cAAA;AACjD;AASA,SAASK,gBAAgBjS,IAAI,EAAEqR,QAAQ,EAAElkB,MAAM,EAAEykB,cAAc,EAAE;EAC/D,MAAMM,YAAe,GAAAlS,IAAA,CAAKmS,MAAM,CAACtV,UAAU;EAC3C,MAAMuV,SAAA,GAAYC,SAAU,CAAArS,IAAA,CAAK7d,OAAO;EACxC,MAAM;IAACmwB,aAAe,EAAAhxB,YAAA;IAAca,OAAA,EAAS;MAACwL;IAAQ;EAAC,CAAC,GAAGqS,IAAA;EAC3D,MAAM1S,KAAA,GAAQH,MAAA,CAAOpM,MAAM;EAC3B,MAAMyF,MAAA,GAAS,EAAE;EACjB,IAAI+rB,SAAY,GAAAH,SAAA;EAChB,IAAItpB,KAAQ,GAAAuoB,QAAQ,CAAC,EAAE,CAACvoB,KAAK;EAC7B,IAAIlI,CAAI,GAAAkI,KAAA;EAER,SAAS0pB,SAASvpB,CAAC,EAAEnE,CAAC,EAAE2tB,CAAC,EAAEC,EAAE,EAAE;IAC7B,MAAMC,GAAM,GAAAhlB,QAAA,GAAW,CAAC,IAAI,CAAC;IAC7B,IAAI1E,CAAA,KAAMnE,CAAG;MACX;;IAGFmE,CAAK,IAAAqE,KAAA;IACL,OAAOH,MAAM,CAAClE,CAAA,GAAIqE,KAAM,EAACya,IAAI,EAAE;MAC7B9e,CAAK,IAAA0pB,GAAA;IACP;IACA,OAAOxlB,MAAM,CAACrI,CAAA,GAAIwI,KAAM,EAACya,IAAI,EAAE;MAC7BjjB,CAAK,IAAA6tB,GAAA;IACP;IACA,IAAI1pB,CAAA,GAAIqE,KAAU,KAAAxI,CAAA,GAAIwI,KAAO;MAC3B9G,MAAA,CAAO5C,IAAI,CAAC;QAACkF,KAAA,EAAOG,CAAI,GAAAqE,KAAA;QAAOvE,GAAA,EAAKjE,CAAI,GAAAwI,KAAA;QAAOgF,IAAM,EAAAmgB,CAAA;QAAGtZ,KAAO,EAAAuZ;MAAE;MACjEH,SAAY,GAAAG,EAAA;MACZ5pB,KAAA,GAAQhE,CAAI,GAAAwI,KAAA;;EAEhB;EAEA,KAAK,MAAMmjB,OAAA,IAAWY,QAAU;IAC9BvoB,KAAQ,GAAA6E,QAAA,GAAW7E,KAAQ,GAAA2nB,OAAA,CAAQ3nB,KAAK;IACxC,IAAIwhB,IAAO,GAAAnd,MAAM,CAACrE,KAAA,GAAQwE,KAAM;IAChC,IAAI6L,KAAA;IACJ,KAAKvY,CAAA,GAAIkI,KAAQ,MAAGlI,CAAA,IAAK6vB,OAAQ,CAAA1nB,GAAG,EAAEnI,CAAK;MACzC,MAAMkpB,EAAK,GAAA3c,MAAM,CAACvM,CAAA,GAAI0M,KAAM;MAC5B6L,KAAA,GAAQkZ,SAAU,CAAAT,cAAA,CAAenM,UAAU,CAAClC,aAAA,CAAc2O,YAAc;QACtEhzB,IAAM;QACN0zB,EAAI,EAAAtI,IAAA;QACJsE,EAAI,EAAA9E,EAAA;QACJ+I,WAAA,EAAa,CAACjyB,CAAI,QAAK0M,KAAA;QACvBwlB,WAAA,EAAalyB,CAAI,GAAA0M,KAAA;QACjBhM;MACF;MACA,IAAIyxB,YAAA,CAAa5Z,KAAA,EAAOoZ,SAAY;QAClCC,QAAA,CAAS1pB,KAAO,EAAAlI,CAAA,GAAI,CAAG,EAAA6vB,OAAA,CAAQne,IAAI,EAAEigB,SAAA;;MAEvCjI,IAAO,GAAAR,EAAA;MACPyI,SAAY,GAAApZ,KAAA;IACd;IACA,IAAIrQ,KAAA,GAAQlI,CAAA,GAAI,CAAG;MACjB4xB,QAAA,CAAS1pB,KAAO,EAAAlI,CAAA,GAAI,CAAG,EAAA6vB,OAAA,CAAQne,IAAI,EAAEigB,SAAA;;EAEzC;EAEA,OAAO/rB,MAAA;AACT;AAEA,SAAS6rB,UAAUlwB,OAAO,EAAE;EAC1B,OAAO;IACLqW,eAAA,EAAiBrW,OAAA,CAAQqW,eAAe;IACxCwa,cAAA,EAAgB7wB,OAAA,CAAQ6wB,cAAc;IACtCC,UAAA,EAAY9wB,OAAA,CAAQ8wB,UAAU;IAC9BC,gBAAA,EAAkB/wB,OAAA,CAAQ+wB,gBAAgB;IAC1CC,eAAA,EAAiBhxB,OAAA,CAAQgxB,eAAe;IACxC1U,WAAA,EAAatc,OAAA,CAAQsc,WAAW;IAChChG,WAAA,EAAatW,OAAA,CAAQsW;EACvB;AACF;AAEA,SAASsa,YAAaA,CAAA5Z,KAAK,EAAEoZ,SAAS,EAAE;EACtC,IAAI,CAACA,SAAW;IACd,OAAO,KAAK;;EAEd,MAAMzW,KAAA,GAAQ,EAAE;EAChB,MAAMsX,QAAW,YAAAA,CAASpxB,GAAG,EAAElD,KAAK,EAAE;IACpC,IAAI,CAAC2S,mBAAA,CAAoB3S,KAAQ;MAC/B,OAAOA,KAAA;;IAET,IAAI,CAACgd,KAAA,CAAMtG,QAAQ,CAAC1W,KAAQ;MAC1Bgd,KAAA,CAAMlY,IAAI,CAAC9E,KAAA;;IAEb,OAAOgd,KAAA,CAAM7Z,OAAO,CAACnD,KAAA;EACvB;EACA,OAAOiV,IAAA,CAAKC,SAAS,CAACmF,KAAA,EAAOia,QAAA,MAAcrf,IAAK,CAAAC,SAAS,CAACue,SAAW,EAAAa,QAAA;AACvE;ACzWA,SAASC,eAAe/Y,KAAY,EAAEgZ,SAAoB,EAAEC,KAAsB,EAAE;EAClF,OAAOjZ,KAAA,CAAMnY,OAAO,CAAC4T,IAAI,GAAGuE,KAAK,CAACiZ,KAAM,IAAGD,SAAS,CAACC,KAAM;AAC7D;AAEA,SAASC,cAAeA,CAAAtmB,IAAe,EAAEomB,SAAoB,EAAQ;EACnE,MAAM;IAAC/kB,MAAA;IAAQC;EAAA,CAAO,GAAGtB,IAAA;EACzB,IAAIqB,MAAA,IAAUC,MAAQ;IACpB,OAAO;MACL3B,IAAM,EAAAwmB,cAAA,CAAe9kB,MAAA,EAAQ+kB,SAAW;MACxCxmB,KAAO,EAAAumB,cAAA,CAAe9kB,MAAA,EAAQ+kB,SAAW;MACzC9f,GAAK,EAAA6f,cAAA,CAAe7kB,MAAA,EAAQ8kB,SAAW;MACvC7f,MAAQ,EAAA4f,cAAA,CAAe7kB,MAAA,EAAQ8kB,SAAW;IAC5C;;EAEF,OAAOA,SAAA;AACT;AAEO,SAASG,mBAAmB7e,KAAY,EAAE1H,IAAe,EAAgB;EAC9E,MAAM6I,IAAA,GAAO7I,IAAA,CAAKwmB,KAAK;EACvB,IAAI3d,IAAA,CAAK4d,QAAQ,EAAE;IACjB,OAAO,KAAK;;EAEd,MAAM/U,IAAO,GAAA4U,cAAA,CAAetmB,IAAM,EAAA0H,KAAA,CAAM0e,SAAS;EAEjD,OAAO;IACLzmB,IAAA,EAAMkJ,IAAA,CAAKlJ,IAAI,KAAK,KAAK,GAAG,IAAI+R,IAAA,CAAK/R,IAAI,IAAIkJ,IAAK,CAAAlJ,IAAI,KAAK,IAAI,GAAG,IAAIkJ,IAAK,CAAAlJ,IAAI,CAAC;IAChFC,KAAO,EAAAiJ,IAAA,CAAKjJ,KAAK,KAAK,KAAK,GAAG8H,KAAM,CAAAgC,KAAK,GAAGgI,IAAK,CAAA9R,KAAK,IAAIiJ,IAAA,CAAKjJ,KAAK,KAAK,IAAI,GAAG,CAAI,GAAAiJ,IAAA,CAAKjJ,KAAI,CAAE;IAC/F0G,GAAA,EAAKuC,IAAA,CAAKvC,GAAG,KAAK,KAAK,GAAG,IAAIoL,IAAA,CAAKpL,GAAG,IAAIuC,IAAK,CAAAvC,GAAG,KAAK,IAAI,GAAG,IAAIuC,IAAK,CAAAvC,GAAG,CAAC;IAC3EC,MAAQ,EAAAsC,IAAA,CAAKtC,MAAM,KAAK,KAAK,GAAGmB,KAAM,CAAAoI,MAAM,GAAG4B,IAAK,CAAAnL,MAAM,IAAIsC,IAAA,CAAKtC,MAAM,KAAK,IAAI,GAAG,CAAI,GAAAsC,IAAA,CAAKtC,MAAK;EACrG;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}