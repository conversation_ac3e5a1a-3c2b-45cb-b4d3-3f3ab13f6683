import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Chip,
  LinearProgress,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Button
} from '@mui/material';
import {
  TrendingUp,
  People,
  School,
  VideoLibrary,
  WorkspacePremium,
  AttachMoney,
  PersonAdd,
  Payment,
  Visibility,
  MoreVert
} from '@mui/icons-material';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
} from 'chart.js';
import axios from 'axios';

// تسجيل مكونات Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

const StatCard = ({ title, value, icon, color, subtitle, trend }) => (
  <Card sx={{ height: '100%' }}>
    <CardContent>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box>
          <Typography color="textSecondary" gutterBottom variant="h6">
            {title}
          </Typography>
          <Typography variant="h4" component="h2" color={color}>
            {value}
          </Typography>
          {subtitle && (
            <Typography color="textSecondary" variant="body2">
              {subtitle}
            </Typography>
          )}
          {trend && (
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              <TrendingUp color="success" fontSize="small" />
              <Typography color="success.main" variant="body2" sx={{ ml: 0.5 }}>
                +{trend}% من الشهر الماضي
              </Typography>
            </Box>
          )}
        </Box>
        <Avatar sx={{ bgcolor: color, width: 56, height: 56 }}>
          {icon}
        </Avatar>
      </Box>
    </CardContent>
  </Card>
);

const DashboardAdvanced = () => {
  const [stats, setStats] = useState({});
  const [analytics, setAnalytics] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const [statsResponse, analyticsResponse] = await Promise.all([
        axios.get('/admin/dashboard-stats'),
        axios.get('/admin/analytics')
      ]);
      
      setStats(statsResponse.data);
      setAnalytics(analyticsResponse.data);
    } catch (error) {
      console.error('خطأ في جلب بيانات لوحة التحكم:', error);
    } finally {
      setLoading(false);
    }
  };

  // إعدادات الرسوم البيانية
  const enrollmentChartData = {
    labels: analytics.enrollments?.labels || [],
    datasets: [
      {
        label: 'التسجيلات',
        data: analytics.enrollments?.data || [],
        borderColor: 'rgb(33, 150, 243)',
        backgroundColor: 'rgba(33, 150, 243, 0.1)',
        tension: 0.4
      }
    ]
  };

  const revenueChartData = {
    labels: analytics.revenue?.labels || [],
    datasets: [
      {
        label: 'الإيرادات (ريال)',
        data: analytics.revenue?.data || [],
        backgroundColor: 'rgba(76, 175, 80, 0.8)',
        borderColor: 'rgb(76, 175, 80)',
        borderWidth: 1
      }
    ]
  };

  const progressChartData = {
    labels: ['مكتمل', 'قيد التقدم', 'لم يبدأ'],
    datasets: [
      {
        data: [
          analytics.studentProgress?.completed || 0,
          analytics.studentProgress?.inProgress || 0,
          analytics.studentProgress?.notStarted || 0
        ],
        backgroundColor: [
          'rgba(76, 175, 80, 0.8)',
          'rgba(255, 193, 7, 0.8)',
          'rgba(158, 158, 158, 0.8)'
        ],
        borderColor: [
          'rgb(76, 175, 80)',
          'rgb(255, 193, 7)',
          'rgb(158, 158, 158)'
        ],
        borderWidth: 1
      }
    ]
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top'
      }
    },
    scales: {
      y: {
        beginAtZero: true
      }
    }
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <LinearProgress />
        <Typography sx={{ mt: 2 }}>جاري تحميل بيانات لوحة التحكم...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        لوحة التحكم
      </Typography>

      {/* إحصائيات سريعة */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="إجمالي الطلاب"
            value={stats.totalStudents}
            icon={<People />}
            color="primary.main"
            subtitle={`${stats.activeStudents} نشط`}
            trend="12"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="إجمالي الكورسات"
            value={stats.totalCourses}
            icon={<School />}
            color="success.main"
            subtitle={`${stats.activeCourses} نشط`}
            trend="8"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="إجمالي الفيديوهات"
            value={stats.totalVideos}
            icon={<VideoLibrary />}
            color="info.main"
            subtitle="عبر جميع الكورسات"
            trend="15"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="إجمالي الإيرادات"
            value={`${stats.totalRevenue?.toLocaleString()} ريال`}
            icon={<AttachMoney />}
            color="warning.main"
            subtitle={`${stats.monthlyRevenue?.toLocaleString()} هذا الشهر`}
            trend="25"
          />
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* الرسوم البيانية */}
        <Grid item xs={12} md={8}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                التسجيلات الأسبوعية
              </Typography>
              <Line data={enrollmentChartData} options={chartOptions} />
            </CardContent>
          </Card>

          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                الإيرادات الأسبوعية
              </Typography>
              <Bar data={revenueChartData} options={chartOptions} />
            </CardContent>
          </Card>
        </Grid>

        {/* الأنشطة الحديثة وتقدم الطلاب */}
        <Grid item xs={12} md={4}>
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                الأنشطة الحديثة
              </Typography>
              <List>
                {stats.recentActivity?.map((activity) => (
                  <ListItem key={activity.id} sx={{ px: 0 }}>
                    <ListItemAvatar>
                      <Avatar sx={{ bgcolor: 'primary.main' }}>
                        {activity.icon === 'person_add' && <PersonAdd />}
                        {activity.icon === 'school' && <School />}
                        {activity.icon === 'workspace_premium' && <WorkspacePremium />}
                        {activity.icon === 'payment' && <Payment />}
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={activity.title}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {activity.description}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {activity.time}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>

          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                تقدم الطلاب
              </Typography>
              <Box sx={{ height: 300 }}>
                <Doughnut 
                  data={progressChartData} 
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: 'bottom'
                      }
                    }
                  }} 
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* أفضل الكورسات */}
      <Card sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            أفضل الكورسات أداءً
          </Typography>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>اسم الكورس</TableCell>
                  <TableCell align="center">التسجيلات</TableCell>
                  <TableCell align="center">الإيرادات</TableCell>
                  <TableCell align="center">الإجراءات</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {analytics.topCourses?.map((course, index) => (
                  <TableRow key={index}>
                    <TableCell>{course.name}</TableCell>
                    <TableCell align="center">
                      <Chip label={course.enrollments} color="primary" size="small" />
                    </TableCell>
                    <TableCell align="center">
                      {course.revenue.toLocaleString()} ريال
                    </TableCell>
                    <TableCell align="center">
                      <IconButton size="small">
                        <Visibility />
                      </IconButton>
                      <IconButton size="small">
                        <MoreVert />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default DashboardAdvanced;
