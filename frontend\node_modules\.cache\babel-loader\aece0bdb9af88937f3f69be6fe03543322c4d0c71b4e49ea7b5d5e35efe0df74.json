{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\\\u0643\\u0648\\u0633\\u0627\\u062A\\\\frontend\\\\src\\\\components\\\\admin\\\\VideoManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, TextField, Box, Typography, List, ListItem, ListItemText, ListItemSecondaryAction, IconButton, Divider, Alert, Chip } from '@mui/material';\nimport { Add, Delete, PlayArrow, Edit, Save, Cancel } from '@mui/icons-material';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst VideoManagement = ({\n  open,\n  onClose,\n  course,\n  onUpdateCourse\n}) => {\n  _s();\n  const [videos, setVideos] = useState((course === null || course === void 0 ? void 0 : course.videos) || []);\n  const [newVideo, setNewVideo] = useState({\n    title: '',\n    duration: '',\n    url: ''\n  });\n  const [editingIndex, setEditingIndex] = useState(-1);\n  const [editingVideo, setEditingVideo] = useState({});\n  const [alert, setAlert] = useState({\n    show: false,\n    message: '',\n    severity: 'success'\n  });\n  const showAlert = (message, severity = 'success') => {\n    setAlert({\n      show: true,\n      message,\n      severity\n    });\n    setTimeout(() => setAlert({\n      show: false,\n      message: '',\n      severity: 'success'\n    }), 3000);\n  };\n  const handleAddVideo = () => {\n    if (!newVideo.title || !newVideo.duration) {\n      showAlert('يرجى إدخال عنوان الفيديو والمدة', 'error');\n      return;\n    }\n    const video = {\n      id: videos.length + 1,\n      title: newVideo.title,\n      duration: newVideo.duration,\n      url: newVideo.url || `https://example.com/video${videos.length + 1}`\n    };\n    const updatedVideos = [...videos, video];\n    setVideos(updatedVideos);\n    setNewVideo({\n      title: '',\n      duration: '',\n      url: ''\n    });\n    showAlert('تم إضافة الفيديو بنجاح');\n  };\n  const handleDeleteVideo = index => {\n    if (window.confirm('هل أنت متأكد من حذف هذا الفيديو؟')) {\n      const updatedVideos = videos.filter((_, i) => i !== index);\n      setVideos(updatedVideos);\n      showAlert('تم حذف الفيديو بنجاح');\n    }\n  };\n  const handleEditVideo = index => {\n    setEditingIndex(index);\n    setEditingVideo({\n      ...videos[index]\n    });\n  };\n  const handleSaveEdit = () => {\n    if (!editingVideo.title || !editingVideo.duration) {\n      showAlert('يرجى إدخال عنوان الفيديو والمدة', 'error');\n      return;\n    }\n    const updatedVideos = videos.map((video, index) => index === editingIndex ? editingVideo : video);\n    setVideos(updatedVideos);\n    setEditingIndex(-1);\n    setEditingVideo({});\n    showAlert('تم تحديث الفيديو بنجاح');\n  };\n  const handleCancelEdit = () => {\n    setEditingIndex(-1);\n    setEditingVideo({});\n  };\n  const handleSave = async () => {\n    try {\n      // تحديث الكورس مع الفيديوهات الجديدة\n      const updatedCourse = {\n        ...course,\n        videos: videos\n      };\n\n      // محاولة إرسال للخادم\n      // await axios.put(`/admin/courses/${course._id}`, updatedCourse);\n\n      // تحديث الكورس في المكون الأب\n      onUpdateCourse(updatedCourse);\n      toast.success('تم حفظ الفيديوهات بنجاح');\n      onClose();\n    } catch (error) {\n      console.error('خطأ في حفظ الفيديوهات:', error);\n      toast.error('حدث خطأ في حفظ الفيديوهات');\n    }\n  };\n  const formatDuration = duration => {\n    // تنسيق المدة (مثال: \"10:30\" أو \"1:05:30\")\n    if (duration.includes(':')) {\n      return duration;\n    }\n    // إذا كانت المدة بالدقائق فقط\n    return `${duration}:00`;\n  };\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: onClose,\n    maxWidth: \"md\",\n    fullWidth: true,\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(PlayArrow, {\n          sx: {\n            color: '#1976d2'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633: \", course === null || course === void 0 ? void 0 : course.title]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          pt: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 4,\n            p: 3,\n            bgcolor: '#f5f5f5',\n            borderRadius: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              mb: 2,\n              fontWeight: 'bold'\n            },\n            children: \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0641\\u064A\\u062F\\u064A\\u0648 \\u062C\\u062F\\u064A\\u062F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              gap: 2,\n              mb: 2,\n              flexWrap: 'wrap'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              label: \"\\u0639\\u0646\\u0648\\u0627\\u0646 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\",\n              value: newVideo.title,\n              onChange: e => setNewVideo({\n                ...newVideo,\n                title: e.target.value\n              }),\n              sx: {\n                flex: 1,\n                minWidth: 200\n              },\n              placeholder: \"\\u0645\\u062B\\u0627\\u0644: \\u0645\\u0642\\u062F\\u0645\\u0629 \\u0641\\u064A \\u0627\\u0644\\u062A\\u0633\\u0648\\u064A\\u0642 \\u0627\\u0644\\u0631\\u0642\\u0645\\u064A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              label: \"\\u0645\\u062F\\u0629 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\",\n              value: newVideo.duration,\n              onChange: e => setNewVideo({\n                ...newVideo,\n                duration: e.target.value\n              }),\n              sx: {\n                width: 120\n              },\n              placeholder: \"10:30\",\n              helperText: \"\\u0628\\u0627\\u0644\\u062F\\u0642\\u0627\\u0626\\u0642:\\u0627\\u0644\\u062B\\u0648\\u0627\\u0646\\u064A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            fullWidth: true,\n            label: \"\\u0631\\u0627\\u0628\\u0637 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648 (\\u0627\\u062E\\u062A\\u064A\\u0627\\u0631\\u064A)\",\n            value: newVideo.url,\n            onChange: e => setNewVideo({\n              ...newVideo,\n              url: e.target.value\n            }),\n            sx: {\n              mb: 2\n            },\n            placeholder: \"https://youtube.com/watch?v=...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            startIcon: /*#__PURE__*/_jsxDEV(Add, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 26\n            }, this),\n            onClick: handleAddVideo,\n            sx: {\n              borderRadius: 2\n            },\n            children: \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 3\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            mb: 2,\n            fontWeight: 'bold'\n          },\n          children: [\"\\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633 (\", videos.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this), videos.length === 0 ? /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A \\u0641\\u064A \\u0647\\u0630\\u0627 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633 \\u0628\\u0639\\u062F. \\u0627\\u0628\\u062F\\u0623 \\u0628\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0641\\u064A\\u062F\\u064A\\u0648 \\u062C\\u062F\\u064A\\u062F!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(List, {\n          sx: {\n            bgcolor: 'background.paper',\n            borderRadius: 2\n          },\n          children: videos.map((video, index) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(ListItem, {\n              sx: {\n                py: 2\n              },\n              children: editingIndex === index ?\n              /*#__PURE__*/\n              // وضع التعديل\n              _jsxDEV(Box, {\n                sx: {\n                  width: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    gap: 2,\n                    mb: 2,\n                    flexWrap: 'wrap'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TextField, {\n                    label: \"\\u0639\\u0646\\u0648\\u0627\\u0646 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\",\n                    value: editingVideo.title,\n                    onChange: e => setEditingVideo({\n                      ...editingVideo,\n                      title: e.target.value\n                    }),\n                    sx: {\n                      flex: 1,\n                      minWidth: 200\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                    label: \"\\u0645\\u062F\\u0629 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\",\n                    value: editingVideo.duration,\n                    onChange: e => setEditingVideo({\n                      ...editingVideo,\n                      duration: e.target.value\n                    }),\n                    sx: {\n                      width: 120\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 210,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(TextField, {\n                  fullWidth: true,\n                  label: \"\\u0631\\u0627\\u0628\\u0637 \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\",\n                  value: editingVideo.url || '',\n                  onChange: e => setEditingVideo({\n                    ...editingVideo,\n                    url: e.target.value\n                  }),\n                  sx: {\n                    mb: 2\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    gap: 1\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    size: \"small\",\n                    variant: \"contained\",\n                    startIcon: /*#__PURE__*/_jsxDEV(Save, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 230,\n                      columnNumber: 40\n                    }, this),\n                    onClick: handleSaveEdit,\n                    children: \"\\u062D\\u0641\\u0638\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 227,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"small\",\n                    variant: \"outlined\",\n                    startIcon: /*#__PURE__*/_jsxDEV(Cancel, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 238,\n                      columnNumber: 40\n                    }, this),\n                    onClick: handleCancelEdit,\n                    children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 23\n              }, this) :\n              /*#__PURE__*/\n              // وضع العرض\n              _jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    mr: 2\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(PlayArrow, {\n                    sx: {\n                      color: '#1976d2',\n                      mr: 1\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"h6\",\n                    sx: {\n                      color: '#1976d2'\n                    },\n                    children: index + 1\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body1\",\n                    sx: {\n                      fontWeight: 500\n                    },\n                    children: video.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 257,\n                    columnNumber: 29\n                  }, this),\n                  secondary: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      gap: 2,\n                      mt: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Chip, {\n                      label: formatDuration(video.duration),\n                      size: \"small\",\n                      color: \"primary\",\n                      variant: \"outlined\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 263,\n                      columnNumber: 31\n                    }, this), video.url && /*#__PURE__*/_jsxDEV(Chip, {\n                      label: \"\\u064A\\u062D\\u062A\\u0648\\u064A \\u0639\\u0644\\u0649 \\u0631\\u0627\\u0628\\u0637\",\n                      size: \"small\",\n                      color: \"success\",\n                      variant: \"outlined\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 270,\n                      columnNumber: 33\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 262,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(ListItemSecondaryAction, {\n                  children: /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      gap: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleEditVideo(index),\n                      sx: {\n                        color: '#1976d2'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Edit, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 288,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 283,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => handleDeleteVideo(index),\n                      sx: {\n                        color: '#d32f2f'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Delete, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 295,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 290,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 19\n            }, this), index < videos.length - 1 && /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 49\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 13\n        }, this), videos.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3,\n            p: 2,\n            bgcolor: '#e3f2fd',\n            borderRadius: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 'bold',\n              mb: 1\n            },\n            children: \"\\u0645\\u0644\\u062E\\u0635 \\u0627\\u0644\\u0643\\u0648\\u0631\\u0633:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"\\u2022 \\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0641\\u064A\\u062F\\u064A\\u0648\\u0647\\u0627\\u062A: \", videos.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: [\"\\u2022 \\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u062F\\u0629: \", videos.reduce((total, video) => {\n              const [minutes, seconds] = video.duration.split(':').map(Number);\n              return total + minutes + (seconds || 0) / 60;\n            }, 0).toFixed(0), \" \\u062F\\u0642\\u064A\\u0642\\u0629 \\u062A\\u0642\\u0631\\u064A\\u0628\\u0627\\u064B\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        children: \"\\u0625\\u0644\\u063A\\u0627\\u0621\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleSave,\n        variant: \"contained\",\n        startIcon: /*#__PURE__*/_jsxDEV(Save, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 69\n        }, this),\n        children: \"\\u062D\\u0641\\u0638 \\u0627\\u0644\\u062A\\u063A\\u064A\\u064A\\u0631\\u0627\\u062A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 129,\n    columnNumber: 5\n  }, this);\n};\n_s(VideoManagement, \"NTUFtT7aP25aaUOZNKwlIfPNmdI=\");\n_c = VideoManagement;\nexport default VideoManagement;\nvar _c;\n$RefreshReg$(_c, \"VideoManagement\");", "map": {"version": 3, "names": ["React", "useState", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "TextField", "Box", "Typography", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "IconButton", "Divider", "<PERSON><PERSON>", "Chip", "Add", "Delete", "PlayArrow", "Edit", "Save", "Cancel", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VideoManagement", "open", "onClose", "course", "onUpdateCourse", "_s", "videos", "setVideos", "newVideo", "setNewVideo", "title", "duration", "url", "editingIndex", "setEditingIndex", "editingVideo", "setEditingVideo", "alert", "<PERSON><PERSON><PERSON><PERSON>", "show", "message", "severity", "show<PERSON><PERSON><PERSON>", "setTimeout", "handleAddVideo", "video", "id", "length", "updatedVideos", "handleDeleteVideo", "index", "window", "confirm", "filter", "_", "i", "handleEditVideo", "handleSaveEdit", "map", "handleCancelEdit", "handleSave", "updatedCourse", "toast", "success", "error", "console", "formatDuration", "includes", "max<PERSON><PERSON><PERSON>", "fullWidth", "children", "sx", "display", "alignItems", "gap", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "pt", "mb", "p", "bgcolor", "borderRadius", "variant", "fontWeight", "flexWrap", "label", "value", "onChange", "e", "target", "flex", "min<PERSON><PERSON><PERSON>", "placeholder", "width", "helperText", "startIcon", "onClick", "my", "mt", "py", "size", "mr", "primary", "secondary", "reduce", "total", "minutes", "seconds", "split", "Number", "toFixed", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/كوسات/frontend/src/components/admin/VideoManagement.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  TextField,\n  Box,\n  Typography,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n  IconButton,\n  Divider,\n  Alert,\n  Chip\n} from '@mui/material';\nimport {\n  Add,\n  Delete,\n  PlayArrow,\n  Edit,\n  Save,\n  Cancel\n} from '@mui/icons-material';\n\nconst VideoManagement = ({ open, onClose, course, onUpdateCourse }) => {\n  const [videos, setVideos] = useState(course?.videos || []);\n  const [newVideo, setNewVideo] = useState({\n    title: '',\n    duration: '',\n    url: ''\n  });\n  const [editingIndex, setEditingIndex] = useState(-1);\n  const [editingVideo, setEditingVideo] = useState({});\n  const [alert, setAlert] = useState({ show: false, message: '', severity: 'success' });\n\n  const showAlert = (message, severity = 'success') => {\n    setAlert({ show: true, message, severity });\n    setTimeout(() => setAlert({ show: false, message: '', severity: 'success' }), 3000);\n  };\n\n  const handleAddVideo = () => {\n    if (!newVideo.title || !newVideo.duration) {\n      showAlert('يرجى إدخال عنوان الفيديو والمدة', 'error');\n      return;\n    }\n\n    const video = {\n      id: videos.length + 1,\n      title: newVideo.title,\n      duration: newVideo.duration,\n      url: newVideo.url || `https://example.com/video${videos.length + 1}`\n    };\n\n    const updatedVideos = [...videos, video];\n    setVideos(updatedVideos);\n    setNewVideo({ title: '', duration: '', url: '' });\n    showAlert('تم إضافة الفيديو بنجاح');\n  };\n\n  const handleDeleteVideo = (index) => {\n    if (window.confirm('هل أنت متأكد من حذف هذا الفيديو؟')) {\n      const updatedVideos = videos.filter((_, i) => i !== index);\n      setVideos(updatedVideos);\n      showAlert('تم حذف الفيديو بنجاح');\n    }\n  };\n\n  const handleEditVideo = (index) => {\n    setEditingIndex(index);\n    setEditingVideo({ ...videos[index] });\n  };\n\n  const handleSaveEdit = () => {\n    if (!editingVideo.title || !editingVideo.duration) {\n      showAlert('يرجى إدخال عنوان الفيديو والمدة', 'error');\n      return;\n    }\n\n    const updatedVideos = videos.map((video, index) =>\n      index === editingIndex ? editingVideo : video\n    );\n    setVideos(updatedVideos);\n    setEditingIndex(-1);\n    setEditingVideo({});\n    showAlert('تم تحديث الفيديو بنجاح');\n  };\n\n  const handleCancelEdit = () => {\n    setEditingIndex(-1);\n    setEditingVideo({});\n  };\n\n  const handleSave = async () => {\n    try {\n      // تحديث الكورس مع الفيديوهات الجديدة\n      const updatedCourse = {\n        ...course,\n        videos: videos\n      };\n\n      // محاولة إرسال للخادم\n      // await axios.put(`/admin/courses/${course._id}`, updatedCourse);\n      \n      // تحديث الكورس في المكون الأب\n      onUpdateCourse(updatedCourse);\n      \n      toast.success('تم حفظ الفيديوهات بنجاح');\n      onClose();\n    } catch (error) {\n      console.error('خطأ في حفظ الفيديوهات:', error);\n      toast.error('حدث خطأ في حفظ الفيديوهات');\n    }\n  };\n\n  const formatDuration = (duration) => {\n    // تنسيق المدة (مثال: \"10:30\" أو \"1:05:30\")\n    if (duration.includes(':')) {\n      return duration;\n    }\n    // إذا كانت المدة بالدقائق فقط\n    return `${duration}:00`;\n  };\n\n  return (\n    <Dialog open={open} onClose={onClose} maxWidth=\"md\" fullWidth>\n      <DialogTitle>\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <PlayArrow sx={{ color: '#1976d2' }} />\n          إدارة فيديوهات الكورس: {course?.title}\n        </Box>\n      </DialogTitle>\n      \n      <DialogContent>\n        <Box sx={{ pt: 2 }}>\n          {/* إضافة فيديو جديد */}\n          <Box sx={{ mb: 4, p: 3, bgcolor: '#f5f5f5', borderRadius: 2 }}>\n            <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 'bold' }}>\n              إضافة فيديو جديد\n            </Typography>\n            \n            <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>\n              <TextField\n                label=\"عنوان الفيديو\"\n                value={newVideo.title}\n                onChange={(e) => setNewVideo({ ...newVideo, title: e.target.value })}\n                sx={{ flex: 1, minWidth: 200 }}\n                placeholder=\"مثال: مقدمة في التسويق الرقمي\"\n              />\n              \n              <TextField\n                label=\"مدة الفيديو\"\n                value={newVideo.duration}\n                onChange={(e) => setNewVideo({ ...newVideo, duration: e.target.value })}\n                sx={{ width: 120 }}\n                placeholder=\"10:30\"\n                helperText=\"بالدقائق:الثواني\"\n              />\n            </Box>\n            \n            <TextField\n              fullWidth\n              label=\"رابط الفيديو (اختياري)\"\n              value={newVideo.url}\n              onChange={(e) => setNewVideo({ ...newVideo, url: e.target.value })}\n              sx={{ mb: 2 }}\n              placeholder=\"https://youtube.com/watch?v=...\"\n            />\n            \n            <Button\n              variant=\"contained\"\n              startIcon={<Add />}\n              onClick={handleAddVideo}\n              sx={{ borderRadius: 2 }}\n            >\n              إضافة الفيديو\n            </Button>\n          </Box>\n\n          <Divider sx={{ my: 3 }} />\n\n          {/* قائمة الفيديوهات */}\n          <Typography variant=\"h6\" sx={{ mb: 2, fontWeight: 'bold' }}>\n            فيديوهات الكورس ({videos.length})\n          </Typography>\n\n          {videos.length === 0 ? (\n            <Alert severity=\"info\" sx={{ mt: 2 }}>\n              لا توجد فيديوهات في هذا الكورس بعد. ابدأ بإضافة فيديو جديد!\n            </Alert>\n          ) : (\n            <List sx={{ bgcolor: 'background.paper', borderRadius: 2 }}>\n              {videos.map((video, index) => (\n                <React.Fragment key={index}>\n                  <ListItem sx={{ py: 2 }}>\n                    {editingIndex === index ? (\n                      // وضع التعديل\n                      <Box sx={{ width: '100%' }}>\n                        <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>\n                          <TextField\n                            label=\"عنوان الفيديو\"\n                            value={editingVideo.title}\n                            onChange={(e) => setEditingVideo({ ...editingVideo, title: e.target.value })}\n                            sx={{ flex: 1, minWidth: 200 }}\n                          />\n                          \n                          <TextField\n                            label=\"مدة الفيديو\"\n                            value={editingVideo.duration}\n                            onChange={(e) => setEditingVideo({ ...editingVideo, duration: e.target.value })}\n                            sx={{ width: 120 }}\n                          />\n                        </Box>\n                        \n                        <TextField\n                          fullWidth\n                          label=\"رابط الفيديو\"\n                          value={editingVideo.url || ''}\n                          onChange={(e) => setEditingVideo({ ...editingVideo, url: e.target.value })}\n                          sx={{ mb: 2 }}\n                        />\n                        \n                        <Box sx={{ display: 'flex', gap: 1 }}>\n                          <Button\n                            size=\"small\"\n                            variant=\"contained\"\n                            startIcon={<Save />}\n                            onClick={handleSaveEdit}\n                          >\n                            حفظ\n                          </Button>\n                          <Button\n                            size=\"small\"\n                            variant=\"outlined\"\n                            startIcon={<Cancel />}\n                            onClick={handleCancelEdit}\n                          >\n                            إلغاء\n                          </Button>\n                        </Box>\n                      </Box>\n                    ) : (\n                      // وضع العرض\n                      <>\n                        <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>\n                          <PlayArrow sx={{ color: '#1976d2', mr: 1 }} />\n                          <Typography variant=\"h6\" sx={{ color: '#1976d2' }}>\n                            {index + 1}\n                          </Typography>\n                        </Box>\n                        \n                        <ListItemText\n                          primary={\n                            <Typography variant=\"body1\" sx={{ fontWeight: 500 }}>\n                              {video.title}\n                            </Typography>\n                          }\n                          secondary={\n                            <Box sx={{ display: 'flex', gap: 2, mt: 1 }}>\n                              <Chip\n                                label={formatDuration(video.duration)}\n                                size=\"small\"\n                                color=\"primary\"\n                                variant=\"outlined\"\n                              />\n                              {video.url && (\n                                <Chip\n                                  label=\"يحتوي على رابط\"\n                                  size=\"small\"\n                                  color=\"success\"\n                                  variant=\"outlined\"\n                                />\n                              )}\n                            </Box>\n                          }\n                        />\n                        \n                        <ListItemSecondaryAction>\n                          <Box sx={{ display: 'flex', gap: 1 }}>\n                            <IconButton\n                              size=\"small\"\n                              onClick={() => handleEditVideo(index)}\n                              sx={{ color: '#1976d2' }}\n                            >\n                              <Edit />\n                            </IconButton>\n                            <IconButton\n                              size=\"small\"\n                              onClick={() => handleDeleteVideo(index)}\n                              sx={{ color: '#d32f2f' }}\n                            >\n                              <Delete />\n                            </IconButton>\n                          </Box>\n                        </ListItemSecondaryAction>\n                      </>\n                    )}\n                  </ListItem>\n                  {index < videos.length - 1 && <Divider />}\n                </React.Fragment>\n              ))}\n            </List>\n          )}\n\n          {videos.length > 0 && (\n            <Box sx={{ mt: 3, p: 2, bgcolor: '#e3f2fd', borderRadius: 2 }}>\n              <Typography variant=\"body2\" sx={{ fontWeight: 'bold', mb: 1 }}>\n                ملخص الكورس:\n              </Typography>\n              <Typography variant=\"body2\">\n                • إجمالي الفيديوهات: {videos.length}\n              </Typography>\n              <Typography variant=\"body2\">\n                • إجمالي المدة: {videos.reduce((total, video) => {\n                  const [minutes, seconds] = video.duration.split(':').map(Number);\n                  return total + minutes + (seconds || 0) / 60;\n                }, 0).toFixed(0)} دقيقة تقريباً\n              </Typography>\n            </Box>\n          )}\n        </Box>\n      </DialogContent>\n      \n      <DialogActions>\n        <Button onClick={onClose}>إلغاء</Button>\n        <Button onClick={handleSave} variant=\"contained\" startIcon={<Save />}>\n          حفظ التغييرات\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default VideoManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,SAAS,EACTC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,uBAAuB,EACvBC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,IAAI,QACC,eAAe;AACtB,SACEC,GAAG,EACHC,MAAM,EACNC,SAAS,EACTC,IAAI,EACJC,IAAI,EACJC,MAAM,QACD,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7B,MAAMC,eAAe,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC,MAAM;EAAEC;AAAe,CAAC,KAAK;EAAAC,EAAA;EACrE,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGlC,QAAQ,CAAC,CAAA8B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEG,MAAM,KAAI,EAAE,CAAC;EAC1D,MAAM,CAACE,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC;IACvCqC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,GAAG,EAAE;EACP,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC0C,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC4C,KAAK,EAAEC,QAAQ,CAAC,GAAG7C,QAAQ,CAAC;IAAE8C,IAAI,EAAE,KAAK;IAAEC,OAAO,EAAE,EAAE;IAAEC,QAAQ,EAAE;EAAU,CAAC,CAAC;EAErF,MAAMC,SAAS,GAAGA,CAACF,OAAO,EAAEC,QAAQ,GAAG,SAAS,KAAK;IACnDH,QAAQ,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO;MAAEC;IAAS,CAAC,CAAC;IAC3CE,UAAU,CAAC,MAAML,QAAQ,CAAC;MAAEC,IAAI,EAAE,KAAK;MAAEC,OAAO,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAU,CAAC,CAAC,EAAE,IAAI,CAAC;EACrF,CAAC;EAED,MAAMG,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAAChB,QAAQ,CAACE,KAAK,IAAI,CAACF,QAAQ,CAACG,QAAQ,EAAE;MACzCW,SAAS,CAAC,iCAAiC,EAAE,OAAO,CAAC;MACrD;IACF;IAEA,MAAMG,KAAK,GAAG;MACZC,EAAE,EAAEpB,MAAM,CAACqB,MAAM,GAAG,CAAC;MACrBjB,KAAK,EAAEF,QAAQ,CAACE,KAAK;MACrBC,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;MAC3BC,GAAG,EAAEJ,QAAQ,CAACI,GAAG,IAAI,4BAA4BN,MAAM,CAACqB,MAAM,GAAG,CAAC;IACpE,CAAC;IAED,MAAMC,aAAa,GAAG,CAAC,GAAGtB,MAAM,EAAEmB,KAAK,CAAC;IACxClB,SAAS,CAACqB,aAAa,CAAC;IACxBnB,WAAW,CAAC;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE,EAAE;MAAEC,GAAG,EAAE;IAAG,CAAC,CAAC;IACjDU,SAAS,CAAC,wBAAwB,CAAC;EACrC,CAAC;EAED,MAAMO,iBAAiB,GAAIC,KAAK,IAAK;IACnC,IAAIC,MAAM,CAACC,OAAO,CAAC,kCAAkC,CAAC,EAAE;MACtD,MAAMJ,aAAa,GAAGtB,MAAM,CAAC2B,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKL,KAAK,CAAC;MAC1DvB,SAAS,CAACqB,aAAa,CAAC;MACxBN,SAAS,CAAC,sBAAsB,CAAC;IACnC;EACF,CAAC;EAED,MAAMc,eAAe,GAAIN,KAAK,IAAK;IACjChB,eAAe,CAACgB,KAAK,CAAC;IACtBd,eAAe,CAAC;MAAE,GAAGV,MAAM,CAACwB,KAAK;IAAE,CAAC,CAAC;EACvC,CAAC;EAED,MAAMO,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACtB,YAAY,CAACL,KAAK,IAAI,CAACK,YAAY,CAACJ,QAAQ,EAAE;MACjDW,SAAS,CAAC,iCAAiC,EAAE,OAAO,CAAC;MACrD;IACF;IAEA,MAAMM,aAAa,GAAGtB,MAAM,CAACgC,GAAG,CAAC,CAACb,KAAK,EAAEK,KAAK,KAC5CA,KAAK,KAAKjB,YAAY,GAAGE,YAAY,GAAGU,KAC1C,CAAC;IACDlB,SAAS,CAACqB,aAAa,CAAC;IACxBd,eAAe,CAAC,CAAC,CAAC,CAAC;IACnBE,eAAe,CAAC,CAAC,CAAC,CAAC;IACnBM,SAAS,CAAC,wBAAwB,CAAC;EACrC,CAAC;EAED,MAAMiB,gBAAgB,GAAGA,CAAA,KAAM;IAC7BzB,eAAe,CAAC,CAAC,CAAC,CAAC;IACnBE,eAAe,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC;EAED,MAAMwB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF;MACA,MAAMC,aAAa,GAAG;QACpB,GAAGtC,MAAM;QACTG,MAAM,EAAEA;MACV,CAAC;;MAED;MACA;;MAEA;MACAF,cAAc,CAACqC,aAAa,CAAC;MAE7BC,KAAK,CAACC,OAAO,CAAC,yBAAyB,CAAC;MACxCzC,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAO0C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9CF,KAAK,CAACE,KAAK,CAAC,2BAA2B,CAAC;IAC1C;EACF,CAAC;EAED,MAAME,cAAc,GAAInC,QAAQ,IAAK;IACnC;IACA,IAAIA,QAAQ,CAACoC,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC1B,OAAOpC,QAAQ;IACjB;IACA;IACA,OAAO,GAAGA,QAAQ,KAAK;EACzB,CAAC;EAED,oBACEd,OAAA,CAACvB,MAAM;IAAC2B,IAAI,EAAEA,IAAK;IAACC,OAAO,EAAEA,OAAQ;IAAC8C,QAAQ,EAAC,IAAI;IAACC,SAAS;IAAAC,QAAA,gBAC3DrD,OAAA,CAACtB,WAAW;MAAA2E,QAAA,eACVrD,OAAA,CAACjB,GAAG;QAACuE,EAAE,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBACzDrD,OAAA,CAACL,SAAS;UAAC2D,EAAE,EAAE;YAAEI,KAAK,EAAE;UAAU;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,0HAChB,EAACxD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEO,KAAK;MAAA;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACK,CAAC,eAEd9D,OAAA,CAACrB,aAAa;MAAA0E,QAAA,eACZrD,OAAA,CAACjB,GAAG;QAACuE,EAAE,EAAE;UAAES,EAAE,EAAE;QAAE,CAAE;QAAAV,QAAA,gBAEjBrD,OAAA,CAACjB,GAAG;UAACuE,EAAE,EAAE;YAAEU,EAAE,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;YAAEC,OAAO,EAAE,SAAS;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAAd,QAAA,gBAC5DrD,OAAA,CAAChB,UAAU;YAACoF,OAAO,EAAC,IAAI;YAACd,EAAE,EAAE;cAAEU,EAAE,EAAE,CAAC;cAAEK,UAAU,EAAE;YAAO,CAAE;YAAAhB,QAAA,EAAC;UAE5D;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEb9D,OAAA,CAACjB,GAAG;YAACuE,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEE,GAAG,EAAE,CAAC;cAAEO,EAAE,EAAE,CAAC;cAAEM,QAAQ,EAAE;YAAO,CAAE;YAAAjB,QAAA,gBAC5DrD,OAAA,CAAClB,SAAS;cACRyF,KAAK,EAAC,2EAAe;cACrBC,KAAK,EAAE7D,QAAQ,CAACE,KAAM;cACtB4D,QAAQ,EAAGC,CAAC,IAAK9D,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEE,KAAK,EAAE6D,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACrElB,EAAE,EAAE;gBAAEsB,IAAI,EAAE,CAAC;gBAAEC,QAAQ,EAAE;cAAI,CAAE;cAC/BC,WAAW,EAAC;YAA+B;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eAEF9D,OAAA,CAAClB,SAAS;cACRyF,KAAK,EAAC,+DAAa;cACnBC,KAAK,EAAE7D,QAAQ,CAACG,QAAS;cACzB2D,QAAQ,EAAGC,CAAC,IAAK9D,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEG,QAAQ,EAAE4D,CAAC,CAACC,MAAM,CAACH;cAAM,CAAC,CAAE;cACxElB,EAAE,EAAE;gBAAEyB,KAAK,EAAE;cAAI,CAAE;cACnBD,WAAW,EAAC,OAAO;cACnBE,UAAU,EAAC;YAAkB;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN9D,OAAA,CAAClB,SAAS;YACRsE,SAAS;YACTmB,KAAK,EAAC,kHAAwB;YAC9BC,KAAK,EAAE7D,QAAQ,CAACI,GAAI;YACpB0D,QAAQ,EAAGC,CAAC,IAAK9D,WAAW,CAAC;cAAE,GAAGD,QAAQ;cAAEI,GAAG,EAAE2D,CAAC,CAACC,MAAM,CAACH;YAAM,CAAC,CAAE;YACnElB,EAAE,EAAE;cAAEU,EAAE,EAAE;YAAE,CAAE;YACdc,WAAW,EAAC;UAAiC;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,eAEF9D,OAAA,CAACnB,MAAM;YACLuF,OAAO,EAAC,WAAW;YACnBa,SAAS,eAAEjF,OAAA,CAACP,GAAG;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnBoB,OAAO,EAAEvD,cAAe;YACxB2B,EAAE,EAAE;cAAEa,YAAY,EAAE;YAAE,CAAE;YAAAd,QAAA,EACzB;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEN9D,OAAA,CAACV,OAAO;UAACgE,EAAE,EAAE;YAAE6B,EAAE,EAAE;UAAE;QAAE;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAG1B9D,OAAA,CAAChB,UAAU;UAACoF,OAAO,EAAC,IAAI;UAACd,EAAE,EAAE;YAAEU,EAAE,EAAE,CAAC;YAAEK,UAAU,EAAE;UAAO,CAAE;UAAAhB,QAAA,GAAC,yFACzC,EAAC5C,MAAM,CAACqB,MAAM,EAAC,GAClC;QAAA;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EAEZrD,MAAM,CAACqB,MAAM,KAAK,CAAC,gBAClB9B,OAAA,CAACT,KAAK;UAACiC,QAAQ,EAAC,MAAM;UAAC8B,EAAE,EAAE;YAAE8B,EAAE,EAAE;UAAE,CAAE;UAAA/B,QAAA,EAAC;QAEtC;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,gBAER9D,OAAA,CAACf,IAAI;UAACqE,EAAE,EAAE;YAAEY,OAAO,EAAE,kBAAkB;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAAd,QAAA,EACxD5C,MAAM,CAACgC,GAAG,CAAC,CAACb,KAAK,EAAEK,KAAK,kBACvBjC,OAAA,CAACzB,KAAK,CAAC0B,QAAQ;YAAAoD,QAAA,gBACbrD,OAAA,CAACd,QAAQ;cAACoE,EAAE,EAAE;gBAAE+B,EAAE,EAAE;cAAE,CAAE;cAAAhC,QAAA,EACrBrC,YAAY,KAAKiB,KAAK;cAAA;cACrB;cACAjC,OAAA,CAACjB,GAAG;gBAACuE,EAAE,EAAE;kBAAEyB,KAAK,EAAE;gBAAO,CAAE;gBAAA1B,QAAA,gBACzBrD,OAAA,CAACjB,GAAG;kBAACuE,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,GAAG,EAAE,CAAC;oBAAEO,EAAE,EAAE,CAAC;oBAAEM,QAAQ,EAAE;kBAAO,CAAE;kBAAAjB,QAAA,gBAC5DrD,OAAA,CAAClB,SAAS;oBACRyF,KAAK,EAAC,2EAAe;oBACrBC,KAAK,EAAEtD,YAAY,CAACL,KAAM;oBAC1B4D,QAAQ,EAAGC,CAAC,IAAKvD,eAAe,CAAC;sBAAE,GAAGD,YAAY;sBAAEL,KAAK,EAAE6D,CAAC,CAACC,MAAM,CAACH;oBAAM,CAAC,CAAE;oBAC7ElB,EAAE,EAAE;sBAAEsB,IAAI,EAAE,CAAC;sBAAEC,QAAQ,EAAE;oBAAI;kBAAE;oBAAAlB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC,CAAC,eAEF9D,OAAA,CAAClB,SAAS;oBACRyF,KAAK,EAAC,+DAAa;oBACnBC,KAAK,EAAEtD,YAAY,CAACJ,QAAS;oBAC7B2D,QAAQ,EAAGC,CAAC,IAAKvD,eAAe,CAAC;sBAAE,GAAGD,YAAY;sBAAEJ,QAAQ,EAAE4D,CAAC,CAACC,MAAM,CAACH;oBAAM,CAAC,CAAE;oBAChFlB,EAAE,EAAE;sBAAEyB,KAAK,EAAE;oBAAI;kBAAE;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAEN9D,OAAA,CAAClB,SAAS;kBACRsE,SAAS;kBACTmB,KAAK,EAAC,qEAAc;kBACpBC,KAAK,EAAEtD,YAAY,CAACH,GAAG,IAAI,EAAG;kBAC9B0D,QAAQ,EAAGC,CAAC,IAAKvD,eAAe,CAAC;oBAAE,GAAGD,YAAY;oBAAEH,GAAG,EAAE2D,CAAC,CAACC,MAAM,CAACH;kBAAM,CAAC,CAAE;kBAC3ElB,EAAE,EAAE;oBAAEU,EAAE,EAAE;kBAAE;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC,eAEF9D,OAAA,CAACjB,GAAG;kBAACuE,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEE,GAAG,EAAE;kBAAE,CAAE;kBAAAJ,QAAA,gBACnCrD,OAAA,CAACnB,MAAM;oBACLyG,IAAI,EAAC,OAAO;oBACZlB,OAAO,EAAC,WAAW;oBACnBa,SAAS,eAAEjF,OAAA,CAACH,IAAI;sBAAA8D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACpBoB,OAAO,EAAE1C,cAAe;oBAAAa,QAAA,EACzB;kBAED;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT9D,OAAA,CAACnB,MAAM;oBACLyG,IAAI,EAAC,OAAO;oBACZlB,OAAO,EAAC,UAAU;oBAClBa,SAAS,eAAEjF,OAAA,CAACF,MAAM;sBAAA6D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACtBoB,OAAO,EAAExC,gBAAiB;oBAAAW,QAAA,EAC3B;kBAED;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;cAAA;cAEN;cACA9D,OAAA,CAAAE,SAAA;gBAAAmD,QAAA,gBACErD,OAAA,CAACjB,GAAG;kBAACuE,EAAE,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEC,UAAU,EAAE,QAAQ;oBAAE+B,EAAE,EAAE;kBAAE,CAAE;kBAAAlC,QAAA,gBACxDrD,OAAA,CAACL,SAAS;oBAAC2D,EAAE,EAAE;sBAAEI,KAAK,EAAE,SAAS;sBAAE6B,EAAE,EAAE;oBAAE;kBAAE;oBAAA5B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9C9D,OAAA,CAAChB,UAAU;oBAACoF,OAAO,EAAC,IAAI;oBAACd,EAAE,EAAE;sBAAEI,KAAK,EAAE;oBAAU,CAAE;oBAAAL,QAAA,EAC/CpB,KAAK,GAAG;kBAAC;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC,eAEN9D,OAAA,CAACb,YAAY;kBACXqG,OAAO,eACLxF,OAAA,CAAChB,UAAU;oBAACoF,OAAO,EAAC,OAAO;oBAACd,EAAE,EAAE;sBAAEe,UAAU,EAAE;oBAAI,CAAE;oBAAAhB,QAAA,EACjDzB,KAAK,CAACf;kBAAK;oBAAA8C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CACb;kBACD2B,SAAS,eACPzF,OAAA,CAACjB,GAAG;oBAACuE,EAAE,EAAE;sBAAEC,OAAO,EAAE,MAAM;sBAAEE,GAAG,EAAE,CAAC;sBAAE2B,EAAE,EAAE;oBAAE,CAAE;oBAAA/B,QAAA,gBAC1CrD,OAAA,CAACR,IAAI;sBACH+E,KAAK,EAAEtB,cAAc,CAACrB,KAAK,CAACd,QAAQ,CAAE;sBACtCwE,IAAI,EAAC,OAAO;sBACZ5B,KAAK,EAAC,SAAS;sBACfU,OAAO,EAAC;oBAAU;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CAAC,EACDlC,KAAK,CAACb,GAAG,iBACRf,OAAA,CAACR,IAAI;sBACH+E,KAAK,EAAC,4EAAgB;sBACtBe,IAAI,EAAC,OAAO;sBACZ5B,KAAK,EAAC,SAAS;sBACfU,OAAO,EAAC;oBAAU;sBAAAT,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnB,CACF;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eAEF9D,OAAA,CAACZ,uBAAuB;kBAAAiE,QAAA,eACtBrD,OAAA,CAACjB,GAAG;oBAACuE,EAAE,EAAE;sBAAEC,OAAO,EAAE,MAAM;sBAAEE,GAAG,EAAE;oBAAE,CAAE;oBAAAJ,QAAA,gBACnCrD,OAAA,CAACX,UAAU;sBACTiG,IAAI,EAAC,OAAO;sBACZJ,OAAO,EAAEA,CAAA,KAAM3C,eAAe,CAACN,KAAK,CAAE;sBACtCqB,EAAE,EAAE;wBAAEI,KAAK,EAAE;sBAAU,CAAE;sBAAAL,QAAA,eAEzBrD,OAAA,CAACJ,IAAI;wBAAA+D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eACb9D,OAAA,CAACX,UAAU;sBACTiG,IAAI,EAAC,OAAO;sBACZJ,OAAO,EAAEA,CAAA,KAAMlD,iBAAiB,CAACC,KAAK,CAAE;sBACxCqB,EAAE,EAAE;wBAAEI,KAAK,EAAE;sBAAU,CAAE;sBAAAL,QAAA,eAEzBrD,OAAA,CAACN,MAAM;wBAAAiE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACiB,CAAC;cAAA,eAC1B;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,EACV7B,KAAK,GAAGxB,MAAM,CAACqB,MAAM,GAAG,CAAC,iBAAI9B,OAAA,CAACV,OAAO;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,GAzGtB7B,KAAK;YAAA0B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA0GV,CACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACP,EAEArD,MAAM,CAACqB,MAAM,GAAG,CAAC,iBAChB9B,OAAA,CAACjB,GAAG;UAACuE,EAAE,EAAE;YAAE8B,EAAE,EAAE,CAAC;YAAEnB,CAAC,EAAE,CAAC;YAAEC,OAAO,EAAE,SAAS;YAAEC,YAAY,EAAE;UAAE,CAAE;UAAAd,QAAA,gBAC5DrD,OAAA,CAAChB,UAAU;YAACoF,OAAO,EAAC,OAAO;YAACd,EAAE,EAAE;cAAEe,UAAU,EAAE,MAAM;cAAEL,EAAE,EAAE;YAAE,CAAE;YAAAX,QAAA,EAAC;UAE/D;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9D,OAAA,CAAChB,UAAU;YAACoF,OAAO,EAAC,OAAO;YAAAf,QAAA,GAAC,4GACL,EAAC5C,MAAM,CAACqB,MAAM;UAAA;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eACb9D,OAAA,CAAChB,UAAU;YAACoF,OAAO,EAAC,OAAO;YAAAf,QAAA,GAAC,8EACV,EAAC5C,MAAM,CAACiF,MAAM,CAAC,CAACC,KAAK,EAAE/D,KAAK,KAAK;cAC/C,MAAM,CAACgE,OAAO,EAAEC,OAAO,CAAC,GAAGjE,KAAK,CAACd,QAAQ,CAACgF,KAAK,CAAC,GAAG,CAAC,CAACrD,GAAG,CAACsD,MAAM,CAAC;cAChE,OAAOJ,KAAK,GAAGC,OAAO,GAAG,CAACC,OAAO,IAAI,CAAC,IAAI,EAAE;YAC9C,CAAC,EAAE,CAAC,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,EAAC,4EACnB;UAAA;YAAArC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAEhB9D,OAAA,CAACpB,aAAa;MAAAyE,QAAA,gBACZrD,OAAA,CAACnB,MAAM;QAACqG,OAAO,EAAE7E,OAAQ;QAAAgD,QAAA,EAAC;MAAK;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACxC9D,OAAA,CAACnB,MAAM;QAACqG,OAAO,EAAEvC,UAAW;QAACyB,OAAO,EAAC,WAAW;QAACa,SAAS,eAAEjF,OAAA,CAACH,IAAI;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAT,QAAA,EAAC;MAEtE;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACtD,EAAA,CAlTIL,eAAe;AAAA8F,EAAA,GAAf9F,eAAe;AAoTrB,eAAeA,eAAe;AAAC,IAAA8F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}